# bolt.diy Expert Agent

Author: [<PERSON>](https://www.youtube.com/@ColeMedin)

The bolt.diy Expert Agent is a specialized n8n AI agent designed to assist users with questions and issues related to bolt.diy - the official open source bolt.new that enables users to choose from multiple LLM providers for web development.

## Purpose

This agent serves as a knowledgeable assistant that:
- Answers questions about bolt.diy's features and capabilities
- Helps troubleshoot installation and setup issues
- Provides guidance on using different LLM providers
- Assists with understanding bolt.diy's architecture and components
- Offers support for contributing to the project

## Knowledge Base

The agent's responses are based on the official bolt.diy documentation, which includes:
- Installation and setup instructions
- Available LLM integrations
- Feature documentation
- Contribution guidelines
- Project roadmap
- Frequently asked questions

## How to Use

Simply ask any question related to bolt.diy, and the agent will search through the documentation to provide accurate and helpful responses. The agent can assist with:

1. Setup and Installation
2. LLM Provider Configuration
3. Feature Usage
4. Troubleshooting
5. Contributing to bolt.diy
6. Understanding the Project Roadmap

## Examples

Here are some example questions you can ask:
- "How do I install bolt.diy?"
- "Which LLM providers are supported?"
- "How can I contribute to the project?"
- "What's the process for adding a new LLM provider?"
- "How do I set up my API keys?"

## Limitations

The agent's knowledge is based on the current documentation. For the most up-to-date information, always refer to:
- [bolt.diy Documentation](https://stackblitz-labs.github.io/bolt.diy/)
- [bolt.diy Community](https://thinktank.ottomator.ai)
- [GitHub Repository](https://github.com/stackblitz-labs/bolt.diy)

## Support

If you need additional support or have questions that the agent cannot answer, please:
1. Join the bolt.diy community at https://thinktank.ottomator.ai
2. Create an issue on the GitHub repository
3. Check the FAQ section in the documentation

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.
