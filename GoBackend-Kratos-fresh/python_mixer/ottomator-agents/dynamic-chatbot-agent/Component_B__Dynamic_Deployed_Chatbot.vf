{"version": {"_version": 12, "autoSaveFromRestore": false, "canvasTemplates": [], "components": [], "creatorID": 1479280, "manualSave": false, "model": {"modelID": "6785a0a480deee0fe5a745d7"}, "name": "Initial Version", "platformData": {"slots": [{"key": "67859fb20cfcf7818a1338f2", "name": "Email", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}}, {"key": "6785a03f0cfcf7818a13394b", "name": "PIN", "color": "#DC8879", "inputs": [], "type": {"value": "VF.NUMBER"}}, {"key": "6785bc920cfcf7818a13406c", "name": "second_email_attempt", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}}, {"key": "6785c44a0cfcf7818a1342c3", "name": "Name", "color": "#d58493", "inputs": [""], "type": {"value": "VF.NAME"}}], "intents": [{"key": "None", "name": "None", "slots": [], "inputs": [], "noteID": null}], "settings": {"restart": true, "repeat": 100, "locales": ["en-US"], "globalNoMatch": {"type": "static", "prompt": {"id": "cm1729z6g01ya3b7xmgnv5yop", "content": [{"children": [{"text": "Sorry, I didn’t get that. Please try again."}]}]}}, "messageDelay": {"durationMilliseconds": 500}, "defaultVoice": "Alexa"}, "publishing": {"avatar": "https://cm4-production-assets.s3.amazonaws.com/1737400670373-ottomator-logo.png", "color": "#191C33", "description": "I truly appreciate you using my agent. Thank you!", "feedback": false, "image": "https://cm4-production-assets.s3.amazonaws.com/1737400668065-ottomator-logo.png", "persistence": "memory", "position": "right", "spacing": {"side": 24, "bottom": 24}, "title": "Hackathon Dynamic Chatbot", "watermark": true, "audioInterface": false}, "platform": "webchat"}, "programResources": {"messages": {"678daa9471fdef590d29e5ff": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": ""}, {"text": "{welcome_message}"}, {"text": " "}]}], "delay": null}, "condition": null}]}}, "678db05571fdef590d29e8ea": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": ""}, {"text": "{multiple_topic_answer}"}, {"text": " "}]}], "delay": null}, "condition": null}]}}, "678db4a971fdef590d29ed5e": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "I don’t have an answer to your question yet. Please feel free to use our contact information: "}]}, {"children": [{"children": [{"text": ""}]}]}, {"children": [{"text": "Phone Number: "}, {"text": "{clean_business_phone_number}"}, {"text": " "}]}, {"children": [{"children": [{"text": ""}]}]}, {"children": [{"text": "Email: "}, {"text": "{clean_business_email}"}, {"text": " "}]}], "delay": null}, "condition": null}]}}, "678db52471fdef590d29eda8": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": ""}, {"text": "{one_topic_answer}"}, {"text": " "}]}], "delay": null}, "condition": null}]}}, "678edea6771abeed1aac1d34": {"variants": {"default:en-us": [{"data": {"text": [{"children": [{"text": "Make sure you use the link provided exactly as it is—don’t modify it. "}]}], "delay": null}, "condition": null}]}}}, "prompts": {"672ae8ad671f984162a8aa34": {"settings": {"model": "gpt-3.5-turbo", "maxTokens": 256, "temperature": 0.3}, "system": "You are a customer support bot that tries to help the user. Guide them towards asking for support.", "messages": [{"content": "Speak directly to the end customer. Do not include anything outside of your answer. Ensure that you speak to users in the language identied below:\n\nlanguage: {66e8821d517bf2228b6c8667}", "role": "user"}]}, "672ae903671f984162a8aa3a": {"settings": {"model": "gpt-4o-mini", "maxTokens": 256, "temperature": 0.3}, "system": "You are an expert AI assistant trained to analyze customer inquiries and determine if they require clarification. Your role is to ensure efficient and accurate responses by identifying when additional information is needed from the customer.", "messages": [{"content": "# Clarification Assessment Prompt Template\n\nYou are an expert AI assistant trained to analyze customer inquiries and determine if they require clarification. Your role is to ensure efficient and accurate responses by identifying when additional information is needed from the customer.\n\n## Instructions:\n1. Carefully analyze the 'user's question' for specific details such as product or service types, policy names, or other relevant specifics that provide clear context.\n2. Review the 'chunks' to see if they contain information that directly addresses the specific details in the user's question.\n3. Output '~' if:\n   - The user's question contains specific details that are clearly addressed in the 'chunks'.\n   - The query aligns well with a specific part of the 'chunks' that provides a comprehensive answer.\n4. Output '#' only if:\n   - The query is genuinely broad or vague, lacking specific details that would allow for a direct answer from the 'chunks'.\n   - The 'chunks' contain multiple categories or types of information that could potentially answer the question, but the user's intent is unclear.\n   - There's a crucial piece of information missing from the user's question that is necessary to provide an accurate answer, and this information is not implied or obvious from the context given.\n5. If outputting '#', specify the type of information or category that would help better address their question, based on the 'chunks'.\n\n## Output format:\n[Decision: '~' or '#', (if '#') then clarification is required. Specify the type of information or category that would help better address their question.]\n\n## Important:\n- Focus on whether the available information in the chunks is sufficient to answer the user's specific question accurately.\n- Consider whether the user's question is too broad or vague to be answered comprehensively with the information provided in the chunks.\n- Assess if there are multiple possible interpretations of the user's question that would lead to significantly different answers based on the information in the chunks.\n\n## Input data:\n1. User's question: \n{last_utterance}\n\n  2. Chunks: \n{chunks}\n\nNow, take a deep breath and provide your assessment based on the instructions above.", "role": "user"}]}, "672ae92e671f984162a8aa40": {"settings": {"model": "gpt-3.5-turbo", "maxTokens": 256, "temperature": 0.3}, "system": "You are a question optimizer. Keep your questions down to one sentence", "messages": [{"content": "You are an AI assistant specialized in generating optimal questions for retrieval augmented generation (RAG) in the context of user queries and intent. Your goal is to create a detailed question that will yield the most relevant and useful information from a knowledge base to enhance the quality of responses to user queries.\n\n##Task\n\nGenerate a single, comprehensive question that best captures the information needed to address the user’s query or intent, considering the following guidelines:\n\n\t1.\tAnalyze the user’s last response and the conversation history to identify the core topic, intent, or information need.\n\t2.\tCraft a question that is:\n\t•\tHighly specific and detailed, targeting exact information in the knowledge base\n\t•\tComprehensive, covering multiple aspects of the topic mentioned by the user\n\t•\tPhrased naturally, but including key terms multiple times for emphasis\n\t•\tFocused strongly on the user’s perspective if specified in the conversation\n\t3.\tInclude relevant keywords and phrases throughout the question, repeating important terms where appropriate. Use specific phrasing that emphasizes the user perspective.\n\t4.\tStructure the question to cover multiple related aspects, using phrases like “including”, “as well as”, or “such as”.\n\t5.\tIncorporate specific terms that are likely to appear in relevant documents.\n\t6.\tUse repetitive but varied phrasing to emphasize key points and increase the likelihood of matching relevant documents.\n\t7.\tFor policy-related questions, mention potential exceptions or special circumstances only if they were part of the user’s query.\n\t8.\tFocus solely on the aspects mentioned by the user. Do not include additional topics or comparisons unless specifically requested.\n\n\n##Output\n\nProvide only the generated question, without quotation marks or any additional text.\n\n## Input Data\n\nUser's last response:\nHow do common processes work for users?", "role": "user"}, {"content": "What are the specific details of common processes for users, including relevant terms for users based on specific factors, and potential challenges that users may face when following common processes? Additionally, are there any exceptions or special considerations such as specific circumstances that may impact the user process?", "role": "assistant"}, {"content": "User's last response:\nWhat services are offered for users?", "role": "user"}, {"content": "What are the different services offered specifically for users, including key details, features, and any limitations or exclusions for user service plans? How do these user service plans work within the industry platform, and what should users know about the service when using it through the platform?", "role": "assistant"}, {"content": "{vf_memory}", "role": "user"}, {"content": "User's last response:\n$\"{last_utterance}\"\n\nNow, take a deep breath and generate a detailed, comprehensive question that targets the most relevant information in the knowledge base, repeating key terms where appropriate and emphasizing the user perspective as specified in their query.\n\nGenerated question:", "role": "user"}]}, "672ae94c671f984162a8aa43": {"settings": {"model": "gpt-4o-mini", "maxTokens": 256, "temperature": 0.3}, "system": "You are an AI Support Agent. Your task is to create a brief, friendly clarifying question that fits naturally into an ongoing conversation.", "messages": [{"content": "You are an AI Support Agent. Your task is to create a brief, friendly clarifying question that fits naturally into an ongoing conversation. You will be given the following data: 'guidelines for the question', the 'user's question' that needs a response, and the 'information retrieved from a knowledge base'.\n\n## Instructions:\n1. Analyze the user's question and the provided information.\n2. Identify the key ambiguity or missing detail in the user's query.\n3. Construct a single, clear, and concise clarifying question related to providing support.\n4. Use a friendly tone, but prioritize brevity and directness.\n5. You may use one appropriate emoji if it adds value without increasing length significantly.\n6. Guide the user towards a precise response.\n7. Avoid multiple questions or unnecessary explanations.\n8. Focus on the single most important aspect that requires clarification.\n9. Begin with a very brief acknowledgment, then ask your clarifying question.\n10. Aim for a total response length of 15-20 words maximum.\n\n## Example Responses:\n- \"Sure. Are you a  guest or host? The cancellation process differs for each.\"\n- \"Uh-huh, okay. Can you tell me which  plan are you asking about?\"\n- \"Sure. And is this for a domestic or international  rental? \"\n\n## Input data:\nGuidelines for the question: $\"{66e8813c517bf2228b6c8665}\"\n \nThe user's question: $\"{last_utterance}\"\n Information from knowledge base: $\"{chunks}\"\nThe user's language: $\"{66e8821d517bf2228b6c8667}\"\n \nNow, create your brief, friendly clarifying question in the user's language that fits naturally into the ongoing dialogue:", "role": "user"}]}, "672ae8f2671f984162a8aa37": {"settings": {"model": "gpt-4o-mini", "maxTokens": 500, "temperature": 0.3}, "system": "You are an AI customer support agent.", "messages": [{"content": "{vf_memory}", "role": "user"}, {"content": "You are an AI customer support bot helping a customer with their question. When communicating with guests, follow these guidelines:\n\nUse the following step-by-step reasoning.\n\n1. Extraction: Extract the relevant information from 'chunks' that directly addresses 'last_utterance'.\n\n2. Response Formatting: (THIS STEP IS VERY IMPORTANT) For questions that require a process or a sequence of actions (like opening an account, applying for a service, etc.), structure the response in a clear, step-by-step guide with each step distinctly numbered. For other types of questions, provide a concise and direct answer.\n\n3. Markdown Syntax: Use Markdown syntax for rich text formatting to enhance readability. For process-oriented responses, use a numbered list format. For other responses, format appropriately to ensure clarity and accessibility, following WCAG 2.1 guidelines.\n\n5. Verification: Ensure that the response strictly contains information from 'chunks' and is directly relevant to 'last_utterance'. Do not incorporate any additional or external information.\n\n6. Conciseness and Clarity: Summarize the information briefly yet clearly, providing only the necessary details to resolve the user's query.\n\nAdditional Instructions:\n\nDo not include links to URLs or information not present in 'chunks'.\nMaintain a friendly, conversational tone, using \"I\" and \"we\" to foster a connection with the user.\n\nSpecifically, emphasize the step-by-step format for any instructional or process-related queries.\n\nInput data:\n1. Customers question: \"{last_utterance}\"\n2. Provided details: \"{chunks}\"\n3. User's language: \"{66e8821d517bf2228b6c8667}\"  (if unknown default to English)\n\nNow, take a deep breath and respond to the guest's question in their language ({66e8821d517bf2228b6c8667}),  or in English if the language is unknown. Remember to follow all the guidelines provided above.", "role": "user"}]}, "672ae961671f984162a8aa46": {"settings": {"model": "gpt-3.5-turbo", "maxTokens": 256, "temperature": 0.3}, "system": "You are a language classifier", "messages": [{"content": "Analyze the conversation history and the last utterance from a user to determine their likely language. Consider the previously determined language, if available.\n\nClassify the user's language into a specific language name (e.g., English, French, Spanish) or \"unknown\" if the language cannot be determined.\n\nRespond with a JSON string containing the classification, confidence level (high, medium, or low), and a brief explanation of your reasoning. \n\nJSON structure format:\n\n{\n  \"language\": string,\n  \"confidence\": string,\n  \"reasoning\": string\n}\n\n\nExample:\n\nUser: \"How much does it cost to use your service?\"", "role": "user"}, {"content": "{\"language\": \"English\", \"confidence\": \"high\", \"reasoning\": \"The utterance is in clear, grammatically correct English.\"}", "role": "assistant"}, {"content": "Combien coûte votre service ?", "role": "user"}, {"content": "{\"language\": \"French\", \"confidence\": \"high\", \"reasoning\": \"The utterance is in French, using correct French grammar and vocabulary.\"}", "role": "assistant"}, {"content": "¿Cuál es el precio de su servicio?", "role": "user"}, {"content": "{\"language\": \"Spanish\", \"confidence\": \"high\", \"reasoning\": \"The sentence is in Spanish, asking about the price of the service.\"}", "role": "assistant"}, {"content": "Hello! Quel est le prix de votre service ?", "role": "user"}, {"content": "{\"language\": \"French\", \"confidence\": \"medium\", \"reasoning\": \"While 'Hello' is English, the rest of the sentence is in French, indicating the user is primarily communicating in French.\"}", "role": "assistant"}, {"content": "👍", "role": "user"}, {"content": "{\"language\": \"unknown\", \"confidence\": \"low\", \"reasoning\": \"The user only used an emoji, which is not specific to any language.\"}", "role": "assistant"}, {"content": "What's the キャンセル policy?", "role": "user"}, {"content": "{\"language\": \"English\", \"confidence\": \"medium\", \"reasoning\": \"The sentence structure is English, but it includes a Japanese word. The primary language appears to be English with some Japanese vocabulary.\"}", "role": "assistant"}, {"content": "Now, analyze the following:\n\nLast utterance: $\"{last_utterance}\"\nAgent's last response: $\"{last_response}\"\nPreviously set language: $\"{66e8821d517bf2228b6c8667}\"", "role": "user"}]}}}, "prototype": {"type": "chat", "data": {"name": "Chatbot B: Dynamic Deployed Chatbot", "locales": ["en-US"]}, "model": {"intents": [], "slots": [{"key": "67859fb20cfcf7818a1338f2", "name": "Email", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}, "description": null}, {"key": "6785a03f0cfcf7818a13394b", "name": "PIN", "color": "#DC8879", "inputs": [], "type": {"value": "VF.NUMBER"}, "description": null}, {"key": "6785bc920cfcf7818a13406c", "name": "second_email_attempt", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}, "description": null}, {"key": "6785c44a0cfcf7818a1342c3", "name": "Name", "color": "#d58493", "inputs": [], "type": {"value": "VF.NAME"}, "description": null}]}, "context": {"stack": [{"diagramID": "64dbb6696a8fab0013dba194", "storage": {}, "variables": {}}], "variables": {}}, "surveyorContext": {"nonDraftResponsesMap": {}, "responseMessagesByDiscriminatorIDMap": {}, "responseDiscriminatorsByResponseIDMap": {}, "functionDefinitions": {}, "referencedResponseIDs": ["678daa9471fdef590d29e5ff", "678db05571fdef590d29e8ea", "678db4a971fdef590d29ed5e", "678db52471fdef590d29eda8", "678edea6771abeed1aac1d34"], "slotsMap": {"67859fb20cfcf7818a1338f2": {"key": "67859fb20cfcf7818a1338f2", "name": "Email", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}}, "6785a03f0cfcf7818a13394b": {"key": "6785a03f0cfcf7818a13394b", "name": "PIN", "color": "#DC8879", "inputs": [], "type": {"value": "VF.NUMBER"}}, "6785bc920cfcf7818a13406c": {"key": "6785bc920cfcf7818a13406c", "name": "second_email_attempt", "color": "#b3976c", "inputs": [], "type": {"value": "VF.EMAIL"}}, "6785c44a0cfcf7818a1342c3": {"key": "6785c44a0cfcf7818a1342c3", "name": "Name", "color": "#d58493", "inputs": [""], "type": {"value": "VF.NAME"}}}, "platform": "webchat", "products": {}, "extraSlots": [], "interfaces": [], "permissions": [], "projectType": "chat", "extraIntents": [], "usedEventsSet": [], "usedIntentsSet": [], "goToIntentsSet": [], "entitiesMap": {}, "variableMap": {}, "intentsMap": {}, "requiredEntitiesMap": {}, "eventsMap": {}, "cmsVariables": {"welcome_message": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "selected_variables": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "final_variables": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "multiple_topic_answer": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "number_of_topics": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "one_topic_answer": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "sessions": {"isSystem": true, "description": "The number of times a particular user has opened the app.", "defaultValue": null}, "user_id": {"isSystem": true, "description": "The user's unique ID.", "defaultValue": null}, "timestamp": {"isSystem": true, "description": "UNIX timestamp (number of seconds since January 1st, 1970 at UTC).", "defaultValue": null}, "platform": {"isSystem": true, "description": "The platform your agent is running on (e.g. \"voiceflow\").", "defaultValue": null}, "locale": {"isSystem": true, "description": "The locale of the user (eg. en-US, en-CA, it-IT, fr-FR, ...).", "defaultValue": null}, "KB_response": {"isArray": false, "isSystem": false, "datatype": "any", "description": "The final answer formed by the AI to be shown to the user.", "defaultValue": null}, "intent_confidence": {"isSystem": true, "description": "The confidence interval (measured as a value from 0 to 100) for the most recently matched intent.", "defaultValue": null}, "last_response": {"isSystem": true, "description": "The agent's last response (text/speak) in a string.", "defaultValue": null}, "last_event": {"isSystem": true, "description": "The object containing the last event that the user client has triggered.", "defaultValue": null}, "last_utterance": {"isSystem": true, "description": "The user's last utterance in a text string.", "defaultValue": null}, "vf_memory": {"isSystem": true, "description": "Last 10 user inputs and agent responses in a string (e.g. \"agent: How can i help?\"\nuser: What's the weather today?).", "defaultValue": null}, "optimized_question": {"isArray": false, "isSystem": false, "datatype": "any", "description": "Optimized version of the users question for the AI to answer. It stores a reformatted question that uses memory.", "defaultValue": null}, "clarifying_question_check_counter": {"isArray": false, "isSystem": false, "datatype": "any", "description": "How many times the AI has asked a clarifying question. This is used so it doesnt ask too many clarifying questions.", "defaultValue": null}, "clarifying_question_check": {"isArray": false, "isSystem": false, "datatype": "any", "description": "Determine if the AI needs to ask a clarifying question.", "defaultValue": null}, "clarifying_question": {"isArray": false, "isSystem": false, "datatype": "any", "description": "Clarifying question asked by the AI", "defaultValue": null}, "language_JSON": {"isArray": false, "isSystem": false, "datatype": "any", "description": "Information about the users language. Generated by AI", "defaultValue": null}, "chunks": {"isArray": false, "isSystem": false, "datatype": "any", "description": "Information received from the knowledge base - also known as 'chunks'.", "defaultValue": null}, "BusinessID": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "user_password": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "email_verification": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_services": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "second_email_verification": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "user_intent": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_name": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_phone_number": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_address": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_operating_hours": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_name": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_operating_hours": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_phone_number": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "raw_business_email": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_address": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_email": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "clean_business_services": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "business_name_confirmation": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "business_name_conditional": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "phone_number_confirmation": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "phone_number_conditional": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "business_ID_from_airtable": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "deployment_id": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "spa_name": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}, "multiple_topic_answer_verification": {"isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null}}, "cmsEntities": {"Email": {"variants": [], "classifier": "VF.EMAIL", "description": null}, "PIN": {"variants": [], "classifier": "VF.NUMBER", "description": null}, "second_email_attempt": {"variants": [], "classifier": "VF.EMAIL", "description": null}, "Name": {"variants": [], "classifier": "VF.NAME", "description": null}}}, "settings": {}, "platform": "webchat"}, "settings": {"intentClassification": {"type": "llm", "params": {"model": "claude-3-haiku", "temperature": 0.1}, "promptWrapper": null}}, "topics": [{"type": "DIAGRAM", "sourceID": "64dbb6696a8fab0013dba194"}], "variables": ["welcome_message", "selected_variables", "final_variables", "multiple_topic_answer", "number_of_topics", "one_topic_answer", "sessions", "user_id", "timestamp", "platform", "locale", "KB_response", "intent_confidence", "last_response", "last_event", "last_utterance", "vf_memory", "optimized_question", "clarifying_question_check_counter", "clarifying_question_check", "clarifying_question", "language_JSON", "chunks", "BusinessID", "user_password", "email_verification", "raw_business_services", "second_email_verification", "user_intent", "raw_business_name", "raw_business_phone_number", "raw_business_address", "raw_business_operating_hours", "clean_business_name", "clean_business_operating_hours", "clean_business_phone_number", "raw_business_email", "clean_business_address", "clean_business_email", "clean_business_services", "business_name_confirmation", "business_name_conditional", "phone_number_confirmation", "phone_number_conditional", "business_ID_from_airtable", "deployment_id", "spa_name", "multiple_topic_answer_verification"], "_id": "678e863973475b48413aba14", "updatedAt": "2025-01-21T16:21:57.563Z", "publishedAt": "2024-09-27T16:54:27.996Z", "domains": [{"id": "cla06iyr900b206pkh8d4ap8n", "name": "Home", "live": true, "topicIDs": ["64dbb6696a8fab0013dba194"], "rootDiagramID": "64dbb6696a8fab0013dba194", "updatedBy": 1479280, "updatedAt": "2025-01-20T17:22:01.837Z"}], "projectID": "678e863973475b48413aba13", "rootDiagramID": "64dbb6696a8fab0013dba194", "templateDiagramID": "64dbb6696a8fab0013dba195"}, "diagrams": {"64dbb6696a8fab0013dba195": {"name": "Template <PERSON>agram", "type": "TEMPLATE", "zoom": 100, "nodes": {}, "offsetX": 0, "offsetY": 0, "modified": 1701833501, "creatorID": 1479280, "variables": [], "menuItems": [], "_id": "678e863973475b48413aba16", "diagramID": "64dbb6696a8fab0013dba195", "versionID": "678e863973475b48413aba14"}, "64dbb6696a8fab0013dba194": {"name": "ROOT", "type": "TOPIC", "zoom": 100, "nodes": {"start00000000000000000000": {"type": "start", "data": {"name": "Start", "color": "#56b365", "steps": [], "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "67892930751751204509c866", "id": "6032afcf359e8c14c06c0319", "data": {"points": [{"point": [-1368.75, -1671.69]}, {"point": [-1313.73, -1674.34]}]}}}, "dynamic": []}, "label": "Start"}, "nodeID": "start00000000000000000000", "coords": [-1433.4018111216649, -1693.6927363685538]}, "67892930751751204509c864": {"type": "set-v3", "data": {"name": "", "items": [{"id": "cm5zi3cef00ku337qhmqki75p", "type": "value", "label": null, "value": [{"text": [{"text": [{"text": ["{last_event.payload.deployment_id}"], "attributes": {"__type": "text", "color": {"r": 57, "g": 125, "b": 255, "a": 1}}}], "attributes": {"__type": "link", "url": "{last_event.payload.deployment_id}"}}]}], "variableID": "6789231f6fcd91c13b792b8b"}], "label": "", "portsV2": {"byKey": {"next": {"type": "next", "target": "6789841e13a6fafde3b4b3fc", "id": "67892930751751204509c865", "data": {"points": [{"point": [-982.73, -1603.34]}, {"point": [-941.7, -1603.34]}, {"point": [-941.7, -1697.66]}, {"point": [-900.66, -1697.66], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "67892930751751204509c864"}, "67892930751751204509c866": {"type": "block", "data": {"name": "Extracting ID from the URL", "steps": ["67892930751751204509c864"], "color": "#56b365"}, "nodeID": "67892930751751204509c866", "coords": [-1148.7308391296413, -1701.343198238895]}, "6789841e13a6fafde3b4b3f9": {"type": "api-v2", "data": {"name": "", "url": [{"text": ["https://api.airtable.com/v0/[Your Airtable Base ID]/tblXCL2NSbAnGLwzb?fields%5B%5D=ClinicName&fields%5B%5D=PhoneNumber&fields%5B%5D=Address&fields%5B%5D=Services&fields%5B%5D=BusinessEmail&fields%5B%5D=OperatingHours&filterByFormula=%7BBusinessID%7D%3D%22\r\n", {"variableID": "6789231f6fcd91c13b792b8b"}, "%22\r\n"]}], "headers": [{"id": "cm5zw2oks0162337plm9yxihz", "key": "Authorization", "value": [{"text": ["Bearer  [Your Airtable API Key]"]}]}], "httpMethod": "get", "queryParameters": [], "responseMappings": [{"id": "cm5zw8u2c017p337pq8h32nk5", "path": "response.records[0].fields.ClinicName", "variableID": "6786f5597a74e1b822c86e6a"}, {"id": "cm5zwcra60193337paesnu6hy", "path": "response.records[0].fields.PhoneNumber", "variableID": "6786f5677a74e1b822c86e7a"}, {"id": "cm64cxi7500a9357p8kcima2y", "path": "response.records[0].fields.OperatingHours", "variableID": "6786f5617a74e1b822c86e6e"}, {"id": "cm64cxrbr00ag357pt9lndc3d", "path": "response.records[0].fields.Address", "variableID": "6786f52d7a74e1b822c86e5d"}, {"id": "cm64cxxrd00an357pr08mcvsd", "path": "response.records[0].fields.Services", "variableID": "6786f5737a74e1b822c86e7e"}, {"id": "cm64cytz700b7357pwnuk9bft", "path": "response.records[0].fields.BusinessEmail", "variableID": "6786f5307a74e1b822c86e5e"}], "portsV2": {"byKey": {"next": {"type": "next", "target": "678ede80e5a19aee5fb545a7", "id": "6789841e13a6fafde3b4b3fa", "data": {"points": [{"point": [-569.66, -1590.66]}, {"point": [-478.91, -1590.66]}, {"point": [-478.91, -1674.29]}, {"point": [-388.16, -1674.29], "allowedToTop": true}]}}, "fail": {"type": "fail", "target": null, "id": "6789841e13a6fafde3b4b3fb"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "6789841e13a6fafde3b4b3f9", "coords": [0, 0]}, "6789841e13a6fafde3b4b3fc": {"type": "block", "data": {"name": "Retrieving specific data from Airtable using the ID", "steps": ["6789841e13a6fafde3b4b3f9"], "color": "#56b365"}, "nodeID": "6789841e13a6fafde3b4b3fc", "coords": [-735.6621723078318, -1724.6644362829127]}, "678daa694806b04525357574": {"type": "ai_set", "data": {"name": "AI Set", "label": "Welcome Message", "sets": [{"mode": "prompt", "prompt": "Create a brief and welcoming message for the user using the Dental Clinic information provided. Keep it concise and include up to 2 emojis for a friendly touch.\n\nDetails to Use:\nDental Clinic Name: {{[unknown].6786f5597a74e1b822c86e6a}}\nRequirements:\nThe message should be short, welcoming, and clear.\nUse a maximum of 2 emojis to maintain a friendly tone.\nInclude the clinic name in the message.\nYou have to use at least one blank line\nNEVER OUTPUT SOMETHING THAT IS NOT THE WELCOME MESSAGE ITSELF. IT HAS TO BE READY TO SHOW", "variable": "678daa8e71fdef590d29e5f6"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": null, "id": "678daa694806b04525357575"}}, "dynamic": []}}, "nodeID": "678daa694806b04525357574"}, "678daa694806b04525357576": {"type": "block", "data": {"name": "Welcome Message", "steps": ["678daa694806b04525357574", "678daa934806b0452535757d", "678dab144806b04525357582"], "color": "#5b9fd7"}, "nodeID": "678daa694806b04525357576", "coords": [264.08528303737484, -1719.3260795882613]}, "678daa934806b0452535757d": {"type": "message", "data": {"name": "", "messageID": "678daa9471fdef590d29e5ff", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678daa934806b0452535757e"}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678daa934806b0452535757d"}, "678dab144806b04525357582": {"type": "capture-v3", "data": {"name": "Capture", "capture": {"type": "user-reply", "variableID": "last_utterance"}, "listenForOtherTriggers": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "678e83dc7b263cd219d5b3d0", "id": "678dab144806b04525357583", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678dab144806b04525357582", "coords": [0, 0]}, "678dab214806b04525357588": {"type": "ai_set", "data": {"name": "AI Set", "label": "Multiple topic answer generation", "sets": [{"mode": "prompt", "prompt": "You are given these Knowledge Base variables:\r\n\r\nBusiness Name: {{[unknown].6786f5597a74e1b822c86e6a}}\r\nBusiness Phone: {{[unknown].6786f5677a74e1b822c86e7a}}\r\nBusiness Hours: {{[unknown].6786f5617a74e1b822c86e6e}}\r\nBusiness Address: {{[unknown].6786f52d7a74e1b822c86e5d}}\r\nBusiness Services: {{[unknown].6786f5737a74e1b822c86e7e}}\r\nBusiness Email: {{[unknown].6786f5307a74e1b822c86e5e}}\r\n\rBusiness Contact: {{[unknown].6786f5677a74e1b822c86e7a}}  and {{[unknown].6786f5307a74e1b822c86e5e}} \r\n\r\nThe user’s latest message references multiple topics. Provide one concise, combined answer. Follow these rules:\r\n\r\n1. **Use Only Existing Variables**:\r\n   - If the user asks for a variable not listed (e.g., “insurance coverage” if you don’t store that), respond with “I’m sorry, I don’t have that information.”\r\n2. **No Hallucinations**:\r\n   - If a requested topic is empty or null in your database, say “I’m sorry, I don’t have that information” for that piece only.\r\n3. **Comprehensive**:\r\n   - If the user asks about 3 variables, address all 3 in a short but clear manner.\r\n4. **Formatting**:\r\n   - You can respond with a short paragraph or bullet points, whichever is clearer.\r\n5. **Context**:\r\n   - If the user asked for something in the conversation history, rely on {{[vf_memory].vf_memory}} for consistency. \r\n  \r\n\r\nUser Input:\r\n{{[last_utterance].last_utterance}}\r\n\r\nChat history:\r\n{{[vf_memory].vf_memory}}\r\n", "variable": "678daf4f71fdef590d29e88e"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678e84037b263cd219d5b3e2", "id": "678dab214806b04525357589", "data": {}}}, "dynamic": []}}, "nodeID": "678dab214806b04525357588", "coords": [0, 0]}, "678dab214806b0452535758a": {"type": "block", "data": {"name": "Multiple topic answer generation", "steps": ["678dab214806b04525357588"], "color": ""}, "nodeID": "678dab214806b0452535758a", "coords": [758.6340295957081, -1073.5039391769315]}, "678daf554806b045253575af": {"type": "ai_set", "data": {"name": "AI Set", "label": "Number of topics", "sets": [{"mode": "prompt", "prompt": "Analyze the user’s last utterance and return the number of distinct questions or topics mentioned. However, if the utterance is unspecific or ambiguous (for example, a fragment such as \"until when?\"), output the word \"unspecific\" instead of a number.\r\n\r\nDefinitions:\r\n- A \"topic\" is any unique piece of information requested (e.g., phone number, services, operating hours, email, address, prices, contact details).\r\n- If multiple topics are explicitly mentioned in a single sentence (e.g., \"phone number and services\"), count each separately.\r\n- If the utterance is vague but clearly references at least one potential topic (e.g., \"tell me about your phone number\"), count it as 1.\r\n- If the utterance is completely ambiguous or consists solely of a fragment without a clear topic (e.g., \"until when?\"), output \"unspecific\".\r\n- If the user says nothing related to your data or just random talk, output \"0\".\r\n\r\nOutput rules:\r\n- For a specific utterance, output only a single digit (0, 1, 2, 3, …) corresponding to the topic count.\r\n- For an ambiguous or unspecific utterance, output the exact word \"unspecific\".\r\n- Do not include any additional text or explanation.\r\n\r\nUser Input:\r\n{{[last_utterance].last_utterance}} \r\r\n\r\nChat history for reference:\r\n{{[vf_memory].vf_memory}} \r\r\n", "variable": "678dafe571fdef590d29e8b1"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "claude-3.5-haiku", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": null, "id": "678daf554806b045253575b0"}}, "dynamic": []}}, "nodeID": "678daf554806b045253575af"}, "678daf554806b045253575b1": {"type": "block", "data": {"name": "Counting the number of topics", "steps": ["678daf554806b045253575af", "678daf5c4806b045253575b8"], "color": "#56b365"}, "nodeID": "678daf554806b045253575b1", "coords": [803.5386711476868, -1503.9158586275366]}, "678daf5c4806b045253575b8": {"type": "condition-v3", "data": {"name": "", "noMatch": {"path": true, "repromptID": null, "pathLabel": null}, "condition": {"type": "logic", "items": [{"id": "cm64ep4ji01ge357q6m0gtknq", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "678dafe571fdef590d29e8b1"}, "rhs": [{"text": ["1"]}], "key": "cm64ep4ji01gf357qdazc3lqz", "operation": "is"}]}}, {"id": "cm66new9703pw357ql79ez49d", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "678dafe571fdef590d29e8b1"}, "rhs": [{"text": ["1"]}], "key": "cm66new9703px357qkbwlt3y4", "operation": "greater_than"}]}}, {"id": "cm66nf7ac03qp357qx1v7n9ci", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "678dafe571fdef590d29e8b1"}, "rhs": [{"text": ["unspecific"]}], "key": "cm66nf7ac03qq357qzwsjez7k", "operation": "is"}]}}]}, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678daf5c4806b045253575b9"}, "cm64ep4ji01ge357q6m0gtknq": {"id": "678dafe74806b045253575c2", "target": "678e83e87b263cd219d5b3d6", "type": "", "data": {}}, "cm66new9703pw357ql79ez49d": {"id": "678fc17b752c80c30da34b6b", "target": "678dab214806b0452535758a", "type": "", "data": {"points": [{"point": [637.54, -1300.92], "reversed": true}, {"point": [613.54, -1300.92]}, {"point": [613.54, -1102.92]}, {"point": [758.63, -1102.92]}, {"point": [758.63, -1073.5], "toTop": true, "allowedToTop": true}]}}, "cm66nf7ac03qp357qx1v7n9ci": {"id": "678fc189752c80c30da34b6d", "target": "678dab214806b0452535758a", "type": "", "data": {"points": [{"point": [637.54, -1228.92], "reversed": true}, {"point": [613.54, -1228.92]}, {"point": [613.54, -1102.92]}, {"point": [758.63, -1102.92]}, {"point": [758.63, -1073.5], "toTop": true, "allowedToTop": true}]}}, "else": {"id": "678fc9ed8a926d1ccec817b7", "target": "678dab214806b0452535758a", "type": "else", "data": {"points": [{"point": [637.54, -1165.92], "reversed": true}, {"point": [613.54, -1165.92]}, {"point": [613.54, -1102.92]}, {"point": [758.63, -1102.92]}, {"point": [758.63, -1073.5], "toTop": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678daf5c4806b045253575b8"}, "678db0554806b045253575c6": {"type": "message", "data": {"name": "", "messageID": "678db05571fdef590d29e8ea", "portsV2": {"byKey": {"next": {"type": "next", "target": "678dab144806b04525357582", "id": "678db0554806b045253575c7", "data": {"points": [{"point": [117.82, -1277.66], "reversed": true}, {"point": [74.09, -1277.66]}, {"point": [74.09, -1519.83]}, {"point": [98.09, -1519.83], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db0554806b045253575c6"}, "678db0554806b045253575c8": {"type": "block", "data": {"name": "Multiple topic answer", "steps": ["678db0554806b045253575c6"], "color": "#5b9fd7"}, "nodeID": "678db0554806b045253575c8", "coords": [283.81743667693104, -1356.6631396732307]}, "678db1724806b045253575cf": {"type": "ai_set", "data": {"name": "AI Set", "label": "Intent analysis", "sets": [{"mode": "prompt", "prompt": "Analyze the user’s last utterance and decide which ONE category matches best. \r\nOutput only the exact category from this list (no extra text):\r\n\r\nbusiness_name\r\nbusiness_phone_number\r\nbusiness_operating_hours\r\nbusiness_address\r\nbusiness_services\r\nbusiness_email\r\nbusiness_prices\r\nbusiness_contact\r\nelse\r\n\r\nRules:\r\n1. If the user explicitly asks for cost, fee, or \"how much,\" output `business_prices`.\r\n2. If they mention \"contact info,\" \"contact details,\" or \"get in touch,\" output `business_contact`.\r\n3. If the question doesn’t match any known variable, output `else`.\r\n4. Output only the category text—no quotes, no punctuation.\r\n\r\nUser Input:\r\n{{[last_utterance].last_utterance}}\r\n\r\nChat history:\r\n{{[vf_memory].vf_memory}}\r\n", "variable": "6785cdfd7c531a8a54422b76"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678e84247b263cd219d5b3e8", "id": "678db1724806b045253575d0", "data": {}}}, "dynamic": []}}, "nodeID": "678db1724806b045253575cf"}, "678db1724806b045253575d1": {"type": "block", "data": {"name": "Intent analysis", "steps": ["678db1724806b045253575cf"], "color": "#56b365"}, "nodeID": "678db1724806b045253575d1", "coords": [739.1493654307126, -2424.9255317405386]}, "678db21e4806b045253575e8": {"type": "condition-v3", "data": {"name": "", "noMatch": {"path": true, "repromptID": null}, "condition": {"type": "logic", "items": [{"id": "cm64f1dcu01rl357q0pzgiq6a", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_name"]}], "key": "cm64f1dcv01rm357ql06cnh0x", "operation": "is"}]}}, {"id": "cm64f1kig01s8357qe6h3eiwg", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_phone_number"]}], "key": "cm64f1kig01s9357qb9t930oc", "operation": "is"}]}}, {"id": "cm64f1upd01t4357qztlf1sm1", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_operating_hours"]}], "key": "cm64f1upd01t5357qgr9zgd5u", "operation": "is"}]}}, {"id": "cm64f2foc01ua357qpeyh28x3", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_address"]}], "key": "cm64f2foc01ub357qx2xxlo9x", "operation": "is"}]}}, {"id": "cm64f2szr01vb357qt8kbk674", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_services"]}], "key": "cm64f2szr01vc357q9jnsf5e3", "operation": "is"}]}}, {"id": "cm64f34a501w3357q32m3nqa7", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_email"]}], "key": "cm64f34a501w4357qx6bci4iz", "operation": "is"}]}}, {"id": "cm64f418301ww357qz3b9sfmy", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["else"]}], "key": "cm64f418301wx357qc4jszm5v", "operation": "is"}]}}, {"id": "cm65o9e9y03t0357q18wnltco", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_contact"]}], "key": "cm65o9e9y03t1357qd7l5krsy", "operation": "is"}]}}, {"id": "cm65o9j8c03tp357q27f97g0s", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6785cdfd7c531a8a54422b76"}, "rhs": [{"text": ["business_prices"]}], "key": "cm65o9j8c03tq357q42rdi0xk", "operation": "is"}]}}]}, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678db21e4806b045253575e9"}, "else": {"type": "else", "target": "678e83a87b263cd219d5b3ca", "id": "678db21e4806b045253575ea", "data": {}}, "cm64f1dcu01rl357q0pzgiq6a": {"id": "678db2234806b045253575f0", "target": "678e835fd602104d98f46788", "type": "", "data": {}}, "cm64f1kig01s8357qe6h3eiwg": {"id": "678db22c4806b045253575f3", "target": "678e83817b263cd219d5b3a6", "type": "", "data": {}}, "cm64f1upd01t4357qztlf1sm1": {"id": "678db2394806b045253575f5", "target": "678e83877b263cd219d5b3ac", "type": "", "data": {}}, "cm64f2foc01ua357qpeyh28x3": {"id": "678db2544806b045253575f7", "target": "678e838e7b263cd219d5b3b2", "type": "", "data": {}}, "cm64f2szr01vb357qt8kbk674": {"id": "678db2654806b045253575f9", "target": "678e83937b263cd219d5b3b8", "type": "", "data": {}}, "cm64f34a501w3357q32m3nqa7": {"id": "678db2744806b045253575fb", "target": "678e839b7b263cd219d5b3be", "type": "", "data": {}}, "cm64f418301ww357qz3b9sfmy": {"id": "678db29f4806b045253575fd", "target": "678e83a17b263cd219d5b3c4", "type": "", "data": {}}, "cm65o9e9y03t0357q18wnltco": {"id": "678edad8d4d91b3713a89d66", "target": "678edb0bd4d91b3713a89d71", "type": "", "data": {}}, "cm65o9j8c03tp357q27f97g0s": {"id": "678edaded4d91b3713a89d68", "target": "678edb02d4d91b3713a89d6b", "type": "", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db21e4806b045253575e8"}, "678db21e4806b045253575eb": {"type": "block", "data": {"name": "Intent conditional block", "steps": ["678db21e4806b045253575e8"], "color": "#b3976c"}, "nodeID": "678db21e4806b045253575eb", "coords": [738.4657697893646, -2287.4745588235483]}, "678db2d54806b04525357600": {"type": "ai_set", "data": {"name": "AI Set", "label": "Name: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f5597a74e1b822c86e6a}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\n\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db2d54806b04525357601", "data": {"points": [{"point": [1546.66, -2334.6]}, {"point": [1737.66, -2334.6]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db2d54806b04525357600", "coords": [0, 0]}, "678db2d54806b04525357602": {"type": "block", "data": {"name": "Name: Answer generation", "steps": ["678db2d54806b04525357600"], "color": ""}, "nodeID": "678db2d54806b04525357602", "coords": [1380.6649746678168, -2413.8533861153633]}, "678db44a4806b0452535760c": {"type": "ai_set", "data": {"name": "AI Set", "label": "Phone Number: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f5677a74e1b822c86e7a}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db44a4806b0452535760d", "data": {"points": [{"point": [1547.18, -2195.88]}, {"point": [1737.66, -2195.88]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db44a4806b0452535760c"}, "678db44f4806b04525357619": {"type": "block", "data": {"name": "Phone Number: Answer generation", "steps": ["678db44a4806b0452535760c"], "color": ""}, "nodeID": "678db44f4806b04525357619", "coords": [1381.1760223984772, -2275.134211257936]}, "678db45a4806b04525357624": {"type": "block", "data": {"name": "Operating Hours: Answer generation", "color": "", "steps": ["678db45a4806b04525357626"]}, "nodeID": "678db45a4806b04525357624", "coords": [1380.739386453775, -2131.0639347602396]}, "678db45a4806b04525357626": {"type": "ai_set", "data": {"name": "AI Set", "label": "Operating Hours: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f5617a74e1b822c86e6e}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db45a4806b04525357627", "data": {"points": [{"point": [1546.74, -2051.81]}, {"point": [1737.66, -2051.81]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db45a4806b04525357626"}, "678db46f4806b0452535762f": {"type": "block", "data": {"name": "Address: Answer generation", "color": "", "steps": ["678db46f4806b04525357631"]}, "nodeID": "678db46f4806b0452535762f", "coords": [1384.2958380666773, -1962.615778078213]}, "678db46f4806b04525357631": {"type": "ai_set", "data": {"name": "AI Set", "label": "Address: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f52d7a74e1b822c86e5d}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db46f4806b04525357632", "data": {"points": [{"point": [1550.3, -1883.37]}, {"point": [1737.66, -1883.37]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db46f4806b04525357631"}, "678db4794806b0452535763a": {"type": "block", "data": {"name": "Services: Answer generation", "color": "", "steps": ["678db4794806b0452535763c"]}, "nodeID": "678db4794806b0452535763a", "coords": [1387.831552352393, -1817.6157780782085]}, "678db4794806b0452535763c": {"type": "ai_set", "data": {"name": "AI Set", "label": "Services: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f5737a74e1b822c86e7e}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db4794806b0452535763d", "data": {"points": [{"point": [1553.83, -1738.37]}, {"point": [1737.66, -1738.37]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db4794806b0452535763c"}, "678db4864806b04525357645": {"type": "block", "data": {"name": "Email: Answer generation", "color": "", "steps": ["678db4864806b04525357647"]}, "nodeID": "678db4864806b04525357645", "coords": [1387.3315523523947, -1687.6514923639206]}, "678db4864806b04525357647": {"type": "ai_set", "data": {"name": "AI Set", "label": "Email: Answer generation", "sets": [{"mode": "prompt", "prompt": "Answer the user's input using the provided information. Use only the relevant details to construct a concise response. \n\nInformation provided: {{[unknown].6786f5307a74e1b822c86e5e}} \n\nUser message: {{[last_utterance].last_utterance}} \n\nRules:\nUse only the provided information to respond.\nKeep the response short and directly relevant to the user's input.\nChat history for reference and context: {{[vf_memory].vf_memory}} ", "variable": "678db44871fdef590d29ecf8"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "gpt-4o-mini", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db5234806b0452535765b", "id": "678db4864806b04525357648", "data": {"points": [{"point": [1553.33, -1608.4]}, {"point": [1737.66, -1608.4]}, {"point": [1737.66, -1521.31], "toTop": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678db4864806b04525357647"}, "678db4a94806b0452535764f": {"type": "message", "data": {"name": "", "messageID": "678db4a971fdef590d29ed5e", "portsV2": {"byKey": {"next": {"type": "next", "target": "678dab144806b04525357582", "id": "678db4a94806b04525357650", "data": {"points": [{"point": [32.71, -1922.74]}, {"point": [72.27, -1922.74]}, {"point": [72.27, -1518.58]}, {"point": [111.84, -1518.58], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db4a94806b0452535764f"}, "678db4a94806b04525357651": {"type": "block", "data": {"name": "I don’t have an answer to your question", "steps": ["678db4a94806b0452535764f"], "color": "#CB627B"}, "nodeID": "678db4a94806b04525357651", "coords": [-147.0390210330933, -2066.9895448330176]}, "678db5234806b04525357659": {"type": "message", "data": {"name": "", "messageID": "678db52471fdef590d29eda8", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678db5234806b0452535765a", "data": {}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678db5234806b04525357659"}, "678db5234806b0452535765b": {"type": "block", "data": {"name": "One topic answer", "steps": ["678db5234806b04525357659", "678e84777b263cd219d5b3f2"], "color": ""}, "nodeID": "678db5234806b0452535765b", "coords": [1737.6563211076666, -1521.3119359586801]}, "678e835fd602104d98f46787": {"type": "goToNode", "data": {"name": "", "nodeID": "678db2d54806b04525357602", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e835fd602104d98f46787"}, "678e835fd602104d98f46788": {"type": "actions", "data": {"name": "", "steps": ["678e835fd602104d98f46787"]}, "nodeID": "678e835fd602104d98f46788", "coords": [0, 0]}, "678e83817b263cd219d5b3a5": {"type": "goToNode", "data": {"name": "", "nodeID": "678db44f4806b04525357619", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83817b263cd219d5b3a5"}, "678e83817b263cd219d5b3a6": {"type": "actions", "data": {"name": "", "steps": ["678e83817b263cd219d5b3a5"]}, "nodeID": "678e83817b263cd219d5b3a6", "coords": [0, 0]}, "678e83877b263cd219d5b3ab": {"type": "goToNode", "data": {"name": "", "nodeID": "678db45a4806b04525357624", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83877b263cd219d5b3ab"}, "678e83877b263cd219d5b3ac": {"type": "actions", "data": {"name": "", "steps": ["678e83877b263cd219d5b3ab"]}, "nodeID": "678e83877b263cd219d5b3ac", "coords": [0, 0]}, "678e838e7b263cd219d5b3b1": {"type": "goToNode", "data": {"name": "", "nodeID": "678db46f4806b0452535762f", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e838e7b263cd219d5b3b1"}, "678e838e7b263cd219d5b3b2": {"type": "actions", "data": {"name": "", "steps": ["678e838e7b263cd219d5b3b1"]}, "nodeID": "678e838e7b263cd219d5b3b2", "coords": [0, 0]}, "678e83937b263cd219d5b3b7": {"type": "goToNode", "data": {"name": "", "nodeID": "678db4794806b0452535763a", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83937b263cd219d5b3b7"}, "678e83937b263cd219d5b3b8": {"type": "actions", "data": {"name": "", "steps": ["678e83937b263cd219d5b3b7"]}, "nodeID": "678e83937b263cd219d5b3b8", "coords": [0, 0]}, "678e839b7b263cd219d5b3bd": {"type": "goToNode", "data": {"name": "", "nodeID": "678db4864806b04525357645", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e839b7b263cd219d5b3bd"}, "678e839b7b263cd219d5b3be": {"type": "actions", "data": {"name": "", "steps": ["678e839b7b263cd219d5b3bd"]}, "nodeID": "678e839b7b263cd219d5b3be", "coords": [0, 0]}, "678e83a17b263cd219d5b3c3": {"type": "goToNode", "data": {"name": "", "nodeID": "678db4a94806b04525357651", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83a17b263cd219d5b3c3"}, "678e83a17b263cd219d5b3c4": {"type": "actions", "data": {"name": "", "steps": ["678e83a17b263cd219d5b3c3"]}, "nodeID": "678e83a17b263cd219d5b3c4", "coords": [0, 0]}, "678e83a87b263cd219d5b3c9": {"type": "goToNode", "data": {"name": "", "nodeID": "678db4a94806b04525357651", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83a87b263cd219d5b3c9"}, "678e83a87b263cd219d5b3ca": {"type": "actions", "data": {"name": "", "steps": ["678e83a87b263cd219d5b3c9"]}, "nodeID": "678e83a87b263cd219d5b3ca", "coords": [0, 0]}, "678e83dc7b263cd219d5b3cf": {"type": "goToNode", "data": {"name": "", "nodeID": "678daf554806b045253575b1", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83dc7b263cd219d5b3cf"}, "678e83dc7b263cd219d5b3d0": {"type": "actions", "data": {"name": "", "steps": ["678e83dc7b263cd219d5b3cf"]}, "nodeID": "678e83dc7b263cd219d5b3d0", "coords": [0, 0]}, "678e83e87b263cd219d5b3d5": {"type": "goToNode", "data": {"name": "", "nodeID": "678db1724806b045253575d1", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e83e87b263cd219d5b3d5"}, "678e83e87b263cd219d5b3d6": {"type": "actions", "data": {"name": "", "steps": ["678e83e87b263cd219d5b3d5"]}, "nodeID": "678e83e87b263cd219d5b3d6", "coords": [0, 0]}, "678e84037b263cd219d5b3e1": {"type": "goToNode", "data": {"name": "", "nodeID": "678fa21b752c80c30da34b15", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e84037b263cd219d5b3e1"}, "678e84037b263cd219d5b3e2": {"type": "actions", "data": {"name": "", "steps": ["678e84037b263cd219d5b3e1"]}, "nodeID": "678e84037b263cd219d5b3e2", "coords": [0, 0]}, "678e84247b263cd219d5b3e7": {"type": "goToNode", "data": {"name": "", "nodeID": "678db21e4806b045253575eb", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e84247b263cd219d5b3e7"}, "678e84247b263cd219d5b3e8": {"type": "actions", "data": {"name": "", "steps": ["678e84247b263cd219d5b3e7"]}, "nodeID": "678e84247b263cd219d5b3e8", "coords": [0, 0]}, "678e84777b263cd219d5b3f0": {"type": "actions", "data": {"name": "Actions", "steps": ["678e84777b263cd219d5b3f5"]}, "nodeID": "678e84777b263cd219d5b3f0", "coords": [40, 40.00000000000023]}, "678e84777b263cd219d5b3f2": {"type": "capture-v3", "data": {"name": "Capture", "capture": {"type": "user-reply", "variableID": "last_utterance"}, "listenForOtherTriggers": false, "portsV2": {"byKey": {"next": {"type": "next", "target": "678e84777b263cd219d5b3f0", "id": "678e84777b263cd219d5b3f3", "data": {"points": [{"point": [437.36, -1457.2799999999997]}, {"point": [498.96, -1457.2799999999997]}, {"point": [498.96, -1385.9299999999998]}, {"point": [560.56, -1385.9299999999998], "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e84777b263cd219d5b3f2"}, "678e84777b263cd219d5b3f5": {"type": "goToNode", "data": {"name": "", "nodeID": "678daf554806b045253575b1", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678e84777b263cd219d5b3f5"}, "678edb02d4d91b3713a89d6a": {"type": "goToNode", "data": {"name": "", "nodeID": "678db4794806b0452535763a", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678edb02d4d91b3713a89d6a"}, "678edb02d4d91b3713a89d6b": {"type": "actions", "data": {"name": "", "steps": ["678edb02d4d91b3713a89d6a"]}, "nodeID": "678edb02d4d91b3713a89d6b", "coords": [0, 0]}, "678edb0bd4d91b3713a89d70": {"type": "goToNode", "data": {"name": "", "nodeID": "678db44f4806b04525357619", "diagramID": "64dbb6696a8fab0013dba194", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678edb0bd4d91b3713a89d70"}, "678edb0bd4d91b3713a89d71": {"type": "actions", "data": {"name": "", "steps": ["678edb0bd4d91b3713a89d70"]}, "nodeID": "678edb0bd4d91b3713a89d71", "coords": [0, 0]}, "678ede80e5a19aee5fb545a4": {"type": "condition-v3", "data": {"name": "", "noMatch": {"path": true, "repromptID": null}, "condition": {"type": "logic", "items": [{"id": "cm65otjir00jc357p2per8wp5", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "6786f5597a74e1b822c86e6a"}, "rhs": [{"text": ["0"]}], "key": "cm65otjis00jd357powz8jxg6", "operation": "is"}]}}]}, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678ede80e5a19aee5fb545a5"}, "else": {"type": "else", "target": "678daa694806b04525357576", "id": "678ede80e5a19aee5fb545a6", "data": {}}, "cm65otjir00jc357p2per8wp5": {"id": "678ede84e5a19aee5fb545af", "target": "678edea5e5a19aee5fb545b4", "type": "", "data": {"points": [{"point": [-389.16, -1622.04], "reversed": true}, {"point": [-413.16, -1622.04]}, {"point": [-413.16, -1462.37]}, {"point": [-230.07, -1462.37]}, {"point": [-230.07, -1302.7], "toTop": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678ede80e5a19aee5fb545a4"}, "678ede80e5a19aee5fb545a7": {"type": "block", "data": {"name": "VALID BUSINESS ID Conditional", "steps": ["678ede80e5a19aee5fb545a4"], "color": "#b3976c"}, "nodeID": "678ede80e5a19aee5fb545a7", "coords": [-223.16307596235242, -1701.2916846853911]}, "678edea5e5a19aee5fb545b4": {"type": "block", "data": {"name": "Link may be modified", "color": "#CB627B", "steps": ["678edea5e5a19aee5fb545b6", "678edef0e5a19aee5fb545be"]}, "nodeID": "678edea5e5a19aee5fb545b4", "coords": [-230.07473531880686, -1302.703830547308]}, "678edea5e5a19aee5fb545b6": {"type": "message", "data": {"name": "", "messageID": "678edea6771abeed1aac1d34", "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678edea5e5a19aee5fb545b7", "data": {"points": [{"point": [58.95999999999995, -1883.99]}, {"point": [98.51999999999995, -1883.99]}, {"point": [98.51999999999995, -1479.83]}, {"point": [138.08999999999995, -1479.83], "allowedToTop": false}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678edea5e5a19aee5fb545b6"}, "678edef0e5a19aee5fb545be": {"type": "exit", "data": {"name": "", "portsV2": {"byKey": {}, "builtIn": {}, "dynamic": []}}, "nodeID": "678edef0e5a19aee5fb545be"}, "678fa21b752c80c30da34b13": {"type": "ai_set", "data": {"name": "AI Set", "label": "Multiple topic answer verification", "sets": [{"mode": "prompt", "prompt": "You are an expert QA for an AI-driven knowledge base. Your job is to verify that a candidate answer uses only information from the provided knowledge base. Do not add any extra explanations—your output should be one of the following:\r\n\r\n- \"Verified\" (if the candidate answer uses only data from the knowledge base)\r\n- \"Unverified: <list of unsupported topics>\" (if there is additional information not present in the knowledge base)\r\n\r\nThe knowledge base variables are:\r\n- Business Name: {{[clean_business_name].6786f5597a74e1b822c86e6a}}\r\n- Business Phone Number: {{[clean_business_phone_number].6786f5677a74e1b822c86e7a}}\r\n- Business Operating Hours: {{[clean_business_operating_hours].6786f5617a74e1b822c86e6e}}\r\n- Business Address: {{[clean_business_address].6786f52d7a74e1b822c86e5d}}\r\n- Business Services: {{[clean_business_services].6786f5737a74e1b822c86e7e}}\r\n- Business Email: {{[clean_business_email].6786f5307a74e1b822c86e5e}}\r\n\r\nCandidate Answer:\r\n{{[multiple_topic_answer].678daf4f71fdef590d29e88e}} \r\n\r\nPlease verify the candidate answer against the above variables.\r\n", "variable": "678fa2de6fad26319ac88255"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "claude-3.5-haiku", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": null, "id": "678fa21b752c80c30da34b14"}}, "dynamic": []}}, "nodeID": "678fa21b752c80c30da34b13"}, "678fa21b752c80c30da34b15": {"type": "block", "data": {"name": "Multiple topic answer verification", "steps": ["678fa21b752c80c30da34b13", "678fa2ee752c80c30da34b1b"], "color": ""}, "nodeID": "678fa21b752c80c30da34b15", "coords": [756.6767454622668, -909.9365473162075]}, "678fa2ee752c80c30da34b1b": {"type": "condition-v3", "data": {"name": "", "noMatch": {"path": true, "repromptID": null}, "condition": {"type": "logic", "items": [{"id": "cm66irdy201ih357qvrq7k2w1", "value": {"type": "value-variable", "matchAll": true, "assertions": [{"lhs": {"variableID": "678fa2de6fad26319ac88255"}, "rhs": [{"text": ["Verified"]}], "key": "cm66irdy201ii357qcp5mrzdd", "operation": "is"}]}}]}, "portsV2": {"byKey": {"next": {"type": "next", "target": null, "id": "678fa2ee752c80c30da34b1c"}, "else": {"type": "else", "target": "678fa455752c80c30da34b27", "id": "678fa2ee752c80c30da34b1d", "data": {"points": [{"point": [590.68, -698.44], "reversed": true}, {"point": [565.88, -698.44]}, {"point": [565.88, -558.83]}, {"point": [589.88, -558.83], "allowedToTop": false}]}}, "cm66irdy201ih357qvrq7k2w1": {"id": "678fa2f4752c80c30da34b23", "target": "678db0554806b045253575c8", "type": "", "data": {"points": [{"point": [590.68, -761.44], "reversed": true}, {"point": [519.75, -761.44]}, {"point": [519.75, -1329.66]}, {"point": [448.82, -1329.66], "reversed": true, "allowedToTop": true}]}}}, "builtIn": {}, "dynamic": []}}, "nodeID": "678fa2ee752c80c30da34b1b"}, "678fa455752c80c30da34b27": {"type": "ai_set", "data": {"name": "AI Set", "label": "Multiple topic answer fixing", "sets": [{"mode": "prompt", "prompt": "You are an expert editor specialized in refining AI-generated responses. You have been provided with a candidate answer for a user query, along with a verification result stored in the variable {{[multiple_topic_answer_verification].678fa2de6fad26319ac88255}}. This verification result may be either \"Verified\" if the candidate answer uses only information from the approved knowledge base or \"Unverified: <list of unsupported topics>\" if unsupported details are present.\r\n\r\nThe approved knowledge base variables are:\r\n- Business Name: {{[clean_business_name].6786f5597a74e1b822c86e6a}}\r\n- Business Phone Number: {{[clean_business_phone_number].6786f5677a74e1b822c86e7a}}\r\n- Business Operating Hours: {{[clean_business_operating_hours].6786f5617a74e1b822c86e6e}}\r\n- Business Address: {{[clean_business_address].6786f52d7a74e1b822c86e5d}}\r\n- Business Services: {{[clean_business_services].6786f5737a74e1b822c86e7e}}\r\n- Business Email: {{[clean_business_email].6786f5307a74e1b822c86e5e}}\r\n\r\nThe candidate answer is:\r\n{{[multiple_topic_answer].678daf4f71fdef590d29e88e}} \n\r\n\r\nUnsupported topics that must be removed/modified:  {{[multiple_topic_answer_verification].678fa2de6fad26319ac88255}} \r\n\r\nYour task is to revise the candidate answer as follows:\r\n1. Remove any details related to each unsupported topic specified in \r\n2. For any unsupported topic that was mentioned, replace or omit that portion of the answer. If necessary, insert the fallback phrase \"I'm sorry, I don't have that information.\" for those topics.\r\n3. Ensure the final answer uses only the data from the approved knowledge base and is accurate and concise.\r\n4. Do not add any new information that is not part of the provided variables.\r\n\r\nPlease output only the revised answer.\r\n", "variable": "678daf4f71fdef590d29e88e"}], "system": "", "maxTokens": 2000, "overrideParams": false, "model": "claude-3.5-haiku", "temperature": 0.7, "portsV2": {"byKey": {}, "builtIn": {"next": {"type": "next", "target": "678db0554806b045253575c8", "id": "678fa455752c80c30da34b28", "data": {"points": [{"point": [589.88, -558.58], "reversed": true}, {"point": [519.35, -558.58]}, {"point": [519.35, -1329.66]}, {"point": [448.82, -1329.66], "reversed": true, "allowedToTop": true}]}}}, "dynamic": []}}, "nodeID": "678fa455752c80c30da34b27"}, "678fa455752c80c30da34b29": {"type": "block", "data": {"name": "Multiple topic answer fixing", "steps": ["678fa455752c80c30da34b27"], "color": ""}, "nodeID": "678fa455752c80c30da34b29", "coords": [755.8802456424368, -637.827300258444]}}, "offsetX": 1706.7214488973318, "offsetY": 1654.954189094843, "modified": 1701833501, "creatorID": 1479280, "variables": [], "menuItems": [{"type": "NODE", "sourceID": "start00000000000000000000"}], "menuNodeIDs": [], "intentStepIDs": [], "_id": "678e863973475b48413aba17", "diagramID": "64dbb6696a8fab0013dba194", "versionID": "678e863973475b48413aba14"}}, "flows": [], "entities": [{"id": "67859fb20cfcf7818a1338f2", "name": "Email", "createdByID": 1479280, "folderID": null, "description": null, "color": "#b3976c", "classifier": "VF.EMAIL", "isArray": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785a03f0cfcf7818a13394b", "name": "PIN", "createdByID": 1479280, "folderID": null, "description": null, "color": "#DC8879", "classifier": "VF.NUMBER", "isArray": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785bc920cfcf7818a13406c", "name": "second_email_attempt", "createdByID": 1479280, "folderID": null, "description": null, "color": "#b3976c", "classifier": "VF.EMAIL", "isArray": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c44a0cfcf7818a1342c3", "name": "Name", "createdByID": 1479280, "folderID": null, "description": null, "color": "#d58493", "classifier": "VF.NAME", "isArray": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}], "entityVariants": [{"id": "6785d2f57d3065d990d130b3", "language": "en-us", "value": "", "synonyms": [""], "entityID": "6785c44a0cfcf7818a1342c3", "createdAt": "2025-01-20T17:22:02.000Z"}], "intents": [{"id": "None", "name": "None", "createdByID": 1479280, "folderID": null, "description": null, "automaticReprompt": false, "entityOrder": [], "automaticRepromptSettings": null, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}], "utterances": [], "requiredEntities": [], "folders": [{"id": "672b8cf088690850a00446e6", "name": "Clarifying question prompts", "scope": "prompt", "parentID": null, "createdAt": "2025-01-20T17:22:01.000Z"}, {"id": "672b8d3788690850a0044715", "name": "KB response prompts", "scope": "prompt", "parentID": null, "createdAt": "2025-01-20T17:22:01.000Z"}], "responses": [{"id": "67855644296fa7851c53a6c9", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "67855be7296fa7851c53a96b", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785a3c50cfcf7818a133b76", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785a3e30cfcf7818a133b9b", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785cda87c531a8a54422b56", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785ced37c531a8a54422bfb", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785bfee0cfcf7818a134108", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678596d20cfcf7818a133479", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c024", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c02c", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c034", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c037", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c03a", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c03d", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c040", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c048", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c2526663774798e2c050", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785c42c6663774798e2c055", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ad513f7bbb68ff4ef9b9", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ad5d3f7bbb68ff4ef9c9", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ae3195b700f4503281ca", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ae3195b700f4503281cd", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ae4b95b700f4503281d3", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ae3b95b700f4503281d0", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786ae5c95b700f4503281dc", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786b01795b700f4503281e9", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6786b1f93f7bbb68ff4efd4e", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787c42d6cc78b98ee73a242", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787cdf76cc78b98ee73a819", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787cebcce3450f6b9c625bb", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787d68b6cc78b98ee73af81", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787db316cc78b98ee73b35b", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787ec850b87dbe9ac5d8bbf", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787ec920b87dbe9ac5d8bce", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6787ee990b87dbe9ac5d8c93", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "67898920631a401e6d1a697b", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678daa9471fdef590d29e5ff", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678dad7a71fdef590d29e7ac", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678db05571fdef590d29e8ea", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678db4a971fdef590d29ed5e", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678db52471fdef590d29eda8", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678923676fcd91c13b792be4", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678928bb6fcd91c13b79301c", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678929536fcd91c13b79308f", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e4658c97035e0f16ac4caa", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e4658c97035e0f16ac4cad", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e495882c6dde44b3c05728", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e880a4ee916c66659cd5ee", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e9ed3ecc90326d546e95e6", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "66e9f4e4cc90326d546e9a90", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "6785508a296fa7851c53a322", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678550b8296fa7851c53a342", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678551ba71a3d4a110006e79", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "678edea6771abeed1aac1d34", "name": "", "createdByID": 1479280, "folderID": null, "type": "message", "draft": false, "createdAt": "2025-01-20T23:39:19.000Z", "updatedAt": "2025-01-20T23:39:19.000Z", "updatedByID": 1479280}], "responseMessages": [{"id": "678db4a971fdef590d29ed60", "discriminatorID": "678db4a971fdef590d29ed5f", "text": [{"text": ["I don’t have an answer to your question yet. Please feel free to use our contact information: "]}, {"text": [{"text": [""]}]}, {"text": ["Phone Number: ", {"variableID": "6786f5677a74e1b822c86e7a"}, " "]}, {"text": [{"text": [""]}]}, {"text": ["Email: ", {"variableID": "6786f5307a74e1b822c86e5e"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678db52471fdef590d29edaa", "discriminatorID": "678db52471fdef590d29eda9", "text": [{"text": ["", {"variableID": "678db44871fdef590d29ecf8"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678daa9471fdef590d29e601", "discriminatorID": "678daa9471fdef590d29e600", "text": [{"text": ["", {"variableID": "678daa8e71fdef590d29e5f6"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678dad7a71fdef590d29e7ae", "discriminatorID": "678dad7a71fdef590d29e7ad", "text": [{"text": ["", {"variableID": "678dad5871fdef590d29e798"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678db05571fdef590d29e8ec", "discriminatorID": "678db05571fdef590d29e8eb", "text": [{"text": ["", {"variableID": "678daf4f71fdef590d29e88e"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3ca0cfcf7818a133b84", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["Registration successful!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3ca0cfcf7818a133b85", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["Congratulations on your registration!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3ca0cfcf7818a133b86", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["You have been successfully enrolled!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3ca0cfcf7818a133b87", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["Success! You're now registered!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3ca0cfcf7818a133b88", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["Your registration has been completed!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3e30cfcf7818a133b9d", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["Apologies, there was an issue with our servers. Please try again later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a40e0cfcf7818a133bb8", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["We apologize for the inconvenience, our servers are experiencing an issue. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a40e0cfcf7818a133bb9", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["Sorry, we are facing server problems. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a40e0cfcf7818a133bba", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["We regret to inform you that our servers are experiencing technical difficulties. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a40e0cfcf7818a133bbb", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["We apologize for the inconvenience, but there is a problem with our servers. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a40e0cfcf7818a133bbc", "discriminatorID": "6785a3e30cfcf7818a133b9c", "text": [{"text": ["Sorry, there's a problem with our servers. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ad5d3f7bbb68ff4ef9cb", "discriminatorID": "6786ad5d3f7bbb68ff4ef9ca", "text": [{"text": ["What’s the name of your business?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3195b700f4503281cc", "discriminatorID": "6786ae3195b700f4503281cb", "text": [{"text": ["Let’s begin customizing your chatbot!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3195b700f4503281cf", "discriminatorID": "6786ae3195b700f4503281ce", "text": [{"text": ["Could you please provide your business phone number? Make sure to include the country code (e.g., +number) ."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3b95b700f4503281d2", "discriminatorID": "6786ae3b95b700f4503281d1", "text": [{"text": ["Could you please provide your business's email address?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae4b95b700f4503281d5", "discriminatorID": "6786ae4b95b700f4503281d4", "text": [{"text": ["Could you also share your business's address?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786b01795b700f4503281eb", "discriminatorID": "6786b01795b700f4503281ea", "text": [{"text": ["Could you please provide details about the services you offer, including their ", {"text": ["descriptions"], "attributes": {"__type": "text", "fontWeight": "700"}}, ", ", {"text": ["duration"], "attributes": {"__type": "text", "fontWeight": "700"}}, ", and ", {"text": ["pricing"], "attributes": {"__type": "text", "fontWeight": "700"}}, "? Feel free to elaborate or add any additional information you'd like to include! "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae5c95b700f4503281de", "discriminatorID": "6786ae5c95b700f4503281dd", "text": [{"text": ["Could you please provide your business's operating hours? "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786b1f93f7bbb68ff4efd50", "discriminatorID": "6786b1f93f7bbb68ff4efd4f", "text": [{"text": ["Great! I’ve noted your initial details. Let’s refine and review them together next. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785bfee0cfcf7818a13410a", "discriminatorID": "6785bfee0cfcf7818a134109", "text": [{"text": ["The email already exists. Too many attempts have been made. Please try again later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c027", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's collaborate to build your personalized chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c028", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's partner up to develop your unique chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c029", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's team up to design your one-of-a-kind chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c02a", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's join forces to craft your bespoke chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c02b", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's unite to construct your tailored chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c02e", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["To get started, I'll need a bit of information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c02f", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["To begin, I'll require some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c030", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["Let's start by gathering some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c031", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["In order to start, I'll need some details from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c032", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["To start, I'll need a few details from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c033", "discriminatorID": "6785c2526663774798e2c02d", "text": [{"text": ["To get the ball rolling, I'll need some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c036", "discriminatorID": "6785c2526663774798e2c035", "text": [{"text": ["Please provide your personal email address. It will be used for logging in and future conversations."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c039", "discriminatorID": "6785c2526663774798e2c038", "text": [{"text": ["Please create a simple, memorable PIN with a maximum of 4 digits. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c03c", "discriminatorID": "6785c2526663774798e2c03b", "text": [{"text": ["The email you provided is already in use. Try again."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c03f", "discriminatorID": "6785c2526663774798e2c03e", "text": [{"text": ["Please give me a moment."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c042", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["Registration successful!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c043", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["Congratulations on your registration!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c044", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["You have been successfully enrolled!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c045", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["Success! You're now registered!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c046", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["Your registration has been completed!"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c047", "discriminatorID": "6785c2526663774798e2c041", "text": [{"text": ["You have successfully registered! "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04a", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["Apologies, there was an issue with our servers. Please try again later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04b", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["We apologize for the inconvenience, our servers are experiencing an issue. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04c", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["Sorry, we are facing server problems. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04d", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["We regret to inform you that our servers are experiencing technical difficulties. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04e", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["We apologize for the inconvenience, but there is a problem with our servers. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c04f", "discriminatorID": "6785c2526663774798e2c049", "text": [{"text": ["Sorry, there's a problem with our servers. Please try again later."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c052", "discriminatorID": "6785c2526663774798e2c051", "text": [{"text": ["The email already exists. Too many attempts have been made. Please try again later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c026", "discriminatorID": "6785c2526663774798e2c025", "text": [{"text": ["Let's work together to create your very own customized chatbot for your spa salon."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e495882c6dde44b3c0572a", "discriminatorID": "66e495882c6dde44b3c05729", "text": [{"text": ["Hello and welcome to the ", {"text": ["Spa"], "attributes": {"__type": "text", "fontWeight": "700"}}, " ", {"text": ["Chatbot Creator"], "attributes": {"__type": "text", "fontWeight": "700"}}, "! Would you like to sign up for a new account or log in to continue your journey? "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787cdf76cc78b98ee73a81b", "discriminatorID": "6787cdf76cc78b98ee73a81a", "text": [{"text": ["Great! I've saved your business name. Let's continue with more data. Don't worry—you'll be able to change everything later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ad513f7bbb68ff4ef9bb", "discriminatorID": "6786ad513f7bbb68ff4ef9ba", "text": [{"text": ["Let’s begin customizing your chatbot! Try to be ", {"text": ["straightforward"], "attributes": {"__type": "text", "italic": true}}, ". Don’t worry—you’ll be able to change everything later. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c42c6663774798e2c057", "discriminatorID": "6785c42c6663774798e2c056", "text": [{"text": ["May I have your name, please?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787c42d6cc78b98ee73a244", "discriminatorID": "6787c42d6cc78b98ee73a243", "text": [{"text": ["Can we confirm that the name of your business is ", {"variableID": "6786f5597a74e1b822c86e6a"}, "?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787cebcce3450f6b9c625bd", "discriminatorID": "6787cebcce3450f6b9c625bc", "text": [{"text": ["Can we confirm that the phone number of your business is ", {"variableID": "6786f5677a74e1b822c86e7a"}, "?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787d68b6cc78b98ee73af83", "discriminatorID": "6787d68b6cc78b98ee73af82", "text": [{"text": ["Please provide the number again, and make sure it includes the country code. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787db316cc78b98ee73b35d", "discriminatorID": "6787db316cc78b98ee73b35c", "text": [{"text": [{"text": ["All data has been gathered. Uploading... "], "attributes": {"__type": "text", "fontWeight": "700"}}]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ec850b87dbe9ac5d8bc1", "discriminatorID": "6787ec850b87dbe9ac5d8bc0", "text": [{"text": ["Please, provide a valid PIN."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ec920b87dbe9ac5d8bd0", "discriminatorID": "6787ec920b87dbe9ac5d8bcf", "text": [{"text": ["Please, provide a valid name."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678596d20cfcf7818a13347b", "discriminatorID": "678596d20cfcf7818a13347a", "text": [{"text": ["Please give me a moment."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785cda87c531a8a54422b58", "discriminatorID": "6785cda87c531a8a54422b57", "text": [{"text": ["Test again? "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785ced37c531a8a54422bfd", "discriminatorID": "6785ced37c531a8a54422bfc", "text": [{"text": ["To test the capabilities of our chatbot, please sign up or log in "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678923676fcd91c13b792be6", "discriminatorID": "678923676fcd91c13b792be5", "text": [{"text": ["Then a Speak block: “Got ID ", {"text": [{"text": ["{deployment_id}"], "attributes": {"color": {"a": 1, "b": 255, "g": 125, "r": 57}, "__type": "text"}}], "attributes": {"url": "{deployment_id}", "__type": "link"}}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678929536fcd91c13b793091", "discriminatorID": "678929536fcd91c13b793090", "text": [{"text": ["Your deployment ID is: ", {"variableID": "6789231f6fcd91c13b792b8b"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678928bb6fcd91c13b79301e", "discriminatorID": "678928bb6fcd91c13b79301d", "text": [{"text": ["Your deployment ID is: ", {"text": [{"text": ["{last_event.payload.deployment_id}"], "attributes": {"color": {"a": 1, "b": 255, "g": 125, "r": 57}, "__type": "text"}}], "attributes": {"url": "{last_event.payload.deployment_id}", "__type": "link"}}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e9f4e4cc90326d546e9a92", "discriminatorID": "66e9f4e4cc90326d546e9a91", "text": [{"text": ["This workflow hasn't been built yet."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e4658c97035e0f16ac4cac", "discriminatorID": "66e4658c97035e0f16ac4cab", "text": [{"text": ["Sorry. I didn't quite get that, can you please try asking the question in a different way?"]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e4658c97035e0f16ac4caf", "discriminatorID": "66e4658c97035e0f16ac4cae", "text": [{"text": ["", {"variableID": "KBOutput"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e880a4ee916c66659cd5f0", "discriminatorID": "66e880a4ee916c66659cd5ef", "text": [{"text": ["", {"variableID": "66e881bb517bf2228b6c8666"}, "  "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e9ed3ecc90326d546e95e8", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["To get started, I'll need a bit of information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67854951296fa7851c539e11", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["To begin, I'll require some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67854951296fa7851c539e12", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["Let's start by gathering some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67854951296fa7851c539e13", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["In order to start, I'll need some details from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67854951296fa7851c539e14", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["To start, I'll need a few details from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67854951296fa7851c539e15", "discriminatorID": "66e9ed3ecc90326d546e95e7", "text": [{"text": ["To get the ball rolling, I'll need some information from you."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67855be7296fa7851c53a96d", "discriminatorID": "67855be7296fa7851c53a96c", "text": [{"text": ["The email you provided is already in use. Try again."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785508a296fa7851c53a324", "discriminatorID": "6785508a296fa7851c53a323", "text": [{"text": ["", {"variableID": "67855059296fa7851c53a300"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678550b8296fa7851c53a344", "discriminatorID": "678550b8296fa7851c53a343", "text": [{"text": ["Please provide your personal email address. It will be used for logging in and future conversations."]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678551ba71a3d4a110006e7b", "discriminatorID": "678551ba71a3d4a110006e7a", "text": [{"text": ["Please create a simple, memorable PIN with a maximum of 4 digits. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67855644296fa7851c53a6cb", "discriminatorID": "67855644296fa7851c53a6ca", "text": [{"text": ["", {"variableID": "67855638296fa7851c53a6bf"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3c50cfcf7818a133b78", "discriminatorID": "6785a3c50cfcf7818a133b77", "text": [{"text": ["You have successfully registered! "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ee990b87dbe9ac5d8c95", "discriminatorID": "6787ee990b87dbe9ac5d8c94", "text": [{"text": ["", {"text": [{"text": ["Click Here!"], "attributes": {"color": {"a": 1, "b": 255, "g": 125, "r": 57}, "__type": "text"}}], "attributes": {"url": "https://hackathonchatbotcreator.infinityfreeapp.com?id={business_ID_from_airtable}", "__type": "link"}}, "  "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67898920631a401e6d1a697d", "discriminatorID": "67898920631a401e6d1a697c", "text": [{"text": ["", {"variableID": "6786f5597a74e1b822c86e6a"}, " "]}, {"text": ["", {"variableID": "6786f5677a74e1b822c86e7a"}, " "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678edea6771abeed1aac1d36", "discriminatorID": "678edea6771abeed1aac1d35", "text": [{"text": ["Make sure you use the link provided exactly as it is—don’t modify it. "]}], "condition": null, "delay": null, "createdAt": "2025-01-20T23:39:19.000Z"}], "responseDiscriminators": [{"id": "678db05571fdef590d29e8eb", "channel": "default", "language": "en-us", "responseID": "678db05571fdef590d29e8ea", "variantOrder": ["678db05571fdef590d29e8ec"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678daa9471fdef590d29e600", "channel": "default", "language": "en-us", "responseID": "678daa9471fdef590d29e5ff", "variantOrder": ["678daa9471fdef590d29e601"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678dad7a71fdef590d29e7ad", "channel": "default", "language": "en-us", "responseID": "678dad7a71fdef590d29e7ac", "variantOrder": ["678dad7a71fdef590d29e7ae"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678db4a971fdef590d29ed5f", "channel": "default", "language": "en-us", "responseID": "678db4a971fdef590d29ed5e", "variantOrder": ["678db4a971fdef590d29ed60"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678db52471fdef590d29eda9", "channel": "default", "language": "en-us", "responseID": "678db52471fdef590d29eda8", "variantOrder": ["678db52471fdef590d29edaa"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e4658c97035e0f16ac4cab", "channel": "default", "language": "en-us", "responseID": "66e4658c97035e0f16ac4caa", "variantOrder": ["66e4658c97035e0f16ac4cac"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e4658c97035e0f16ac4cae", "channel": "default", "language": "en-us", "responseID": "66e4658c97035e0f16ac4cad", "variantOrder": ["66e4658c97035e0f16ac4caf"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e880a4ee916c66659cd5ef", "channel": "default", "language": "en-us", "responseID": "66e880a4ee916c66659cd5ee", "variantOrder": ["66e880a4ee916c66659cd5f0"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e9f4e4cc90326d546e9a91", "channel": "default", "language": "en-us", "responseID": "66e9f4e4cc90326d546e9a90", "variantOrder": ["66e9f4e4cc90326d546e9a92"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e9ed3ecc90326d546e95e7", "channel": "default", "language": "en-us", "responseID": "66e9ed3ecc90326d546e95e6", "variantOrder": ["66e9ed3ecc90326d546e95e8", "67854951296fa7851c539e11", "67854951296fa7851c539e12", "67854951296fa7851c539e13", "67854951296fa7851c539e14", "67854951296fa7851c539e15"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "66e495882c6dde44b3c05729", "channel": "default", "language": "en-us", "responseID": "66e495882c6dde44b3c05728", "variantOrder": ["66e495882c6dde44b3c0572a"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785508a296fa7851c53a323", "channel": "default", "language": "en-us", "responseID": "6785508a296fa7851c53a322", "variantOrder": ["6785508a296fa7851c53a324"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678550b8296fa7851c53a343", "channel": "default", "language": "en-us", "responseID": "678550b8296fa7851c53a342", "variantOrder": ["678550b8296fa7851c53a344"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678551ba71a3d4a110006e7a", "channel": "default", "language": "en-us", "responseID": "678551ba71a3d4a110006e79", "variantOrder": ["678551ba71a3d4a110006e7b"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67855644296fa7851c53a6ca", "channel": "default", "language": "en-us", "responseID": "67855644296fa7851c53a6c9", "variantOrder": ["67855644296fa7851c53a6cb"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678596d20cfcf7818a13347a", "channel": "default", "language": "en-us", "responseID": "678596d20cfcf7818a133479", "variantOrder": ["678596d20cfcf7818a13347b"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3c50cfcf7818a133b77", "channel": "default", "language": "en-us", "responseID": "6785a3c50cfcf7818a133b76", "variantOrder": ["6785a3c50cfcf7818a133b78", "6785a3ca0cfcf7818a133b84", "6785a3ca0cfcf7818a133b85", "6785a3ca0cfcf7818a133b86", "6785a3ca0cfcf7818a133b87", "6785a3ca0cfcf7818a133b88"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785a3e30cfcf7818a133b9c", "channel": "default", "language": "en-us", "responseID": "6785a3e30cfcf7818a133b9b", "variantOrder": ["6785a3e30cfcf7818a133b9d", "6785a40e0cfcf7818a133bb8", "6785a40e0cfcf7818a133bb9", "6785a40e0cfcf7818a133bba", "6785a40e0cfcf7818a133bbb", "6785a40e0cfcf7818a133bbc"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67855be7296fa7851c53a96c", "channel": "default", "language": "en-us", "responseID": "67855be7296fa7851c53a96b", "variantOrder": ["67855be7296fa7851c53a96d"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785bfee0cfcf7818a134109", "channel": "default", "language": "en-us", "responseID": "6785bfee0cfcf7818a134108", "variantOrder": ["6785bfee0cfcf7818a13410a"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c025", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c024", "variantOrder": ["6785c2526663774798e2c026", "6785c2526663774798e2c027", "6785c2526663774798e2c028", "6785c2526663774798e2c029", "6785c2526663774798e2c02a", "6785c2526663774798e2c02b"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c02d", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c02c", "variantOrder": ["6785c2526663774798e2c02e", "6785c2526663774798e2c02f", "6785c2526663774798e2c030", "6785c2526663774798e2c031", "6785c2526663774798e2c032", "6785c2526663774798e2c033"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c035", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c034", "variantOrder": ["6785c2526663774798e2c036"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c038", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c037", "variantOrder": ["6785c2526663774798e2c039"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c03b", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c03a", "variantOrder": ["6785c2526663774798e2c03c"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c03e", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c03d", "variantOrder": ["6785c2526663774798e2c03f"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c041", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c040", "variantOrder": ["6785c2526663774798e2c047", "6785c2526663774798e2c042", "6785c2526663774798e2c043", "6785c2526663774798e2c044", "6785c2526663774798e2c045", "6785c2526663774798e2c046"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c049", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c048", "variantOrder": ["6785c2526663774798e2c04a", "6785c2526663774798e2c04b", "6785c2526663774798e2c04c", "6785c2526663774798e2c04d", "6785c2526663774798e2c04e", "6785c2526663774798e2c04f"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c2526663774798e2c051", "channel": "default", "language": "en-us", "responseID": "6785c2526663774798e2c050", "variantOrder": ["6785c2526663774798e2c052"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785c42c6663774798e2c056", "channel": "default", "language": "en-us", "responseID": "6785c42c6663774798e2c055", "variantOrder": ["6785c42c6663774798e2c057"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785cda87c531a8a54422b57", "channel": "default", "language": "en-us", "responseID": "6785cda87c531a8a54422b56", "variantOrder": ["6785cda87c531a8a54422b58"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6785ced37c531a8a54422bfc", "channel": "default", "language": "en-us", "responseID": "6785ced37c531a8a54422bfb", "variantOrder": ["6785ced37c531a8a54422bfd"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786b1f93f7bbb68ff4efd4f", "channel": "default", "language": "en-us", "responseID": "6786b1f93f7bbb68ff4efd4e", "variantOrder": ["6786b1f93f7bbb68ff4efd50"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ad513f7bbb68ff4ef9ba", "channel": "default", "language": "en-us", "responseID": "6786ad513f7bbb68ff4ef9b9", "variantOrder": ["6786ad513f7bbb68ff4ef9bb"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ad5d3f7bbb68ff4ef9ca", "channel": "default", "language": "en-us", "responseID": "6786ad5d3f7bbb68ff4ef9c9", "variantOrder": ["6786ad5d3f7bbb68ff4ef9cb"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3195b700f4503281cb", "channel": "default", "language": "en-us", "responseID": "6786ae3195b700f4503281ca", "variantOrder": ["6786ae3195b700f4503281cc"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3195b700f4503281ce", "channel": "default", "language": "en-us", "responseID": "6786ae3195b700f4503281cd", "variantOrder": ["6786ae3195b700f4503281cf"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae4b95b700f4503281d4", "channel": "default", "language": "en-us", "responseID": "6786ae4b95b700f4503281d3", "variantOrder": ["6786ae4b95b700f4503281d5"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae3b95b700f4503281d1", "channel": "default", "language": "en-us", "responseID": "6786ae3b95b700f4503281d0", "variantOrder": ["6786ae3b95b700f4503281d2"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786ae5c95b700f4503281dd", "channel": "default", "language": "en-us", "responseID": "6786ae5c95b700f4503281dc", "variantOrder": ["6786ae5c95b700f4503281de"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6786b01795b700f4503281ea", "channel": "default", "language": "en-us", "responseID": "6786b01795b700f4503281e9", "variantOrder": ["6786b01795b700f4503281eb"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787c42d6cc78b98ee73a243", "channel": "default", "language": "en-us", "responseID": "6787c42d6cc78b98ee73a242", "variantOrder": ["6787c42d6cc78b98ee73a244"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ec850b87dbe9ac5d8bc0", "channel": "default", "language": "en-us", "responseID": "6787ec850b87dbe9ac5d8bbf", "variantOrder": ["6787ec850b87dbe9ac5d8bc1"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ec920b87dbe9ac5d8bcf", "channel": "default", "language": "en-us", "responseID": "6787ec920b87dbe9ac5d8bce", "variantOrder": ["6787ec920b87dbe9ac5d8bd0"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787cdf76cc78b98ee73a81a", "channel": "default", "language": "en-us", "responseID": "6787cdf76cc78b98ee73a819", "variantOrder": ["6787cdf76cc78b98ee73a81b"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787cebcce3450f6b9c625bc", "channel": "default", "language": "en-us", "responseID": "6787cebcce3450f6b9c625bb", "variantOrder": ["6787cebcce3450f6b9c625bd"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787ee990b87dbe9ac5d8c94", "channel": "default", "language": "en-us", "responseID": "6787ee990b87dbe9ac5d8c93", "variantOrder": ["6787ee990b87dbe9ac5d8c95"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787d68b6cc78b98ee73af82", "channel": "default", "language": "en-us", "responseID": "6787d68b6cc78b98ee73af81", "variantOrder": ["6787d68b6cc78b98ee73af83"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "6787db316cc78b98ee73b35c", "channel": "default", "language": "en-us", "responseID": "6787db316cc78b98ee73b35b", "variantOrder": ["6787db316cc78b98ee73b35d"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678923676fcd91c13b792be5", "channel": "default", "language": "en-us", "responseID": "678923676fcd91c13b792be4", "variantOrder": ["678923676fcd91c13b792be6"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678928bb6fcd91c13b79301d", "channel": "default", "language": "en-us", "responseID": "678928bb6fcd91c13b79301c", "variantOrder": ["678928bb6fcd91c13b79301e"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678929536fcd91c13b793090", "channel": "default", "language": "en-us", "responseID": "678929536fcd91c13b79308f", "variantOrder": ["678929536fcd91c13b793091"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "67898920631a401e6d1a697c", "channel": "default", "language": "en-us", "responseID": "67898920631a401e6d1a697b", "variantOrder": ["67898920631a401e6d1a697d"], "createdAt": "2025-01-20T17:22:02.000Z"}, {"id": "678edea6771abeed1aac1d35", "channel": "default", "language": "en-us", "responseID": "678edea6771abeed1aac1d34", "variantOrder": ["678edea6771abeed1aac1d36"], "createdAt": "2025-01-20T23:39:19.000Z"}], "variables": [{"id": "678daa8e71fdef590d29e5f6", "name": "welcome_message", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678dac9571fdef590d29e71f", "name": "selected_variables", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678dad5871fdef590d29e798", "name": "final_variables", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678daf4f71fdef590d29e88e", "name": "multiple_topic_answer", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678dafe571fdef590d29e8b1", "name": "number_of_topics", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678db44871fdef590d29ecf8", "name": "one_topic_answer", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "sessions", "name": "sessions", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "number", "description": "The number of times a particular user has opened the app.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "user_id", "name": "user_id", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The user's unique ID.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "timestamp", "name": "timestamp", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "UNIX timestamp (number of seconds since January 1st, 1970 at UTC).", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "platform", "name": "platform", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The platform your agent is running on (e.g. \"voiceflow\").", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "locale", "name": "locale", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The locale of the user (eg. en-US, en-CA, it-IT, fr-FR, ...).", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "KBOutput", "name": "KB_response", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "The final answer formed by the AI to be shown to the user.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "intent_confidence", "name": "intent_confidence", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "number", "description": "The confidence interval (measured as a value from 0 to 100) for the most recently matched intent.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "last_response", "name": "last_response", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The agent's last response (text/speak) in a string.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "last_event", "name": "last_event", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "any", "description": "The object containing the last event that the user client has triggered.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "last_utterance", "name": "last_utterance", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "The user's last utterance in a text string.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "vf_memory", "name": "vf_memory", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": true, "datatype": "text", "description": "Last 10 user inputs and agent responses in a string (e.g. \"agent: How can i help?\"\nuser: What's the weather today?).", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "66e87e79517bf2228b6c8657", "name": "optimized_question", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "Optimized version of the users question for the AI to answer. It stores a reformatted question that uses memory.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "66e880de517bf2228b6c8664", "name": "clarifying_question_check_counter", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "How many times the AI has asked a clarifying question. This is used so it doesnt ask too many clarifying questions.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "66e8813c517bf2228b6c8665", "name": "clarifying_question_check", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "Determine if the AI needs to ask a clarifying question.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "66e881bb517bf2228b6c8666", "name": "clarifying_question", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "Clarifying question asked by the AI", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "66e8821d517bf2228b6c8667", "name": "language_JSON", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "Information about the users language. Generated by AI", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "chunks", "name": "chunks", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "Information received from the knowledge base - also known as 'chunks'.", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "67855059296fa7851c53a300", "name": "BusinessID", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6785521c296fa7851c53a3f4", "name": "user_password", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "67855638296fa7851c53a6bf", "name": "email_verification", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786b2353f7bbb68ff4efe00", "name": "raw_business_services", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6785bf090cfcf7818a1340d2", "name": "second_email_verification", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6785cdfd7c531a8a54422b76", "name": "user_intent", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786ad953f7bbb68ff4ef9f1", "name": "raw_business_name", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786aecd3f7bbb68ff4efaee", "name": "raw_business_phone_number", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786af583f7bbb68ff4efb1f", "name": "raw_business_address", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786af973f7bbb68ff4efb58", "name": "raw_business_operating_hours", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f5597a74e1b822c86e6a", "name": "clean_business_name", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f5617a74e1b822c86e6e", "name": "clean_business_operating_hours", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f5677a74e1b822c86e7a", "name": "clean_business_phone_number", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786af113f7bbb68ff4efb0f", "name": "raw_business_email", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f52d7a74e1b822c86e5d", "name": "clean_business_address", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f5307a74e1b822c86e5e", "name": "clean_business_email", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6786f5737a74e1b822c86e7e", "name": "clean_business_services", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6787c5b76cc78b98ee73a36f", "name": "business_name_confirmation", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6787c8356cc78b98ee73a5a0", "name": "business_name_conditional", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6787cef36cc78b98ee73a8d4", "name": "phone_number_confirmation", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6787da246cc78b98ee73b23f", "name": "phone_number_conditional", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6787e1790b87dbe9ac5d83e2", "name": "business_ID_from_airtable", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "6789231f6fcd91c13b792b8b", "name": "deployment_id", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678986bc631a401e6d1a6853", "name": "spa_name", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-20T17:22:01.000Z", "updatedAt": "2025-01-20T17:22:01.000Z", "updatedByID": 1479280}, {"id": "678fa2de6fad26319ac88255", "name": "multiple_topic_answer_verification", "createdByID": 1479280, "folderID": null, "color": "#515A63", "isArray": false, "isSystem": false, "datatype": "any", "description": "", "defaultValue": null, "createdAt": "2025-01-21T13:36:30.000Z", "updatedAt": "2025-01-21T13:36:30.000Z", "updatedByID": 1479280}], "workflows": [{"id": "66df755be937eb000784abb2", "name": "General Flow", "createdByID": 1479280, "folderID": null, "status": null, "isStart": true, "diagramID": "64dbb6696a8fab0013dba194", "assigneeID": null, "description": "", "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-21T23:22:57.000Z", "updatedByID": 1479280}], "attachments": [], "cardButtons": [], "prompts": [{"id": "672ae8ad671f984162a8aa34", "name": "No information found", "createdByID": 1479280, "folderID": "672b8d3788690850a0044715", "description": "Create a response if no information is found.", "settings": {"model": "gpt-3.5-turbo"}, "messageOrder": ["672ae8ad671f984162a8aa35", "672ae8ad671f984162a8aa36"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "672ae903671f984162a8aa3a", "name": "Clarifying question check", "createdByID": 1479280, "folderID": "672b8cf088690850a00446e6", "description": "Look at the information found and determine if a clarifying question is needed.", "settings": {"model": "gpt-4o-mini"}, "messageOrder": ["672ae903671f984162a8aa3b", "672ae903671f984162a8aa3c"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "672ae92e671f984162a8aa40", "name": "Optimize question", "createdByID": 1479280, "folderID": "672b8d3788690850a0044715", "description": "This prompt optimizes the users question using the conversation memory.", "settings": {"model": "gpt-3.5-turbo"}, "messageOrder": ["672ae92e671f984162a8aa41", "672ae92e671f984162a8aa42", "672b8adc039d320007ef90d5", "672b8adc039d320007ef90d6", "672b8b4d039d320007ef90e2", "672b8c1f88690850a004469b", "672b8b4d039d320007ef90e3"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "672ae94c671f984162a8aa43", "name": "Create a clarifying question", "createdByID": 1479280, "folderID": "672b8cf088690850a00446e6", "description": "Create a clarifying question based on the users initial question.", "settings": {"model": "gpt-4o-mini"}, "messageOrder": ["672ae94c671f984162a8aa44", "672ae94c671f984162a8aa45"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "672ae8f2671f984162a8aa37", "name": "Create answer", "createdByID": 1479280, "folderID": "672b8d3788690850a0044715", "description": "Using the information found from the 'search knowledge base' step, this prompt creates a detailed answer for the user.", "settings": {"model": "gpt-4o-mini", "maxTokens": 500}, "messageOrder": ["672ae8f2671f984162a8aa38", "672b917c88690850a0044a49", "672ae8f2671f984162a8aa39"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}, {"id": "672ae961671f984162a8aa46", "name": "Language Classifier", "createdByID": 1479280, "folderID": "672b8d3788690850a0044715", "description": "Classify the language that the user is speaking.", "settings": {"model": "gpt-3.5-turbo"}, "messageOrder": ["672ae961671f984162a8aa47", "672ae961671f984162a8aa48", "672b8d7c039d320007ef912d", "672b8d7c039d320007ef912e", "672b8d7e039d320007ef912f", "672b8d7e039d320007ef9130", "672b8d80039d320007ef9131", "672b8d80039d320007ef9132", "672b8de7039d320007ef9139", "672b8de7039d320007ef913a", "672b8dff039d320007ef913e", "672b8dff039d320007ef913f", "672b8e11039d320007ef9189", "672b8e11039d320007ef918a"], "createdAt": "2025-01-20T17:22:02.000Z", "updatedAt": "2025-01-20T17:22:02.000Z", "updatedByID": 1479280}], "promptMessages": [{"id": "672b8adc039d320007ef90d6", "promptID": "672ae92e671f984162a8aa40", "pairID": "672b8adc039d320007ef90d5", "type": "role", "data": {"role": "user", "content": [{"text": ["User's last response:"]}, {"text": ["What services are offered for users?"]}]}, "createdAt": "2024-11-06T15:27:24.000Z"}, {"id": "672b8adc039d320007ef90d5", "promptID": "672ae92e671f984162a8aa40", "pairID": "672b8adc039d320007ef90d6", "type": "role", "data": {"role": "agent", "content": [{"text": ["What are the specific details of common processes for users, including relevant terms for users based on specific factors, and potential challenges that users may face when following common processes? Additionally, are there any exceptions or special considerations such as specific circumstances that may impact the user process?"]}]}, "createdAt": "2024-11-06T15:27:24.000Z"}, {"id": "672b8b4d039d320007ef90e3", "promptID": "672ae92e671f984162a8aa40", "pairID": "672b8b4d039d320007ef90e2", "type": "role", "data": {"role": "user", "content": [{"text": ["User's last response:\n$\"", {"variableID": "last_utterance"}, "\"\n\nNow, take a deep breath and generate a detailed, comprehensive question that targets the most relevant information in the knowledge base, repeating key terms where appropriate and emphasizing the user perspective as specified in their query.\n\nGenerated question:"]}]}, "createdAt": "2024-11-06T15:29:17.000Z"}, {"id": "672b8b4d039d320007ef90e2", "promptID": "672ae92e671f984162a8aa40", "pairID": "672b8b4d039d320007ef90e3", "type": "role", "data": {"role": "agent", "content": [{"text": ["What are the different services offered specifically for users, including key details, features, and any limitations or exclusions for user service plans? How do these user service plans work within the industry platform, and what should users know about the service when using it through the platform?"]}]}, "createdAt": "2024-11-06T15:29:17.000Z"}, {"id": "672b8d7c039d320007ef912e", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d7c039d320007ef912d", "type": "role", "data": {"role": "user", "content": [{"text": ["Combien coûte votre service ?"]}]}, "createdAt": "2024-11-06T15:38:37.000Z"}, {"id": "672b8d7c039d320007ef912d", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d7c039d320007ef912e", "type": "role", "data": {"role": "agent", "content": [{"text": ["{\"language\": \"English\", \"confidence\": \"high\", \"reasoning\": \"The utterance is in clear, grammatically correct English.\"}"]}]}, "createdAt": "2024-11-06T15:38:37.000Z"}, {"id": "672b8d7e039d320007ef9130", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d7e039d320007ef912f", "type": "role", "data": {"role": "user", "content": [{"text": ["¿Cuál es el precio de su servicio?"]}]}, "createdAt": "2024-11-06T15:38:39.000Z"}, {"id": "672b8d7e039d320007ef912f", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d7e039d320007ef9130", "type": "role", "data": {"role": "agent", "content": [{"text": ["{\"language\": \"French\", \"confidence\": \"high\", \"reasoning\": \"The utterance is in French, using correct French grammar and vocabulary.\"}"]}]}, "createdAt": "2024-11-06T15:38:39.000Z"}, {"id": "672b8d80039d320007ef9132", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d80039d320007ef9131", "type": "role", "data": {"role": "user", "content": [{"text": ["Hello! Quel est le prix de votre service ?"]}]}, "createdAt": "2024-11-06T15:38:40.000Z"}, {"id": "672b8d80039d320007ef9131", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8d80039d320007ef9132", "type": "role", "data": {"role": "agent", "content": [{"text": ["{\"language\": \"Spanish\", \"confidence\": \"high\", \"reasoning\": \"The sentence is in Spanish, asking about the price of the service.\"}"]}]}, "createdAt": "2024-11-06T15:38:40.000Z"}, {"id": "672b8de7039d320007ef913a", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8de7039d320007ef9139", "type": "role", "data": {"role": "user", "content": [{"text": ["👍"]}]}, "createdAt": "2024-11-06T15:40:23.000Z"}, {"id": "672b8de7039d320007ef9139", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8de7039d320007ef913a", "type": "role", "data": {"role": "agent", "content": [{"text": ["{\"language\": \"French\", \"confidence\": \"medium\", \"reasoning\": \"While 'Hello' is English, the rest of the sentence is in French, indicating the user is primarily communicating in French.\"}"]}]}, "createdAt": "2024-11-06T15:40:23.000Z"}, {"id": "672b8dff039d320007ef913f", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8dff039d320007ef913e", "type": "role", "data": {"role": "user", "content": [{"text": ["What's the キャンセル policy?"]}]}, "createdAt": "2024-11-06T15:40:48.000Z"}, {"id": "672b8dff039d320007ef913e", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8dff039d320007ef913f", "type": "role", "data": {"role": "agent", "content": [{"text": ["{\"language\": \"unknown\", \"confidence\": \"low\", \"reasoning\": \"The user only used an emoji, which is not specific to any language.\"}"]}]}, "createdAt": "2024-11-06T15:40:48.000Z"}, {"id": "672b8e11039d320007ef918a", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8e11039d320007ef9189", "type": "role", "data": {"role": "user", "content": [{"text": ["Now, analyze the following:\n\nLast utterance: $\"", {"variableID": "last_utterance"}, "\"\nAgent's last response: $\"", {"variableID": "last_response"}, "\"\nPreviously set language: $\"", {"entityID": "66e8821d517bf2228b6c8667"}, "\""]}]}, "createdAt": "2024-11-06T15:41:06.000Z"}, {"id": "672b8e11039d320007ef9189", "promptID": "672ae961671f984162a8aa46", "pairID": "672b8e11039d320007ef918a", "type": "role", "data": {"role": "agent", "content": [{"text": [" {\"language\": \"English\", \"confidence\": \"medium\", \"reasoning\": \"The sentence structure is English, but it includes a Japanese word. The primary language appears to be English with some Japanese vocabulary.\"}"]}]}, "createdAt": "2024-11-06T15:41:06.000Z"}, {"id": "672b8c1f88690850a004469b", "promptID": "672ae92e671f984162a8aa40", "pairID": null, "type": "variable", "data": {"role": "user", "content": [{"text": [{"variableID": "vf_memory"}]}]}, "createdAt": "2024-11-06T15:32:47.000Z"}, {"id": "672b917c88690850a0044a49", "promptID": "672ae8f2671f984162a8aa37", "pairID": null, "type": "variable", "data": {"role": "user", "content": [{"text": [{"variableID": "vf_memory"}]}]}, "createdAt": "2024-11-06T15:55:41.000Z"}, {"id": "672ae8ad671f984162a8aa35", "promptID": "672ae8ad671f984162a8aa34", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are a customer support bot that tries to help the user. Guide them towards asking for support."]}]}, "createdAt": "2024-11-06T03:55:25.000Z"}, {"id": "672ae8ad671f984162a8aa36", "promptID": "672ae8ad671f984162a8aa34", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["Speak directly to the end customer. Do not include anything outside of your answer. Ensure that you speak to users in the language identied below:\n\nlanguage: ", {"entityID": "66e8821d517bf2228b6c8667"}, " "]}]}, "createdAt": "2024-11-06T03:55:25.000Z"}, {"id": "672ae8f2671f984162a8aa38", "promptID": "672ae8f2671f984162a8aa37", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are an AI customer support agent."]}]}, "createdAt": "2024-11-06T03:56:35.000Z"}, {"id": "672ae8f2671f984162a8aa39", "promptID": "672ae8f2671f984162a8aa37", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["You are an AI customer support bot helping a customer with their question. When communicating with guests, follow these guidelines:\n\nUse the following step-by-step reasoning.\n\n1. Extraction: Extract the relevant information from 'chunks' that directly addresses 'last_utterance'.\n\n2. Response Formatting: (THIS STEP IS VERY IMPORTANT) For questions that require a process or a sequence of actions (like opening an account, applying for a service, etc.), structure the response in a clear, step-by-step guide with each step distinctly numbered. For other types of questions, provide a concise and direct answer.\n\n3. Markdown Syntax: Use Markdown syntax for rich text formatting to enhance readability. For process-oriented responses, use a numbered list format. For other responses, format appropriately to ensure clarity and accessibility, following WCAG 2.1 guidelines.\n\n5. Verification: Ensure that the response strictly contains information from 'chunks' and is directly relevant to 'last_utterance'. Do not incorporate any additional or external information.\n\n6. Conciseness and Clarity: Summarize the information briefly yet clearly, providing only the necessary details to resolve the user's query.\n\nAdditional Instructions:\n\nDo not include links to URLs or information not present in 'chunks'.\nMaintain a friendly, conversational tone, using \"I\" and \"we\" to foster a connection with the user.\n\nSpecifically, emphasize the step-by-step format for any instructional or process-related queries.\n\nInput data:\n1. Customers question: \"", {"variableID": "last_utterance"}, "\"\n2. Provided details: \"", {"variableID": "chunks"}, "\"\n3. User's language: \"", {"entityID": "66e8821d517bf2228b6c8667"}, "\"  (if unknown default to English)\n\nNow, take a deep breath and respond to the guest's question in their language (", {"entityID": "66e8821d517bf2228b6c8667"}, "),  or in English if the language is unknown. Remember to follow all the guidelines provided above. "]}]}, "createdAt": "2024-11-06T03:56:35.000Z"}, {"id": "672ae903671f984162a8aa3b", "promptID": "672ae903671f984162a8aa3a", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are an expert AI assistant trained to analyze customer inquiries and determine if they require clarification. Your role is to ensure efficient and accurate responses by identifying when additional information is needed from the customer."]}]}, "createdAt": "2024-11-06T03:56:51.000Z"}, {"id": "672ae903671f984162a8aa3c", "promptID": "672ae903671f984162a8aa3a", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["# Clarification Assessment Prompt Template\n\nYou are an expert AI assistant trained to analyze customer inquiries and determine if they require clarification. Your role is to ensure efficient and accurate responses by identifying when additional information is needed from the customer.\n\n## Instructions:\n1. Carefully analyze the 'user's question' for specific details such as product or service types, policy names, or other relevant specifics that provide clear context.\n2. Review the 'chunks' to see if they contain information that directly addresses the specific details in the user's question.\n3. Output '~' if:\n   - The user's question contains specific details that are clearly addressed in the 'chunks'.\n   - The query aligns well with a specific part of the 'chunks' that provides a comprehensive answer.\n4. Output '#' only if:\n   - The query is genuinely broad or vague, lacking specific details that would allow for a direct answer from the 'chunks'.\n   - The 'chunks' contain multiple categories or types of information that could potentially answer the question, but the user's intent is unclear.\n   - There's a crucial piece of information missing from the user's question that is necessary to provide an accurate answer, and this information is not implied or obvious from the context given.\n5. If outputting '#', specify the type of information or category that would help better address their question, based on the 'chunks'.\n\n## Output format:\n[Decision: '~' or '#', (if '#') then clarification is required. Specify the type of information or category that would help better address their question.]\n\n## Important:\n- Focus on whether the available information in the chunks is sufficient to answer the user's specific question accurately.\n- Consider whether the user's question is too broad or vague to be answered comprehensively with the information provided in the chunks.\n- Assess if there are multiple possible interpretations of the user's question that would lead to significantly different answers based on the information in the chunks.\n\n## Input data:\n1. User's question: \n", {"variableID": "last_utterance"}, "\n\n  2. <PERSON><PERSON>: \n", {"variableID": "chunks"}, "\n\nNow, take a deep breath and provide your assessment based on the instructions above."]}]}, "createdAt": "2024-11-06T03:56:51.000Z"}, {"id": "672ae92e671f984162a8aa41", "promptID": "672ae92e671f984162a8aa40", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are a question optimizer. Keep your questions down to one sentence"]}]}, "createdAt": "2024-11-06T03:57:34.000Z"}, {"id": "672ae92e671f984162a8aa42", "promptID": "672ae92e671f984162a8aa40", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["You are an AI assistant specialized in generating optimal questions for retrieval augmented generation (RAG) in the context of user queries and intent. Your goal is to create a detailed question that will yield the most relevant and useful information from a knowledge base to enhance the quality of responses to user queries.\n\n##Task\n\nGenerate a single, comprehensive question that best captures the information needed to address the user’s query or intent, considering the following guidelines:\n\n\t1.\tAnalyze the user’s last response and the conversation history to identify the core topic, intent, or information need.\n\t2.\tCraft a question that is:\n\t•\tHighly specific and detailed, targeting exact information in the knowledge base\n\t•\tComprehensive, covering multiple aspects of the topic mentioned by the user\n\t•\tPhrased naturally, but including key terms multiple times for emphasis\n\t•\tFocused strongly on the user’s perspective if specified in the conversation\n\t3.\tInclude relevant keywords and phrases throughout the question, repeating important terms where appropriate. Use specific phrasing that emphasizes the user perspective.\n\t4.\tStructure the question to cover multiple related aspects, using phrases like “including”, “as well as”, or “such as”.\n\t5.\tIncorporate specific terms that are likely to appear in relevant documents.\n\t6.\tUse repetitive but varied phrasing to emphasize key points and increase the likelihood of matching relevant documents.\n\t7.\tFor policy-related questions, mention potential exceptions or special circumstances only if they were part of the user’s query.\n\t8.\tFocus solely on the aspects mentioned by the user. Do not include additional topics or comparisons unless specifically requested.\n\n\n##Output\n\nProvide only the generated question, without quotation marks or any additional text.\n\n## Input Data\n"]}, {"text": ["User's last response:"]}, {"text": ["How do common processes work for users?"]}]}, "createdAt": "2024-11-06T03:57:34.000Z"}, {"id": "672ae94c671f984162a8aa44", "promptID": "672ae94c671f984162a8aa43", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are an AI Support Agent. Your task is to create a brief, friendly clarifying question that fits naturally into an ongoing conversation. "]}]}, "createdAt": "2024-11-06T03:58:05.000Z"}, {"id": "672ae94c671f984162a8aa45", "promptID": "672ae94c671f984162a8aa43", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["You are an AI Support Agent. Your task is to create a brief, friendly clarifying question that fits naturally into an ongoing conversation. You will be given the following data: 'guidelines for the question', the 'user's question' that needs a response, and the 'information retrieved from a knowledge base'.\n\n## Instructions:\n1. Analyze the user's question and the provided information.\n2. Identify the key ambiguity or missing detail in the user's query.\n3. Construct a single, clear, and concise clarifying question related to providing support.\n4. Use a friendly tone, but prioritize brevity and directness.\n5. You may use one appropriate emoji if it adds value without increasing length significantly.\n6. Guide the user towards a precise response.\n7. Avoid multiple questions or unnecessary explanations.\n8. Focus on the single most important aspect that requires clarification.\n9. Begin with a very brief acknowledgment, then ask your clarifying question.\n10. Aim for a total response length of 15-20 words maximum.\n\n## Example Responses:\n- \"Sure. Are you a  guest or host? The cancellation process differs for each.\"\n- \"Uh-huh, okay. Can you tell me which  plan are you asking about?\"\n- \"Sure. And is this for a domestic or international  rental? \"\n\n## Input data:\nGuidelines for the question: $\"", {"entityID": "66e8813c517bf2228b6c8665"}, "\"\n \nThe user's question: $\"", {"variableID": "last_utterance"}, "\"\n Information from knowledge base: $\"", {"variableID": "chunks"}, "\"\nThe user's language: $\"", {"entityID": "66e8821d517bf2228b6c8667"}, "\"\n \nNow, create your brief, friendly clarifying question in the user's language that fits naturally into the ongoing dialogue:"]}]}, "createdAt": "2024-11-06T03:58:05.000Z"}, {"id": "672ae961671f984162a8aa47", "promptID": "672ae961671f984162a8aa46", "pairID": null, "type": "role", "data": {"role": "system", "content": [{"text": ["You are a language classifier"]}]}, "createdAt": "2024-11-06T03:58:26.000Z"}, {"id": "672ae961671f984162a8aa48", "promptID": "672ae961671f984162a8aa46", "pairID": null, "type": "role", "data": {"role": "user", "content": [{"text": ["Analyze the conversation history and the last utterance from a user to determine their likely language. Consider the previously determined language, if available.\n\nClassify the user's language into a specific language name (e.g., English, French, Spanish) or \"unknown\" if the language cannot be determined.\n\nRespond with a JSON string containing the classification, confidence level (high, medium, or low), and a brief explanation of your reasoning. \n\nJSON structure format:\n\n{\n  \"language\": string,\n  \"confidence\": string,\n  \"reasoning\": string\n}\n\n\nExample:\n\nUser: \"How much does it cost to use your service?\""]}]}, "createdAt": "2024-11-06T03:58:26.000Z"}], "events": [], "functions": [], "functionPaths": [], "functionVariables": [], "project": {"type": "chat", "name": "Component B: Dynamic Deployed Chatbot", "image": "https://cm4-production-assets.s3.amazonaws.com/1737400649015-ottomator-logo.png", "teamID": "9YnzeOOqjb", "members": [], "platform": "webchat", "_version": 1.2, "linkType": "STRAIGHT", "creatorID": 1479280, "updatedBy": 1479280, "apiPrivacy": "public", "platformData": {"invocationName": "template project general"}, "customThemes": [{"name": "Conditional", "palette": {"50": "#f7ece0", "100": "#f2e0ca", "200": "#ecd0ab", "300": "#e5c18c", "400": "#ccac7c", "500": "#b3976c", "600": "#99805b", "700": "#796547", "800": "#584933", "900": "#372d1e"}, "standardColor": "#b3976c"}, {"name": "", "palette": {"50": "#f7ebed", "100": "#f1dee1", "200": "#ebcdd2", "300": "#e5bcc3", "400": "#dca1ab", "500": "#d58493", "600": "#ce6178", "700": "#a64a5e", "800": "#7a3544", "900": "#4e1f29"}, "standardColor": "#d58493"}], "aiAssistSettings": {"aiPlayground": true}, "privacy": "private", "_id": "678e863973475b48413aba13", "updatedAt": "2025-01-21T21:37:23.553Z", "devVersion": "678e863973475b48413aba14", "liveVersion": "678e863973475b48413aba15"}, "_version": "1.2", "secrets": [], "variableStates": []}