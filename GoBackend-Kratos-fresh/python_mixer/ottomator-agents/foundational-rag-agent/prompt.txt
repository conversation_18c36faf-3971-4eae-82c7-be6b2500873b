I'd like to build a RAG AI agent with Pydantic AI and Supabase, using the following MCP servers:

Be sure to review the planning and task files.
This project should create a simple RAG system with:

A document ingestion pipeline that:

Accepts local TXT and PDF files
Uses a simple chunking approach
Generates embeddings using OpenAI
Stores documents and vectors in Supabase with pgvector


A Pydantic AI agent that:

Has a tool for knowledge base search
Uses OpenAI models for response generation
Integrates retrieved contexts into responses


A Streamlit UI that:

Allows document uploads
Provides a clean interface for querying the agent
Displays responses with source attribution
Use @streamlit_ui_example.py to see exactly how to integrate Streamlit with a Pydantic AI agent.


Use the Supabase MCP server to create the necessary database tables with the pgvector extension enabled. For document processing, keep it simple using PyPDF2 for PDFs rather than complex document processing libraries.

Use the Crawl4AI RAG MCP server that already has the Pydantic AI and Supabase Python documentation available. So just perform RAG queries whenever necessary. Also use the Brave MCP server to search the web for supplemental docs/examples to aid in creating the agent.