PYTHON_VERSION=3.10
#
# App parameters
# APP_NAME="GSAM Ideator and Code Generator"
# MAKER_MAME="<PERSON>"
#
##############################
#
# AI Parameters
#
# OpenAI
OPENAI_API_KEY=
# HuggingFace
HUGGINGFACE_API_KEY=
# Together AI
TOGETHER_AI_API_KEY=
# AI/ML API
AIMLAPI_API_KEY=
# Groq
GROQ_API_KEY=
# X AI
XAI_API_KEY=
# OpenRouter
OPENROUTER_API_KEY=
# Rhymes
RHYMES_ARIA_API_KEY=
RHYMES_ALLEGRO_API_KEY=
# Ollama
#OLLAMA_BASE_URL=localhost:11434
# Nvidia
NVIDIA_API_KEY=
#
##############################
#
# Database Parameters
#
# DB_TYPE=json
DB_TYPE=mongodb
#
# JSON database parameters
JSON_DB_PATH=./db/conversations.json
#
# MongoDB database parameters
MONGODB_URI=mongodb+srv://<user>:<password>@<cluster>.mongodb.net
MONGODB_DB_NAME=mongodb_db_name
MONGODB_COLLECTION_NAME=mongodb_collection_name

# GSAM Agent parameters

# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# For the Postgres version (sample_postgres_agent.py), set your database connection URL.
# Format: postgresql://[user]:[password]@[host]:[port]/[database_name]
# Example: postgresql://postgres:mypassword@localhost:5432/mydb
# For Supabase Postgres connection, you can find this in Database settings -> Connection string -> URI
DATABASE_URL=

# Set this bearer token to whatever you want. This will be changed once the agent is hosted for you on the Studio!
API_BEARER_TOKEN=

# Default LLM provider
DEFAULT_LLM_PROVIDER=openrouter

##############################
#
# LLM defaults
#
# DEFAULT_LLM_PROVIDER=openai
# DEFAULT_LLM_PROVIDER=groq
# DEFAULT_LLM_PROVIDER=nvidia
# DEFAULT_LLM_PROVIDER=ollama
# DEFAULT_LLM_PROVIDER=huggingface
# DEFAULT_LLM_PROVIDER=together_ai  
# DEFAULT_LLM_PROVIDER=rhymes
# DEFAULT_LLM_PROVIDER=openrouter
#
# DEFAULT_TEXT_TO_VIDEO_PROVIDER=rhymes
#
# DEFAULT_TEXT_TO_IMAGE_PROVIDER=openai
# DEFAULT_TEXT_TO_IMAGE_PROVIDER=huggingface
#
##############################

# OpenRouter configuration
OPENROUTER_API_KEY=
OPENROUTER_MODEL_NAME=google/gemini-2.0-flash-exp:free
