Create content for PowerPoint slides for a presentation including the main information for each slide and prompts for generating corresponding images.

# Output Format

Provide the output as a detailed summary of slide content with image prompts in the following JSON format:

```json
{
  "slides": [
    {
      "title": "[Title of Slide]",
      "content": "[Relevant content]",
      "speaker_notes": "[Extended explanation intended only for the presenter]",
      "image_prompt": "[Image prompt for the generated image]"
    }
  ]
}
```

Include all slide details as distinct items under the "slides" array. Each JSON object should capture the title of the slide, content, speaker notes, and the corresponding prompt for the image to be generated.

# Notes

- Ensure that each slide content is succinct but informative.
- Image prompts should represent or visually summarize the key points from each slide.
- Use individual placeholders relevant to the generated `{title}, {subtitle},` or other brackets to ensure all content points are well-covered.
