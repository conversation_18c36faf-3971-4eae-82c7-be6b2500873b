from typing import List, Optional, Dict, Any
import os

from fastapi import FastAP<PERSON>, HTTPException, Security, Depends
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi.middleware.cors import CORSMiddleware
from supabase import create_client, Client
from pydantic import BaseModel
from dotenv import load_dotenv

from gsam_ottomator_agent.gsam_agent_lib import run_agent

DEBUG = False

# Load environment variables
load_dotenv()

security = HTTPBearer()

# Supabase setup
supabase: Client = create_client(
    os.getenv("SUPABASE_URL"),
    os.getenv("SUPABASE_SERVICE_KEY")
)


def init_fastapi_app():
    # Initialize FastAPI app
    app = FastAPI()
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    return app


# Request/Response Models


class AgentRequest(BaseModel):
    """Request model for the agent."""
    query: str
    user_id: str
    request_id: str
    session_id: str


class AgentResponse(BaseModel):
    success: bool


def verify_token(
    credentials: HTTPAuthorizationCredentials = Security(security)
) -> bool:
    """Verify the bearer token against environment variable."""
    expected_token = os.getenv("API_BEARER_TOKEN")
    if not expected_token:
        raise HTTPException(
            status_code=500,
            detail="API_BEARER_TOKEN environment variable not set"
        )
    if credentials.credentials != expected_token:
        raise HTTPException(
            status_code=401,
            detail="Invalid authentication token"
        )
    return True


async def fetch_conversation_history(session_id: str, limit: int = 10
                                     ) -> List[Dict[str, Any]]:
    """Fetch the most recent conversation history for a session."""
    try:
        response = supabase.table("messages") \
            .select("*") \
            .eq("session_id", session_id) \
            .order("created_at", desc=True) \
            .limit(limit) \
            .execute()
        # Convert to list and reverse to get chronological order
        messages = response.data[::-1]
        return messages
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch conversation history: {str(e)}")


async def store_message(session_id: str, message_type: str, content: str,
                        data: Optional[Dict] = None):
    """Store a message in the Supabase messages table."""
    message_obj = {
        "type": message_type,
        "content": content
    }
    if data:
        message_obj["data"] = data

    try:
        supabase.table("messages").insert({
            "session_id": session_id,
            "message": message_obj
        }).execute()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to store message: {str(e)}")


async def gsam_supabase_agent(
    request: AgentRequest,
    authenticated: bool = Depends(verify_token),
    http_request: dict = None,
):
    try:
        # Fetch conversation history from the DB
        conversation_history = await fetch_conversation_history(
            request.session_id)

        # Convert conversation history to format expected by agent
        # This will be different depending on your framework (Pydantic AI,
        # LangChain, etc.)
        messages = []
        for msg in conversation_history:
            msg_data = msg["message"]
            msg_type = msg_data["type"]
            msg_content = msg_data["content"]
            msg = {"role": msg_type, "content": msg_content}
            messages.append(msg)

        # Store user's query
        await store_message(
            session_id=request.session_id,
            message_type="human",
            content=request.query
        )
        """
        TODO:
        This is where you insert the custom logic to get the response from
        your agent.
        Your agent can also insert more records into the database to
        communicate actions/status as it is handling the user's
        question/request.
        Additionally:
            - Use the 'messages' array defined about for the chat history.
              This won't include the latest message from the user.
            - Use request.query for the user's prompt.
            - Use request.session_id if you need to insert more messages into
              the DB in the agent logic.
        """
        agent_response = run_agent(request.query, messages, http_request)

        # Store agent's response
        await store_message(
            session_id=request.session_id,
            message_type="ai",
            content=agent_response,
            data={"request_id": request.request_id}
        )

        return AgentResponse(success=True)

    except Exception as e:
        print(f"Error processing request: {str(e)}")
        # Store error message in conversation
        await store_message(
            session_id=request.session_id,
            message_type="ai",
            content="I apologize, but I encountered an error processing"
                    " your request.",
            data={"error": str(e), "request_id": request.request_id}
        )
        if DEBUG:
            raise e
        return AgentResponse(success=False)
