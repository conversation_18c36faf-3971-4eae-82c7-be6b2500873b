# Web Search AI Agent

Author: [<PERSON><PERSON>](https://www.youtube.com/@tuanmedeiros)

**Platform:** n8n (you can import the .json file into your own n8n to check out the flow)

An AI-driven solution that searches multiple websites on the internet, requiring only a URL from the user. This agent streamlines the process of retrieving and analyzing data, delivering concise, relevant information to support research, content creation, and more.

## Features

- Searches multiple websites with a single URL input  
- Retrieves and consolidates key information  
- Ideal for research, content exploration, and data analysis  
- Reduces manual browsing time and effort  

## How It Works

1. Receives a user-provided URL to define the target site(s)  
2. Crawls the specified website(s) for relevant information  
3. Analyzes and summarizes the retrieved data  
4. Delivers concise, actionable insights for the user  

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.


