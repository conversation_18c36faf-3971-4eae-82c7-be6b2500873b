{"name": "Web Scraper Multi-Page", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields1').item.json.session_id}}", "tableName": "messages", "contextWindowLength": 10}, "id": "f751dcb5-9bf1-42d3-a2f2-aa344ca7ec75", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1040, 300], "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"promptType": "define", "text": "={{$('Prep Input Fields1').item.json.query}}", "options": {"systemMessage": "=# Role\n\nYou are an agent specialized in knowledge that the user asks about.\n\n# Goal\n\n- Make complex subjects easy to understand.\n- Create a teaching methodology to convey the content in the most didactic way possible.\n- Find patterns in the content and organize it chronologically to make it even more didactic.\n\n# Interaction with user\n\n- Ask the user if they want to know information from a Website.\n- Use the website_scraping tool to obtain the correct information.\n- After acquiring the knowledge, clarify the user's doubts with the acquired knowledge.\n\n# Task\n\nThe user will send you one or more Website links, use the website_scraping tool to search for the correct information to accurately answer the user's questions about the topics.\n\n# Tools\n\n- website_scraping\n\n# Link identification\n\nCorrectly identify and separate each link you receive, usually the URL is composed of: network communication protocol, a subdomain, a domain and its extension.\nE.g.:\n<url_example>\nI need help with this content:\nhttps://edition.cnn.com/world\nhttps://www.reddit.com/r/PromptEngineering/\nhttps://bloomberg.com/\n</url_example>\n\n# Time\nCurrent date and time: {{ $now.toLocal().format('DDDD t') }}"}}, "id": "fd05646e-5b48-4fa3-8c54-56f9abc17fc0", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1020, 60]}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": ""}]}}}, "id": "557ac9dd-615b-480f-b828-1c0f3af5c081", "name": "Respond to Webhook1", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1580, 60]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "4b0be839-fa24-44b8-9983-428a8ea769ff", "name": "Prep Output Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1400, 60]}, {"parameters": {"httpMethod": "POST", "path": "api/agent/web-search/v1", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "e98ebb29-2c67-4f73-8021-ac543e1cd794", "name": "Webhook1", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [500, 60], "webhookId": "b3326f84-3efd-431e-84dd-7911daee0c41", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash-exp", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [920, 300], "id": "961e7630-8b92-4199-84dd-cece2aa18d87", "name": "Google Gemini Chat Model", "credentials": {"googlePalmApi": {"id": "mOuOVUXJMjWz8V9h", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [500, 520], "id": "f13f206d-b383-4791-9147-3e1c63618c41", "name": "Execute Workflow Trigger"}, {"parameters": {"content": "## Agent Web Scraper Multi-Pages v1\n\nThe agent searches multiple websites on the internet; the user simply needs to enter the URL.\n", "height": 240, "width": 460, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "********-b9d6-4cd1-8b5e-d7dc3941ce38", "name": "<PERSON><PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "594d727f-2406-4e56-a4a3-85af2c8cb66d", "name": "Prep Input Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [780, 60]}, {"parameters": {"name": "website_scraping", "description": "Chame essa tool quando o usuário enviar qualquer tipo de link de websites que ele deseja obter informações. (com exceções de links do Youtube). Caso haja mais de um link separe corretamente, o formato de input precise ser array.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "fields": {"values": [{"name": "action", "stringValue": "website_scraping"}, {"name": "session_id", "stringValue": "={{ $('Prep Input Fields1').item.json.session_id }}"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"website_url\": [\n    \"https://www.youtube.com/watch?v=exemplo1\",\n    \"https://www.youtube.com/watch?v=exemplo2\"\n  ]\n}"}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.3, "position": [1220, 300], "id": "1a451ecc-a680-44ec-a68b-bc4444ce3053", "name": "website_scraping"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "95507aba-920b-40f3-ba67-614bdea04601", "leftValue": "={{ $json.action }}", "rightValue": "website_scraping", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [740, 520], "id": "79bd24f2-3a7e-41d1-a052-173b2f0113a7", "name": "Filter_website_scraping"}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"-> Getting content...\",\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "7e57989c-d8f2-4e00-8abd-f6aa79e6a621", "name": "Add User Message to DB1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [960, 520], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [1580, 520], "id": "9733d429-8e99-4aba-bbd7-fb10b742a72d", "name": "Loop Over Items"}, {"parameters": {"assignments": {"assignments": [{"id": "c96a52ea-c5c1-456d-ba22-002195a6e3fd", "name": "session_id", "value": "={{ $('Execute Workflow Trigger').item.json.session_id }}", "type": "string"}, {"id": "6a04833d-3b50-4963-ae77-e1131fcf0524", "name": "website_url", "value": "={{ $('Filter_website_scraping').item.json.query.website_url }}", "type": "array"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1180, 520], "id": "2e5cd6e5-c104-41e5-aa90-5a35a6b57eed", "name": "normalization"}, {"parameters": {"fieldToSplitOut": "video_id, website_url", "include": "allOtherFields", "options": {}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1380, 520], "id": "a7341d5c-27ed-4d9e-be43-34dfa2c1087b", "name": "split_out_urls"}, {"parameters": {"amount": 10}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2040, 760], "id": "5c3f2ace-5f19-4933-ab68-2f6e6bf48d1d", "name": "Wait", "webhookId": "5ea3843c-8c68-418e-8f8e-4659d782d300"}, {"parameters": {"aggregate": "aggregateAllItemData", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [1820, 440], "id": "cf4b9418-86cf-48c8-ad86-e43f02c64812", "name": "Aggregate"}, {"parameters": {"url": "=https://r.jina.ai/{{ $json.website_url }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1820, 640], "id": "5ce7402f-91a8-4bd8-97c7-aa372a67a4d9", "name": "get_scraping_website", "retryOnFail": true, "waitBetweenTries": 5000, "maxTries": 5, "onError": "continueErrorOutput"}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('normalization').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `-> ${$json.data[0].toJsonString()}`,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "ea8cfce1-971b-41c4-8c77-9b7c6fa73967", "name": "Add User Message to DB2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [2040, 440], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}], "pinData": {"Execute Workflow Trigger": [{"json": {"query": {"website_url": ["https://developers.tiktok.com/doc/login-kit-web", "https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens", "https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation"]}, "action": "website_scraping", "session_id": "d0a5b3bfe1344d07965afe646d1cbf7a"}}], "Aggregate": [{"json": {"data": [{"data": "Title: TikTok for Developers\n\nURL Source: https://developers.tiktok.com/doc/login-kit-web\n\nMarkdown Content:\nTikTok for Developers\n=============== \n\n[![Image 3: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)](https://developers.tiktok.com/)\n\nProducts\n\n[Docs](https://developers.tiktok.com/doc/overview)[Support](https://developers.tiktok.com/support)[Blog](https://developers.tiktok.com/blogs)\n\nGet started\n\nDocs\n\n*   [Overview](https://developers.tiktok.com/doc/overview?enter_method=left_navigation)\n*   [Account Management](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n    *   [Working with organizations](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n*   [App Management](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Register Your App](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Add a Sandbox](https://developers.tiktok.com/doc/add-a-sandbox?enter_method=left_navigation)\n    *   [App Review FAQ](https://developers.tiktok.com/doc/getting-started-faq?enter_method=left_navigation)\n*   [Our Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [App Review Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [Developer Guidelines](https://developers.tiktok.com/doc/our-guidelines-developer-guidelines?enter_method=left_navigation)\n    *   [Design Guidelines](https://developers.tiktok.com/doc/getting-started-design-guidelines?enter_method=left_navigation)\n    *   [Content Sharing Guidelines](https://developers.tiktok.com/doc/content-sharing-guidelines?enter_method=left_navigation)\n*   [Integration Essentials](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n    *   [Mobile SDK](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n            *   [iOS Quickstart](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n            *   [Android Quickstart](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n    *   [Server API](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Rate Limits](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Error Handling](https://developers.tiktok.com/doc/tiktok-api-v2-error-handling?enter_method=left_navigation)\n        *   [OAuth](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [User Access Token Management](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [Client Access Token Management](https://developers.tiktok.com/doc/client-access-token-management?enter_method=left_navigation)\n            *   [Error Handling](https://developers.tiktok.com/doc/oauth-error-handling?enter_method=left_navigation)\n    *   [Webhooks](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Events](https://developers.tiktok.com/doc/webhooks-events?enter_method=left_navigation)\n        *   [Verification](https://developers.tiktok.com/doc/webhooks-verification?enter_method=left_navigation)\n*   [Login Kit](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Web](https://developers.tiktok.com/doc/login-kit-web?enter_method=left_navigation)\n    *   [Desktop](https://developers.tiktok.com/doc/login-kit-desktop?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/login-kit-ios-quickstart?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/login-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Manage User Access Tokens](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens?enter_method=left_navigation)\n    *   [QR Code Authorization](https://developers.tiktok.com/doc/login-kit-qr-code-authorization?enter_method=left_navigation)\n*   [Share Kit](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Green Screen Effect](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n*   [Content Posting API](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Direct Post](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Upload](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation)\n    *   [Media Transfer Guide](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n        *   [Video](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Direct Post](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Upload](https://developers.tiktok.com/doc/content-posting-api-reference-upload-video?enter_method=left_navigation)\n        *   [Photo](https://developers.tiktok.com/doc/content-posting-api-reference-photo-post?enter_method=left_navigation)\n        *   [Query Creator Info](https://developers.tiktok.com/doc/content-posting-api-reference-query-creator-info?enter_method=left_navigation)\n        *   [Get Post Status](https://developers.tiktok.com/doc/content-posting-api-reference-get-video-status?enter_method=left_navigation)\n*   [Data Portability API](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Data Portability FAQ](https://developers.tiktok.com/doc/data-portability-api-faq?enter_method=left_navigation)\n    *   [Data Portability Application Guidelines](https://developers.tiktok.com/doc/data-portability-api-application-guidelines?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Add Data Request](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Cancel Data Request](https://developers.tiktok.com/doc/data-portability-api-cancel-data-request?enter_method=left_navigation)\n        *   [Check Status of Data Request](https://developers.tiktok.com/doc/data-portability-api-check-status-of-data-request?enter_method=left_navigation)\n        *   [Download](https://developers.tiktok.com/doc/data-portability-api-download?enter_method=left_navigation)\n        *   [Data Types](https://developers.tiktok.com/doc/data-portability-data-types?enter_method=left_navigation)\n*   [Display API](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/display-api-get-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Get User Info](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-query?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-list?enter_method=left_navigation)\n        *   [Video Object](https://developers.tiktok.com/doc/tiktok-api-v2-video-object?enter_method=left_navigation)\n*   [Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [About Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [Getting Started - Research API](https://developers.tiktok.com/doc/research-api-get-started?enter_method=left_navigation)\n    *   [Getting Started - VCE](https://developers.tiktok.com/doc/vce-getting-started?enter_method=left_navigation)\n    *   [Frequently Asked Questions](https://developers.tiktok.com/doc/research-api-faq?enter_method=left_navigation)\n    *   [Codebook](https://developers.tiktok.com/doc/research-api-codebook?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query User Info](https://developers.tiktok.com/doc/research-api-specs-query-user-info?enter_method=left_navigation)\n        *   [Query Video Comments](https://developers.tiktok.com/doc/research-api-specs-query-video-comments?enter_method=left_navigation)\n        *   [Query User Liked Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-liked-videos?enter_method=left_navigation)\n        *   [Query User Pinned Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-pinned-videos?enter_method=left_navigation)\n        *   [Query User Followers](https://developers.tiktok.com/doc/research-api-specs-query-user-followers?enter_method=left_navigation)\n        *   [Query User Following](https://developers.tiktok.com/doc/research-api-specs-query-user-following?enter_method=left_navigation)\n        *   [Query User Reposted Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-reposted-videos?enter_method=left_navigation)\n        *   [Query Playlist Info](https://developers.tiktok.com/doc/research-api-specs-query-playlist-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Info](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Products](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-products?enter_method=left_navigation)\n        *   [Query TikTok Shop Reviews](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-reviews?enter_method=left_navigation)\n    *   [VCE Reference](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Comments](https://developers.tiktok.com/doc/vce-query-video-comments?enter_method=left_navigation)\n        *   [Query Users](https://developers.tiktok.com/doc/vce-query-profiles?enter_method=left_navigation)\n        *   [Query Liked Videos](https://developers.tiktok.com/doc/vce-query-liked-videos?enter_method=left_navigation)\n        *   [Query Reposted Videos](https://developers.tiktok.com/doc/vce-query-reposted-videos?enter_method=left_navigation)\n        *   [Query Pinned Videos](https://developers.tiktok.com/doc/vce-query-pinned-videos?enter_method=left_navigation)\n        *   [Query Followers List](https://developers.tiktok.com/doc/vce-query-followers-list?enter_method=left_navigation)\n        *   [Query Following List](https://developers.tiktok.com/doc/vce-query-following-list?enter_method=left_navigation)\n        *   [Query Playlists](https://developers.tiktok.com/doc/vce-query-playlists?enter_method=left_navigation)\n        *   [Query TikTok Shop Data](https://developers.tiktok.com/doc/vce-query-tiktok-shop-data?enter_method=left_navigation)\n*   [Commercial Content API](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [Getting Started](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Ads](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Advertisers](https://developers.tiktok.com/doc/commercial-content-api-query-advertisers?enter_method=left_navigation)\n        *   [Get Ad Details](https://developers.tiktok.com/doc/commercial-content-api-get-ad-details?enter_method=left_navigation)\n        *   [Get Ad Report](https://developers.tiktok.com/doc/commercial-content-api-get-ad-report?enter_method=left_navigation)\n        *   [Query Commercial Content](https://developers.tiktok.com/doc/commercial-content-api-query-commercial-content?enter_method=left_navigation)\n        *   [Supported Countries](https://developers.tiktok.com/doc/commercial-content-api-supported-countries?enter_method=left_navigation)\n*   [Embed](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Player](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Videos](https://developers.tiktok.com/doc/embed-videos?enter_method=left_navigation)\n    *   [Embed Creator Profiles](https://developers.tiktok.com/doc/embed-creator-profiles?enter_method=left_navigation)\n*   [Scopes](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Overview](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Reference](https://developers.tiktok.com/doc/tiktok-api-scopes?enter_method=left_navigation)\n*   [Legacy Products](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n    *   [Legacy API](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [Migrating](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-list?enter_method=left_navigation)\n        *   [User Info](https://developers.tiktok.com/doc/tiktok-api-v1-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-query?enter_method=left_navigation)\n        *   [Legacy User Access Token Guide](https://developers.tiktok.com/doc/legacy-user-access-guide?enter_method=left_navigation)\n        *   [Share Video API](https://developers.tiktok.com/doc/web-video-kit-with-web?enter_method=left_navigation)\n    *   [Legacy Mobile SDK](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Objective-C](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Swift](https://developers.tiktok.com/doc/getting-started-ios-quickstart-swift?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-ios-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-ios-download?enter_method=left_navigation)\n            *   [Login Kit With Objective-C](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-objective-c?enter_method=left_navigation)\n            *   [Login Kit With Swift](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-swift?enter_method=left_navigation)\n            *   [Video Kit With Objective-C](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-objective-c?enter_method=left_navigation)\n            *   [Video Kit With Swift](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-swift?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Quickstart](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-android-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-android-download?enter_method=left_navigation)\n            *   [Login Kit with Android](https://developers.tiktok.com/doc/login-kit-android?enter_method=left_navigation)\n            *   [Video Kit With Android](https://developers.tiktok.com/doc/video-kit-android-video-kit-with-android?enter_method=left_navigation)\n            *   [Android FileProvider](https://developers.tiktok.com/doc/video-kit-android-android-fileprovider?enter_method=left_navigation)\n*   [Changelog](https://developers.tiktok.com/doc/changelog?enter_method=left_navigation)\n\nLogin Kit for Web\n=================\n\nThis guide details how to enable authentication from your web app to TikTok. After successfully completing authentication with TikTok, developers can obtain an `access_token` for the TikTok user.\n\nPrerequisites\n-------------\n\n### Register your app\n\nRegister your app following [these steps](https://developers.tiktok.com/doc/getting-started-create-an-app). Then obtain a client key and secret from the developer portal on [https://developers.tiktok.com](https://developers.tiktok.com/) under **Manage apps**.\n\n### Configure redirect URI\n\nRedirect URI is required for web apps. After the user completes authorization with Login Kit on the web, they will be redirected to a URI provided by you. This redirect URI must be registered in the Login Kit product configuration for your app.\n\nThe following are restrictions for registering redirect URIs.\n\n*   A maximum of 10 URIs is supported.\n*   The length of each URI must be less than 512 characters.\n*   URIs must be absolute and begin with `https`. For example:\n\n*   Correct: `https://dev.example.com/auth/callback/`\n*   Incorrect: `dev.example.com/auth/callback/`\n\n*   URIs must be static. Parameters will be denied. For example:\n\n*   Correct: `https://dev.example.com/auth/callback/`\n*   Incorrect: `https://dev.example.com/auth/callback/?id=1`\n\n*   URIs cannot include a fragment, or hash character (#):\n\n*   Correct: `https://dev.example.com/auth/callback/`\n*   Incorrect: `https://dev.example.com/auth/callback/#100`\n\nIntegration Guide\n-----------------\n\n### Implement the front-end code\n\nGet started by connecting your front-end login button to the server endpoint. The following is an example in HTML:\n\n```html\n<a href='{SERVER_ENDPOINT_OAUTH}'>Continue with TikTok</a>\n```\n\n### Implement the server code to handle authorization grant flow\n\nThe server code must be responsible for the following:\n\n*   Ensuring that the client secret and refresh token are stored securely.\n*   Ensuring that the security for each user is protected by preventing request forgery attacks.\n*   Handling the refresh flow before access token expiry.\n*   Managing the access token request flow for each user.\n\n#### Redirect request to TikTok's authorization server\n\n##### Create an anti-forgery state token\n\nYou must prevent request forgery attacks to protect the security of your users. The first step before making the redirect request to TikTok's authorization server is to create a unique session token to maintain the state between the request and callback.\n\nYou will later match this unique session token with the authentication response to verify that the user is making the request and not a malicious attacker.\n\nOne of the simple approaches to a state token is a randomly generated alphanumeric string constructed using a random-number generator. For example:\n\n```javascript\nlet array = new Uint8Array(30); \nconst csrfState = window.crypto.getRandomValues(array);\n```\n\n##### Initial redirect to TikTok's authorization page\n\nTo make the initial redirect request to TikTok's authorization server, the following query parameters below must be added to the Authorization Page URL using the `application/x-www-form-urlencoded` format.\n\nFor example, you can use an [online URL encoder](https://www.urlencoder.org/) to encode parameters. Select **UTF-8** as the destination character set.\n\n<table><tbody><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Parameter</b></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Type</b></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Description</b></p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>client_key</code></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">The unique identification key provisioned to the partner.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>scope</code></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">A comma (,) separated string of authorization scope(s). These scope(s) are assigned to your application on the TikTok for Developers website. They handle what content your application can and cannot access. If a scope is toggleable, the user can deny access to one scope while granting access to others.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>redirect_uri</code></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">The redirect URI that you requested for your application. It must match one of the redirect URIs you registered for the app.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>state</code></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">The state is used to maintain the state of your request and callback. This value will be included when redirecting the user back to the client. Check if the state returned in the callback matches what you sent earlier to prevent cross-site request forgery.</p><p style=\"text-align:left\">The state can also include customized parameters that you want TikTok service to return.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>response_type</code></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">This value should always be set to <code>code</code>.</p></td></tr><tr><td><p style=\"text-align:left\"><code>disable_auto_auth</code></p></td><td><p style=\"text-align:left\">int</p></td><td><p style=\"text-align:left\">Controls whether the authorization page is automatically presented to users. When set to 0, skips the authorization page for valid sessions. When set to 1, always displays the authorization page.</p></td></tr></tbody></table>\n\nRedirect your users to the authorization page URL and supply the necessary query parameters. Note that the page can only be accessed through HTTPS.\n\n<table><tbody><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Type</b></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Description</b></p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">URL</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>https://www.tiktok.com/v2/auth/authorize/</code></p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">Query parameters</p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>client_key=&lt;client_key&gt;&amp;response_type=code&amp;scope=&lt;scope&gt;&amp;redirect_uri=&lt;redirect_uri&gt;&amp;state=&lt;state&gt;</code></p></td></tr></tbody></table>\n\nNote: If you are an existing client and use `https://www.tiktok.com/auth/authorize/` as the authorization page URL, please [register a redirect URI](https://developers.tiktok.com/apps/) for your app and migrate to the new URL mentioned above.\n\nThe following is an example using Node, Express, and JavaScript:\n\n```javascript\nconst express = require('express');\nconst app = express();\nconst fetch = require('node-fetch');\nconst cookieParser = require('cookie-parser');\nconst cors = require('cors');\n\napp.use(cookieParser());\napp.use(cors());\napp.listen(process.env.PORT || 5000).\n\nconst CLIENT_KEY = 'your_client_key' // this value can be found in app's developer portal\n\napp.get('/oauth', (req, res) => {\n    const csrfState = Math.random().toString(36).substring(2);\n    res.cookie('csrfState', csrfState, { maxAge: 60000 });\n\n    let url = 'https://www.tiktok.com/v2/auth/authorize/';\n\n    // the following params need to be in `application/x-www-form-urlencoded` format.\n    url += '?client_key={CLIENT_KEY}';\n    url += '&scope=user.info.basic';\n    url += '&response_type=code';\n    url += '&redirect_uri={SERVER_ENDPOINT_REDIRECT}';\n    url += '&state=' + csrfState;\n\n    res.redirect(url);\n})\n```\n\n#### TikTok prompts a users to log in or sign up\n\nThe authorization page takes the user to the TikTok website if the user is not logged in. They are then prompted to log in or sign up for TikTok.\n\n#### TikTok prompts a user for consent\n\nAfter logging in or signing up, an authorization page asks the user for consent to allow your application to access your requested permissions.\n\n#### Manage authorization response\n\nIf the user authorizes access, they will be redirected to `redirect_uri` with the following query parameters appended using `application/x-www-form-urlencoded` format:\n\n<table><tbody><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Parameter</b></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Type</b></p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><b>Description</b></p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>code</code></p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">Authorization code that is used in getting an access token.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>scopes</code></p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">A comma-separated (,) string of authorization scope(s), which the user has granted.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>state</code></p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">A unique, non-guessable string when making the initial authorization request. This value allows you to prevent CSRF attacks by confirming that the value coming from the response matches the one you sent.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>error</code></p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">If this field is set, it means that the current user is not eligible for using third-party login or authorization. The partner is responsible for handling the error gracefully.</p></td></tr><tr><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\"><code>error_description</code></p><br></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">String</p></td><td rowspan=\"1\" colspan=\"1\"><p style=\"text-align:left\">If this field is set, it will be a human-readable description about the error.</p></td></tr></tbody></table>\n\n#### Manage access token\n\nUsing the `code` appended to your `redirect_uri`, you can obtain `access_token` for the user, which completes the flow for logging in with TikTok.\n\nSee [Manage User Access Tokens](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens) for related endpoints.\n\n  \n\nOn this page\n\n[Prerequisites](https://developers.tiktok.com/doc/login-kit-web#prerequisites)[Register your app](https://developers.tiktok.com/doc/login-kit-web#register_your_app)[Configure redirect URI](https://developers.tiktok.com/doc/login-kit-web#configure_redirect_uri)[Integration Guide](https://developers.tiktok.com/doc/login-kit-web#integration_guide)[Implement the front-end code](https://developers.tiktok.com/doc/login-kit-web#implement_the_front-end_code)[Implement the server code to handle authorization grant flow](https://developers.tiktok.com/doc/login-kit-web#implement_the_server_code_to_handle_authorization_grant_flow)[Redirect request to TikTok's authorization server](https://developers.tiktok.com/doc/login-kit-web#redirect_request_to_tiktok's_authorization_server)[TikTok prompts a users to log in or sign up](https://developers.tiktok.com/doc/login-kit-web#tiktok_prompts_a_users_to_log_in_or_sign_up)[TikTok prompts a user for consent](https://developers.tiktok.com/doc/login-kit-web#tiktok_prompts_a_user_for_consent)[Manage authorization response](https://developers.tiktok.com/doc/login-kit-web#manage_authorization_response)[Manage access token](https://developers.tiktok.com/doc/login-kit-web#manage_access_token)\n\nOn this page\n\n![Image 4: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)\n\nProducts\n\n*   [Share Kit](https://developers.tiktok.com/products/share-kit/)\n*   [Login Kit](https://developers.tiktok.com/products/login-kit/)\n*   [Content Posting API](https://developers.tiktok.com/products/content-posting-api/)\n*   [Research API](https://developers.tiktok.com/products/research-api/)\n*   [Display API](https://developers.tiktok.com/doc/display-api-get-started/)\n\n*   [Embed Videos](https://developers.tiktok.com/doc/embed-videos/)\n*   [Data Portability API](https://developers.tiktok.com/products/data-portability-api/)\n*   [Green Screen Kit](https://developers.tiktok.com/products/share-kit#green-screen)\n*   [Commercial Content API](https://developers.tiktok.com/products/commercial-content-api)\n\nOther platforms\n\n*   [TikTok Embeds](https://www.tiktok.com/embed \"TikTok Embeds\")\n*   [TikTok for Business](https://www.tiktok.com/business \"TikTok for Business\")\n*   [Advertise on TikTok](https://www.tiktok.com/business/&attr_source=tt_official_site&attr_medium=tt_official_site_guidance \"Advertise on TikTok\")\n*   [TikTok Creative Center](https://ads.tiktok.com/business/creativecenter/ \"TikTok Creative Center\")\n*   [TikTok.com](https://www.tiktok.com/ \"TikTok\")\n\nCompany\n\n*   [About TikTok](https://www.tiktok.com/about \"About TikTok\")\n*   [Newsroom](https://newsroom.tiktok.com/ \"TikTok Newsroom\")\n*   [Contact](https://www.tiktok.com/about/contact \"Contact TikTok\")\n*   [Careers](https://careers.tiktok.com/ \"TikTok Careers page\")\n*   [ByteDance](https://www.bytedance.com/ \"About ByteDance\")\n*   [Transparency Center](https://www.tiktok.com/transparency \"TikTok\")\n\n© 2025 TikTok[Terms of Service](https://www.tiktok.com/legal/page/global/tik-tok-developer-terms-of-service/en)[Privacy Policy](https://www.tiktok.com/legal/page/global/partner-privacy-policy/en)[Resources & Legal](https://developers.tiktok.com/doc/login-kit-web#site-footer)\n\nClose\n"}, {"data": "Title: TikTok for Developers\n\nURL Source: https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens\n\nMarkdown Content:\nTikTok for Developers\n=============== \n\n[![Image 3: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)](https://developers.tiktok.com/)\n\nProducts\n\n[Docs](https://developers.tiktok.com/doc/overview)[Support](https://developers.tiktok.com/support)[Blog](https://developers.tiktok.com/blogs)\n\nGet started\n\nDocs\n\n*   [Overview](https://developers.tiktok.com/doc/overview?enter_method=left_navigation)\n*   [Account Management](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n    *   [Working with organizations](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n*   [App Management](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Register Your App](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Add a Sandbox](https://developers.tiktok.com/doc/add-a-sandbox?enter_method=left_navigation)\n    *   [App Review FAQ](https://developers.tiktok.com/doc/getting-started-faq?enter_method=left_navigation)\n*   [Our Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [App Review Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [Developer Guidelines](https://developers.tiktok.com/doc/our-guidelines-developer-guidelines?enter_method=left_navigation)\n    *   [Design Guidelines](https://developers.tiktok.com/doc/getting-started-design-guidelines?enter_method=left_navigation)\n    *   [Content Sharing Guidelines](https://developers.tiktok.com/doc/content-sharing-guidelines?enter_method=left_navigation)\n*   [Integration Essentials](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n    *   [Mobile SDK](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n            *   [iOS Quickstart](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n            *   [Android Quickstart](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n    *   [Server API](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Rate Limits](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Error Handling](https://developers.tiktok.com/doc/tiktok-api-v2-error-handling?enter_method=left_navigation)\n        *   [OAuth](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [User Access Token Management](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [Client Access Token Management](https://developers.tiktok.com/doc/client-access-token-management?enter_method=left_navigation)\n            *   [Error Handling](https://developers.tiktok.com/doc/oauth-error-handling?enter_method=left_navigation)\n    *   [Webhooks](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Events](https://developers.tiktok.com/doc/webhooks-events?enter_method=left_navigation)\n        *   [Verification](https://developers.tiktok.com/doc/webhooks-verification?enter_method=left_navigation)\n*   [Login Kit](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Web](https://developers.tiktok.com/doc/login-kit-web?enter_method=left_navigation)\n    *   [Desktop](https://developers.tiktok.com/doc/login-kit-desktop?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/login-kit-ios-quickstart?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/login-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Manage User Access Tokens](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens?enter_method=left_navigation)\n    *   [QR Code Authorization](https://developers.tiktok.com/doc/login-kit-qr-code-authorization?enter_method=left_navigation)\n*   [Share Kit](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Green Screen Effect](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n*   [Content Posting API](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Direct Post](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Upload](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation)\n    *   [Media Transfer Guide](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n        *   [Video](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Direct Post](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Upload](https://developers.tiktok.com/doc/content-posting-api-reference-upload-video?enter_method=left_navigation)\n        *   [Photo](https://developers.tiktok.com/doc/content-posting-api-reference-photo-post?enter_method=left_navigation)\n        *   [Query Creator Info](https://developers.tiktok.com/doc/content-posting-api-reference-query-creator-info?enter_method=left_navigation)\n        *   [Get Post Status](https://developers.tiktok.com/doc/content-posting-api-reference-get-video-status?enter_method=left_navigation)\n*   [Data Portability API](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Data Portability FAQ](https://developers.tiktok.com/doc/data-portability-api-faq?enter_method=left_navigation)\n    *   [Data Portability Application Guidelines](https://developers.tiktok.com/doc/data-portability-api-application-guidelines?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Add Data Request](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Cancel Data Request](https://developers.tiktok.com/doc/data-portability-api-cancel-data-request?enter_method=left_navigation)\n        *   [Check Status of Data Request](https://developers.tiktok.com/doc/data-portability-api-check-status-of-data-request?enter_method=left_navigation)\n        *   [Download](https://developers.tiktok.com/doc/data-portability-api-download?enter_method=left_navigation)\n        *   [Data Types](https://developers.tiktok.com/doc/data-portability-data-types?enter_method=left_navigation)\n*   [Display API](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/display-api-get-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Get User Info](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-query?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-list?enter_method=left_navigation)\n        *   [Video Object](https://developers.tiktok.com/doc/tiktok-api-v2-video-object?enter_method=left_navigation)\n*   [Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [About Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [Getting Started - Research API](https://developers.tiktok.com/doc/research-api-get-started?enter_method=left_navigation)\n    *   [Getting Started - VCE](https://developers.tiktok.com/doc/vce-getting-started?enter_method=left_navigation)\n    *   [Frequently Asked Questions](https://developers.tiktok.com/doc/research-api-faq?enter_method=left_navigation)\n    *   [Codebook](https://developers.tiktok.com/doc/research-api-codebook?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query User Info](https://developers.tiktok.com/doc/research-api-specs-query-user-info?enter_method=left_navigation)\n        *   [Query Video Comments](https://developers.tiktok.com/doc/research-api-specs-query-video-comments?enter_method=left_navigation)\n        *   [Query User Liked Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-liked-videos?enter_method=left_navigation)\n        *   [Query User Pinned Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-pinned-videos?enter_method=left_navigation)\n        *   [Query User Followers](https://developers.tiktok.com/doc/research-api-specs-query-user-followers?enter_method=left_navigation)\n        *   [Query User Following](https://developers.tiktok.com/doc/research-api-specs-query-user-following?enter_method=left_navigation)\n        *   [Query User Reposted Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-reposted-videos?enter_method=left_navigation)\n        *   [Query Playlist Info](https://developers.tiktok.com/doc/research-api-specs-query-playlist-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Info](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Products](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-products?enter_method=left_navigation)\n        *   [Query TikTok Shop Reviews](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-reviews?enter_method=left_navigation)\n    *   [VCE Reference](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Comments](https://developers.tiktok.com/doc/vce-query-video-comments?enter_method=left_navigation)\n        *   [Query Users](https://developers.tiktok.com/doc/vce-query-profiles?enter_method=left_navigation)\n        *   [Query Liked Videos](https://developers.tiktok.com/doc/vce-query-liked-videos?enter_method=left_navigation)\n        *   [Query Reposted Videos](https://developers.tiktok.com/doc/vce-query-reposted-videos?enter_method=left_navigation)\n        *   [Query Pinned Videos](https://developers.tiktok.com/doc/vce-query-pinned-videos?enter_method=left_navigation)\n        *   [Query Followers List](https://developers.tiktok.com/doc/vce-query-followers-list?enter_method=left_navigation)\n        *   [Query Following List](https://developers.tiktok.com/doc/vce-query-following-list?enter_method=left_navigation)\n        *   [Query Playlists](https://developers.tiktok.com/doc/vce-query-playlists?enter_method=left_navigation)\n        *   [Query TikTok Shop Data](https://developers.tiktok.com/doc/vce-query-tiktok-shop-data?enter_method=left_navigation)\n*   [Commercial Content API](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [Getting Started](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Ads](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Advertisers](https://developers.tiktok.com/doc/commercial-content-api-query-advertisers?enter_method=left_navigation)\n        *   [Get Ad Details](https://developers.tiktok.com/doc/commercial-content-api-get-ad-details?enter_method=left_navigation)\n        *   [Get Ad Report](https://developers.tiktok.com/doc/commercial-content-api-get-ad-report?enter_method=left_navigation)\n        *   [Query Commercial Content](https://developers.tiktok.com/doc/commercial-content-api-query-commercial-content?enter_method=left_navigation)\n        *   [Supported Countries](https://developers.tiktok.com/doc/commercial-content-api-supported-countries?enter_method=left_navigation)\n*   [Embed](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Player](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Videos](https://developers.tiktok.com/doc/embed-videos?enter_method=left_navigation)\n    *   [Embed Creator Profiles](https://developers.tiktok.com/doc/embed-creator-profiles?enter_method=left_navigation)\n*   [Scopes](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Overview](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Reference](https://developers.tiktok.com/doc/tiktok-api-scopes?enter_method=left_navigation)\n*   [Legacy Products](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n    *   [Legacy API](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [Migrating](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-list?enter_method=left_navigation)\n        *   [User Info](https://developers.tiktok.com/doc/tiktok-api-v1-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-query?enter_method=left_navigation)\n        *   [Legacy User Access Token Guide](https://developers.tiktok.com/doc/legacy-user-access-guide?enter_method=left_navigation)\n        *   [Share Video API](https://developers.tiktok.com/doc/web-video-kit-with-web?enter_method=left_navigation)\n    *   [Legacy Mobile SDK](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Objective-C](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Swift](https://developers.tiktok.com/doc/getting-started-ios-quickstart-swift?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-ios-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-ios-download?enter_method=left_navigation)\n            *   [Login Kit With Objective-C](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-objective-c?enter_method=left_navigation)\n            *   [Login Kit With Swift](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-swift?enter_method=left_navigation)\n            *   [Video Kit With Objective-C](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-objective-c?enter_method=left_navigation)\n            *   [Video Kit With Swift](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-swift?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Quickstart](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-android-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-android-download?enter_method=left_navigation)\n            *   [Login Kit with Android](https://developers.tiktok.com/doc/login-kit-android?enter_method=left_navigation)\n            *   [Video Kit With Android](https://developers.tiktok.com/doc/video-kit-android-video-kit-with-android?enter_method=left_navigation)\n            *   [Android FileProvider](https://developers.tiktok.com/doc/video-kit-android-android-fileprovider?enter_method=left_navigation)\n*   [Changelog](https://developers.tiktok.com/doc/changelog?enter_method=left_navigation)\n\nManage User Access Tokens with OAuth v2\n=======================================\n\nTikTok Login Kit manages the token life cycle, allowing you to integrate login and authentication flows directly in your application. A successful authorization flow grants you refreshable access tokens. Those tokens enable you to perform endpoint access with user permissions.\n\n### Authorization scopes\n\nMost endpoints provided by TikTok for Developers require direct consent from TikTok users before you can invoke them. The permissions are granted on a scope level. Users have the rights to only agree to a subset of scopes you requested from them.\n\nThe following are some example scopes:\n\n*   **user.info****.basic** gives read-only access to a user's avatar and display name.\n*   **video.list** gives read-only access to a user's public TikTok videos.\n\nLearn more about [scopes](https://developers.tiktok.com/doc/scopes-overview).\n\n### Token security\n\nTokens must be handled with caution. It is recommended that you store and manage all tokens on the server side.\n\n*   Access token is a user authorization token that can be used to directly access user information in the TikTok ecosystem.\n*   Refresh token is used to renew the access token.\n\nEndpoints For Web\n-----------------\n\nIf you have already registered a redirect URI for your web app and use `https://www.tiktok.com/v2/auth/authorize/` to authorize, please refer to the [new generation user access token management API guide](https://developers.tiktok.com/doc/oauth-user-access-token-management).\n\nIf you are an existing client, have not registered a redirect URI for your web app and use `https://www.tiktok.com/auth/authorize/` to authorize, please refer to the [legacy user access token management API guide](https://developers.tiktok.com/doc/legacy-user-access-guide). To register a redirect URI, go to the [Manage apps](https://developers.tiktok.com/apps/) page of the TikTok for Developers website and migrate to the new endpoints as soon as possible.\n\nEndpoints for Mobile\n--------------------\n\n**Preferred:** If you are using the new [Android](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart) or [iOS](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart) TikTok OpenSDK, please refer to the new [user access token management guide](https://developers.tiktok.com/doc/oauth-user-access-token-management).\n\n**Legacy:** If you are using the old [Android](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart) or [iOS](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart) TikTok OpenSDK, please refer to the [legacy user access token management guide](https://developers.tiktok.com/doc/legacy-user-access-guide).\n\nEndpoints for Desktop\n---------------------\n\nYou must register a redirect URI for your desktop app and use `https://www.tiktok.com/v2/auth/authorize/` to authorize. Please refer to the [new generation user access token management API guide](https://developers.tiktok.com/doc/oauth-user-access-token-management) to manage the user access token.\n\n  \n\nOn this page\n\n[Authorization scopes](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#authorization_scopes)[Token security](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#token_security)[Endpoints For Web](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#endpoints_for_web)[Endpoints for Mobile](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#endpoints_for_mobile)[Endpoints for Desktop](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#endpoints_for_desktop)\n\nOn this page\n\n![Image 4: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)\n\nProducts\n\n*   [Share Kit](https://developers.tiktok.com/products/share-kit/)\n*   [Login Kit](https://developers.tiktok.com/products/login-kit/)\n*   [Content Posting API](https://developers.tiktok.com/products/content-posting-api/)\n*   [Research API](https://developers.tiktok.com/products/research-api/)\n*   [Display API](https://developers.tiktok.com/doc/display-api-get-started/)\n\n*   [Embed Videos](https://developers.tiktok.com/doc/embed-videos/)\n*   [Data Portability API](https://developers.tiktok.com/products/data-portability-api/)\n*   [Green Screen Kit](https://developers.tiktok.com/products/share-kit#green-screen)\n*   [Commercial Content API](https://developers.tiktok.com/products/commercial-content-api)\n\nOther platforms\n\n*   [TikTok Embeds](https://www.tiktok.com/embed \"TikTok Embeds\")\n*   [TikTok for Business](https://www.tiktok.com/business \"TikTok for Business\")\n*   [Advertise on TikTok](https://www.tiktok.com/business/&attr_source=tt_official_site&attr_medium=tt_official_site_guidance \"Advertise on TikTok\")\n*   [TikTok Creative Center](https://ads.tiktok.com/business/creativecenter/ \"TikTok Creative Center\")\n*   [TikTok.com](https://www.tiktok.com/ \"TikTok\")\n\nCompany\n\n*   [About TikTok](https://www.tiktok.com/about \"About TikTok\")\n*   [Newsroom](https://newsroom.tiktok.com/ \"TikTok Newsroom\")\n*   [Contact](https://www.tiktok.com/about/contact \"Contact TikTok\")\n*   [Careers](https://careers.tiktok.com/ \"TikTok Careers page\")\n*   [ByteDance](https://www.bytedance.com/ \"About ByteDance\")\n*   [Transparency Center](https://www.tiktok.com/transparency \"TikTok\")\n\n© 2025 TikTok[Terms of Service](https://www.tiktok.com/legal/page/global/tik-tok-developer-terms-of-service/en)[Privacy Policy](https://www.tiktok.com/legal/page/global/partner-privacy-policy/en)[Resources & Legal](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens#site-footer)\n\nClose\n"}, {"data": "Title: TikTok for Developers\n\nURL Source: https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation\n\nMarkdown Content:\nTikTok for Developers\n=============== \n\n[![Image 9: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)](https://developers.tiktok.com/)\n\nProducts\n\n[Docs](https://developers.tiktok.com/doc/overview)[Support](https://developers.tiktok.com/support)[Blog](https://developers.tiktok.com/blogs)\n\nGet started\n\nDocs\n\n*   [Overview](https://developers.tiktok.com/doc/overview?enter_method=left_navigation)\n*   [Account Management](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n    *   [Working with organizations](https://developers.tiktok.com/doc/working-with-organizations?enter_method=left_navigation)\n*   [App Management](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Register Your App](https://developers.tiktok.com/doc/getting-started-create-an-app?enter_method=left_navigation)\n    *   [Add a Sandbox](https://developers.tiktok.com/doc/add-a-sandbox?enter_method=left_navigation)\n    *   [App Review FAQ](https://developers.tiktok.com/doc/getting-started-faq?enter_method=left_navigation)\n*   [Our Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [App Review Guidelines](https://developers.tiktok.com/doc/app-review-guidelines?enter_method=left_navigation)\n    *   [Developer Guidelines](https://developers.tiktok.com/doc/our-guidelines-developer-guidelines?enter_method=left_navigation)\n    *   [Design Guidelines](https://developers.tiktok.com/doc/getting-started-design-guidelines?enter_method=left_navigation)\n    *   [Content Sharing Guidelines](https://developers.tiktok.com/doc/content-sharing-guidelines?enter_method=left_navigation)\n*   [Integration Essentials](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n    *   [Mobile SDK](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n            *   [iOS Quickstart](https://developers.tiktok.com/doc/mobile-sdk-ios-quickstart?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n            *   [Android Quickstart](https://developers.tiktok.com/doc/mobile-sdk-android-quickstart?enter_method=left_navigation)\n    *   [Server API](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Rate Limits](https://developers.tiktok.com/doc/tiktok-api-v2-rate-limit?enter_method=left_navigation)\n        *   [Error Handling](https://developers.tiktok.com/doc/tiktok-api-v2-error-handling?enter_method=left_navigation)\n        *   [OAuth](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [User Access Token Management](https://developers.tiktok.com/doc/oauth-user-access-token-management?enter_method=left_navigation)\n            *   [Client Access Token Management](https://developers.tiktok.com/doc/client-access-token-management?enter_method=left_navigation)\n            *   [Error Handling](https://developers.tiktok.com/doc/oauth-error-handling?enter_method=left_navigation)\n    *   [Webhooks](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/webhooks-overview?enter_method=left_navigation)\n        *   [Events](https://developers.tiktok.com/doc/webhooks-events?enter_method=left_navigation)\n        *   [Verification](https://developers.tiktok.com/doc/webhooks-verification?enter_method=left_navigation)\n*   [Login Kit](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/login-kit-overview?enter_method=left_navigation)\n    *   [Web](https://developers.tiktok.com/doc/login-kit-web?enter_method=left_navigation)\n    *   [Desktop](https://developers.tiktok.com/doc/login-kit-desktop?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/login-kit-ios-quickstart?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/login-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Manage User Access Tokens](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens?enter_method=left_navigation)\n    *   [QR Code Authorization](https://developers.tiktok.com/doc/login-kit-qr-code-authorization?enter_method=left_navigation)\n*   [Share Kit](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for iOS](https://developers.tiktok.com/doc/share-kit-ios-quickstart-v2?enter_method=left_navigation)\n    *   [Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n        *   [Share Kit for Android](https://developers.tiktok.com/doc/share-kit-android-quickstart-v2?enter_method=left_navigation)\n    *   [Green Screen Effect](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n        *   [Overview](https://developers.tiktok.com/doc/green-screen-kit?enter_method=left_navigation)\n*   [Content Posting API](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Direct Post](https://developers.tiktok.com/doc/content-posting-api-get-started?enter_method=left_navigation)\n    *   [Get Started - Upload](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation)\n    *   [Media Transfer Guide](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n        *   [Video](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Direct Post](https://developers.tiktok.com/doc/content-posting-api-reference-direct-post?enter_method=left_navigation)\n            *   [Upload](https://developers.tiktok.com/doc/content-posting-api-reference-upload-video?enter_method=left_navigation)\n        *   [Photo](https://developers.tiktok.com/doc/content-posting-api-reference-photo-post?enter_method=left_navigation)\n        *   [Query Creator Info](https://developers.tiktok.com/doc/content-posting-api-reference-query-creator-info?enter_method=left_navigation)\n        *   [Get Post Status](https://developers.tiktok.com/doc/content-posting-api-reference-get-video-status?enter_method=left_navigation)\n*   [Data Portability API](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/data-portability-api-get-started?enter_method=left_navigation)\n    *   [Data Portability FAQ](https://developers.tiktok.com/doc/data-portability-api-faq?enter_method=left_navigation)\n    *   [Data Portability Application Guidelines](https://developers.tiktok.com/doc/data-portability-api-application-guidelines?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Add Data Request](https://developers.tiktok.com/doc/data-portability-api-add-data-request?enter_method=left_navigation)\n        *   [Cancel Data Request](https://developers.tiktok.com/doc/data-portability-api-cancel-data-request?enter_method=left_navigation)\n        *   [Check Status of Data Request](https://developers.tiktok.com/doc/data-portability-api-check-status-of-data-request?enter_method=left_navigation)\n        *   [Download](https://developers.tiktok.com/doc/data-portability-api-download?enter_method=left_navigation)\n        *   [Data Types](https://developers.tiktok.com/doc/data-portability-data-types?enter_method=left_navigation)\n*   [Display API](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Overview](https://developers.tiktok.com/doc/display-api-overview?enter_method=left_navigation)\n    *   [Get Started](https://developers.tiktok.com/doc/display-api-get-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Get User Info](https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-query?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v2-video-list?enter_method=left_navigation)\n        *   [Video Object](https://developers.tiktok.com/doc/tiktok-api-v2-video-object?enter_method=left_navigation)\n*   [Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [About Research Tools](https://developers.tiktok.com/doc/about-research-api?enter_method=left_navigation)\n    *   [Getting Started - Research API](https://developers.tiktok.com/doc/research-api-get-started?enter_method=left_navigation)\n    *   [Getting Started - VCE](https://developers.tiktok.com/doc/vce-getting-started?enter_method=left_navigation)\n    *   [Frequently Asked Questions](https://developers.tiktok.com/doc/research-api-faq?enter_method=left_navigation)\n    *   [Codebook](https://developers.tiktok.com/doc/research-api-codebook?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/research-api-specs-query-videos?enter_method=left_navigation)\n        *   [Query User Info](https://developers.tiktok.com/doc/research-api-specs-query-user-info?enter_method=left_navigation)\n        *   [Query Video Comments](https://developers.tiktok.com/doc/research-api-specs-query-video-comments?enter_method=left_navigation)\n        *   [Query User Liked Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-liked-videos?enter_method=left_navigation)\n        *   [Query User Pinned Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-pinned-videos?enter_method=left_navigation)\n        *   [Query User Followers](https://developers.tiktok.com/doc/research-api-specs-query-user-followers?enter_method=left_navigation)\n        *   [Query User Following](https://developers.tiktok.com/doc/research-api-specs-query-user-following?enter_method=left_navigation)\n        *   [Query User Reposted Videos](https://developers.tiktok.com/doc/research-api-specs-query-user-reposted-videos?enter_method=left_navigation)\n        *   [Query Playlist Info](https://developers.tiktok.com/doc/research-api-specs-query-playlist-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Info](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-info?enter_method=left_navigation)\n        *   [Query TikTok Shop Products](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-products?enter_method=left_navigation)\n        *   [Query TikTok Shop Reviews](https://developers.tiktok.com/doc/research-api-specs-query-tiktok-shop-reviews?enter_method=left_navigation)\n    *   [VCE Reference](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/vce-query-videos?enter_method=left_navigation)\n        *   [Query Comments](https://developers.tiktok.com/doc/vce-query-video-comments?enter_method=left_navigation)\n        *   [Query Users](https://developers.tiktok.com/doc/vce-query-profiles?enter_method=left_navigation)\n        *   [Query Liked Videos](https://developers.tiktok.com/doc/vce-query-liked-videos?enter_method=left_navigation)\n        *   [Query Reposted Videos](https://developers.tiktok.com/doc/vce-query-reposted-videos?enter_method=left_navigation)\n        *   [Query Pinned Videos](https://developers.tiktok.com/doc/vce-query-pinned-videos?enter_method=left_navigation)\n        *   [Query Followers List](https://developers.tiktok.com/doc/vce-query-followers-list?enter_method=left_navigation)\n        *   [Query Following List](https://developers.tiktok.com/doc/vce-query-following-list?enter_method=left_navigation)\n        *   [Query Playlists](https://developers.tiktok.com/doc/vce-query-playlists?enter_method=left_navigation)\n        *   [Query TikTok Shop Data](https://developers.tiktok.com/doc/vce-query-tiktok-shop-data?enter_method=left_navigation)\n*   [Commercial Content API](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [Getting Started](https://developers.tiktok.com/doc/commercial-content-api-getting-started?enter_method=left_navigation)\n    *   [API Reference](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Ads](https://developers.tiktok.com/doc/commercial-content-api-query-ads?enter_method=left_navigation)\n        *   [Query Advertisers](https://developers.tiktok.com/doc/commercial-content-api-query-advertisers?enter_method=left_navigation)\n        *   [Get Ad Details](https://developers.tiktok.com/doc/commercial-content-api-get-ad-details?enter_method=left_navigation)\n        *   [Get Ad Report](https://developers.tiktok.com/doc/commercial-content-api-get-ad-report?enter_method=left_navigation)\n        *   [Query Commercial Content](https://developers.tiktok.com/doc/commercial-content-api-query-commercial-content?enter_method=left_navigation)\n        *   [Supported Countries](https://developers.tiktok.com/doc/commercial-content-api-supported-countries?enter_method=left_navigation)\n*   [Embed](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Player](https://developers.tiktok.com/doc/embed-player?enter_method=left_navigation)\n    *   [Embed Videos](https://developers.tiktok.com/doc/embed-videos?enter_method=left_navigation)\n    *   [Embed Creator Profiles](https://developers.tiktok.com/doc/embed-creator-profiles?enter_method=left_navigation)\n*   [Scopes](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Overview](https://developers.tiktok.com/doc/scopes-overview?enter_method=left_navigation)\n    *   [Scopes Reference](https://developers.tiktok.com/doc/tiktok-api-scopes?enter_method=left_navigation)\n*   [Legacy Products](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n    *   [Legacy API](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [Migrating](https://developers.tiktok.com/doc/tiktok-api-v2-introduction?enter_method=left_navigation)\n        *   [List Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-list?enter_method=left_navigation)\n        *   [User Info](https://developers.tiktok.com/doc/tiktok-api-v1-user-info?enter_method=left_navigation)\n        *   [Query Videos](https://developers.tiktok.com/doc/tiktok-api-v1-video-query?enter_method=left_navigation)\n        *   [Legacy User Access Token Guide](https://developers.tiktok.com/doc/legacy-user-access-guide?enter_method=left_navigation)\n        *   [Share Video API](https://developers.tiktok.com/doc/web-video-kit-with-web?enter_method=left_navigation)\n    *   [Legacy Mobile SDK](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n        *   [iOS](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Objective-C](https://developers.tiktok.com/doc/getting-started-ios-quickstart-objective-c?enter_method=left_navigation)\n            *   [Quickstart Swift](https://developers.tiktok.com/doc/getting-started-ios-quickstart-swift?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-ios-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-ios-download?enter_method=left_navigation)\n            *   [Login Kit With Objective-C](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-objective-c?enter_method=left_navigation)\n            *   [Login Kit With Swift](https://developers.tiktok.com/doc/login-kit-ios-login-kit-with-swift?enter_method=left_navigation)\n            *   [Video Kit With Objective-C](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-objective-c?enter_method=left_navigation)\n            *   [Video Kit With Swift](https://developers.tiktok.com/doc/video-kit-ios-video-kit-with-swift?enter_method=left_navigation)\n        *   [Android](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Quickstart](https://developers.tiktok.com/doc/getting-started-android-quickstart?enter_method=left_navigation)\n            *   [Handling Errors](https://developers.tiktok.com/doc/getting-started-android-handling-errors?enter_method=left_navigation)\n            *   [Download](https://developers.tiktok.com/doc/getting-started-android-download?enter_method=left_navigation)\n            *   [Login Kit with Android](https://developers.tiktok.com/doc/login-kit-android?enter_method=left_navigation)\n            *   [Video Kit With Android](https://developers.tiktok.com/doc/video-kit-android-video-kit-with-android?enter_method=left_navigation)\n            *   [Android FileProvider](https://developers.tiktok.com/doc/video-kit-android-android-fileprovider?enter_method=left_navigation)\n*   [Changelog](https://developers.tiktok.com/doc/changelog?enter_method=left_navigation)\n\n**Tip**: You can enable [email notifications](https://developers.tiktok.com/settings/#notification-setting) to get our latest product updates.\n\n**New:** Content Posting API now supports [sending photos](https://developers.tiktok.com/doc/content-posting-api-reference-photo-post#upload_)!\n\nGet Started\n===========\n\nThis guide shows you how to use the Content Posting API to upload content to TikTok.\n\nPrerequisites\n-------------\n\nTo successfully complete this tutorial, you will need the following:\n\n1.  A valid video if you want to upload videos:\n\n*   Ensure you have a video file in one of the [supported formats](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide#video_restrictions), such as MP4 + H.264, stored on your local machine.\n*   Alternatively, you can provide a URL of a video from your verified domain or URL prefix. Learn how to [verify your domain or URL prefix](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide#pull_from_url).\n*   Learn more about [video restrictions](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide#video_restrictions).\n\n3.  A valid photo if you want to upload photos:\n\n*   You need to provide a URL of a photo from your verified domain or URL prefix. Learn how to [verify your domain or URL prefix](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide#pull_from_url).\n*   Learn more about [photo restrictions.](https://developers.tiktok.com/doc/content-posting-api-media-transfer-guide#image_restrictions)\n\n5.  A [registered app](https://developers.tiktok.com/doc/getting-started-create-an-app) on the TikTok for Developers website.\n6.  Add the content posting API product to your app as shown below.\n\n![Image 10](https://sf16-va.tiktokcdn.com/obj/tiktok-open-platform/ttop_doc_images/f7e446de7adf4a97a8e606d1db9f0003)\n\n5.  Get approval and authorization of the `video.upload` scope. Learn more about [scopes](https://developers.tiktok.com/doc/scopes-overview).\n\n1.  Your app must be approved for the `video.upload` scope.\n2.  The target TikTok user must have authorized your app for the `video.upload` scope.\n\n7.  The access token and open ID of the TikTok user who authorized your app. Learn how to [obtain the access token and open ID](https://developers.tiktok.com/doc/login-kit-manage-user-access-tokens).\n\n  \n\nUpload draft to TikTok\n----------------------\n\nThis section demonstrates how to successfully upload videos or photos to TikTok for the user to review and post.\n\nYou should inform users that they must click on inbox notifications to continue the editing flow in TikTok and complete the post.\n\n<table><tbody><tr><td rowspan=\"1\" colspan=\"1\"><div style=\"display:flex;justify-content:center\"><img src=\"https://sf16-va.tiktokcdn.com/obj/tiktok-open-platform/ttop_doc_images/ac09fc68fa44ee57607dd758be37d5ec\" width=\"209.000000px\" height=\"452.000000px\"></div><p style=\"text-align:center\"><i>User notified of video upload</i></p></td><td rowspan=\"1\" colspan=\"1\"><div style=\"display:flex;justify-content:center\"><img src=\"https://sf16-va.tiktokcdn.com/obj/tiktok-open-platform/ttop_doc_images/06c4bc17718775074a9b2edaffa0e146\" width=\"209.000000px\" height=\"452.000000px\"></div><p style=\"text-align:center\"><i>User reviews and posts video</i></p></td></tr></tbody></table>\n\n### Upload a video\n\nTo initiate video upload on TikTok's servers, you must invoke the [Content Posting API - Video Upload](https://developers.tiktok.com/doc/content-posting-api-reference-upload-video) endpoint. You have the following two options:\n\n*   If you have the video file locally, set the source parameter to `FILE_UPLOAD` in your request.\n*   if the video is hosted on a URL, set the source parameter to `PULL_FROM_URL`.\n\n#### Example\n\nExample using `source=FILE_UPLOAD`:\n\nRequest:\n\n```bash\ncurl --location 'https://open.tiktokapis.com/v2/post/publish/inbox/video/init/' \\\n--header 'Authorization: Bearer act.example12345Example12345Example' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"source_info\": {\n        \"source\": \"FILE_UPLOAD\",\n        \"video_size\": exampleVideoSize,\n        \"chunk_size\" : exampleVideoSize,\n        \"total_chunk_count\": 1\n    }\n}'\n```\n\nResponse:\n\n```json\n200 OK\n\n{\n    \"data\": {\n        \"publish_id\": \"v_inbox_file~v2.123456789\",\n        \"upload_url\": \"https://open-upload.tiktokapis.com/video/?upload_id=67890&upload_token=Xza123\"    \n    },\n    \"error\": {\n         \"code\": \"ok\",\n         \"message\": \"\",\n         \"log_id\": \"202210112248442CB9319E1FB30C1073F3\"\n     }\n}\n```\n\n  \nExample using `source=PULL_FROM_URL`:\n\nRequest:\n\n```bash\ncurl --location 'https://open.tiktokapis.com/v2/post/publish/inbox/video/init/' \\\n--header 'Authorization: Bearer act.example12345Example12345Example' \\\n--header 'Content-Type: application/json' \\\n--data '{\n    \"source_info\": {\n        \"source\": \"PULL_FROM_URL\",\n        \"video_url\": \"https://example.verified.domain.com/example_video.mp4\",\n    }\n}'\n```\n\nResponse:\n\n```json\n200 OK\n\n{\n    \"data\": {\n        \"publish_id\": \"v_inbox_url~v2.123456789\"\n    },\n    \"error\": {\n         \"code\": \"ok\",\n         \"message\": \"\",\n         \"log_id\": \"202210112248442CB9319E1FB30C1073F4\"\n     }\n}\n```\n\nIf you are using `source=FILE_UPLOAD`:\n\n7.  Extract the `upload_url` and `publish_id` from the response data.\n8.  [Send the video](https://developers.tiktok.com/doc/content-posting-api-reference-upload-video#) from your local filesystem to the extracted `upload_url` using a PUT request. The video processing will occur asynchronously once the upload is complete.\n\n```bash\ncurl --location --request PUT 'https://open-upload.tiktokapis.com/video/?upload_id=67890&upload_token=Xza123' \\\n--header 'Content-Range: bytes 0-30567099/30567100' \\\n--header 'Content-Type: video/mp4' \\\n--data '@/path/to/file/example.mp4'\n```\n\nWith the `publish_id` returned earlier, check for status updates using the [Get Post Status](https://developers.tiktok.com/doc/content-posting-api-reference-get-video-status) endpoint.\n\n```bash\ncurl --location 'https://open.tiktokapis.com/v2/post/publish/status/fetch/' \\\n--header 'Authorization: Bearer act.example12345Example12345Example' \\\n--header 'Content-Type: application/json; charset=UTF-8' \\\n--data '{\n    \"publish_id\": \"v_inbox_file~v2.123456789\"\n}'\n```\n\n### Upload photos\n\nTo initiate photo upload on TikTok's server, you must invoke the [Content Posting API endpoint.](https://developers.tiktok.com/doc/content-posting-api-reference-photo-post)\n\nNote:\n\nThere are differences between the photo post endpoint and the existing video post endpoint.\n\n*   Use /v2/post/publish/content/init/ to upload photos instead of /v2/post/publish/inbox/video/init/\n*   The `post_mode` and `media_type` are required parameters in request.body\n*   There are additional parameters supported, such as title an\\`d description.\n\n#### Example\n\nRequest:\n\n```bash\ncurl --location 'https://open.tiktokapis.com/v2/post/publish/content/init/' \\\n--header 'Authorization: Bearer act.example12345Example12345Example' \\\n--header 'Content-Type: application/json' \\\n--data-raw '{\n    \"post_info\": {\n        \"title\": \"funny cat\",\n        \"description\": \"this will be a #funny photo on your @tiktok #fyp\"\n    },\n    \"source_info\": {\n        \"source\": \"PULL_FROM_URL\",\n        \"photo_cover_index\": 1,\n        \"photo_images\": [\n            \"https://tiktokcdn.com/obj/example-image-01.webp\",\n            \"https://tiktokcdn.com/obj/example-image-02.webp\"\n        ]\n    },\n    \"post_mode\": \"MEDIA_UPLOAD\",\n    \"media_type\": \"PHOTO\"\n}'\n```\n\nResponse:\n\n```json\n200 OK\n\n{\n    \"data\": {\n        \"publish_id\": \"p_pub_url~v2.123456789\"\n    },\n    \"error\": {\n         \"code\": \"ok\",\n         \"message\": \"\",\n         \"log_id\": \"202210112248442CB9319E1FB30C1073F3\"\n     }\n}\n```\n\n  \n\nOn this page\n\n[Prerequisites](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#prerequisites)[Upload draft to TikTok](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#upload_draft_to_tiktok)[Upload a video](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#upload_a_video)[Example](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#example)[Upload photos](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#upload_photos)[Example](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#example_2)\n\nOn this page\n\n![Image 13: TikTok for Developers](https://sf16-website.neutral.ttwstatic.com/obj/tiktok_web_static/developers/build/_assets/tt4d_logo_dark-49ded97f12a37311.svg)\n\nProducts\n\n*   [Share Kit](https://developers.tiktok.com/products/share-kit/)\n*   [Login Kit](https://developers.tiktok.com/products/login-kit/)\n*   [Content Posting API](https://developers.tiktok.com/products/content-posting-api/)\n*   [Research API](https://developers.tiktok.com/products/research-api/)\n*   [Display API](https://developers.tiktok.com/doc/display-api-get-started/)\n\n*   [Embed Videos](https://developers.tiktok.com/doc/embed-videos/)\n*   [Data Portability API](https://developers.tiktok.com/products/data-portability-api/)\n*   [Green Screen Kit](https://developers.tiktok.com/products/share-kit#green-screen)\n*   [Commercial Content API](https://developers.tiktok.com/products/commercial-content-api)\n\nOther platforms\n\n*   [TikTok Embeds](https://www.tiktok.com/embed \"TikTok Embeds\")\n*   [TikTok for Business](https://www.tiktok.com/business \"TikTok for Business\")\n*   [Advertise on TikTok](https://www.tiktok.com/business/&attr_source=tt_official_site&attr_medium=tt_official_site_guidance \"Advertise on TikTok\")\n*   [TikTok Creative Center](https://ads.tiktok.com/business/creativecenter/ \"TikTok Creative Center\")\n*   [TikTok.com](https://www.tiktok.com/ \"TikTok\")\n\nCompany\n\n*   [About TikTok](https://www.tiktok.com/about \"About TikTok\")\n*   [Newsroom](https://newsroom.tiktok.com/ \"TikTok Newsroom\")\n*   [Contact](https://www.tiktok.com/about/contact \"Contact TikTok\")\n*   [Careers](https://careers.tiktok.com/ \"TikTok Careers page\")\n*   [ByteDance](https://www.bytedance.com/ \"About ByteDance\")\n*   [Transparency Center](https://www.tiktok.com/transparency \"TikTok\")\n\n© 2025 TikTok[Terms of Service](https://www.tiktok.com/legal/page/global/tik-tok-developer-terms-of-service/en)[Privacy Policy](https://www.tiktok.com/legal/page/global/partner-privacy-policy/en)[Resources & Legal](https://developers.tiktok.com/doc/content-posting-api-get-started-upload-content?enter_method=left_navigation#site-footer)\n\nClose\n"}]}}], "Webhook1": [{"json": {"headers": {"host": "n8n-c.theracompany.top", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "161", "accept": "*/*", "accept-encoding": "gzip, deflate, br, zstd", "accept-language": "en-US,en;q=0.9,pt-BR;q=0.8,pt;q=0.7", "authorization": "Bearer [token]", "content-type": "application/json", "origin": "https://studio.ottomator.ai", "priority": "u=1, i", "referer": "https://studio.ottomator.ai/", "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"Windows\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "**************", "x-forwarded-host": "n8n-c.theracompany.top", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "96536f9a1db8", "x-real-ip": "**************"}, "params": {}, "query": {}, "body": {"query": "oi", "session_id": "811ff825-8548-4790-bced-0036225d0b35", "user_id": "auth0|6762ea684fa442a9c23d8daa", "request_id": "37de93f1-25de-4d47-9f9a-c3d6e3121b06"}, "webhookUrl": "https://n8n-c.theracompany.top/webhook/api/agent/web-search/v1", "executionMode": "production"}}]}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prep Output Fields1", "type": "main", "index": 0}]]}, "Prep Output Fields1": {"main": [[{"node": "Respond to Webhook1", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "Prep Input Fields1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Prep Input Fields1": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Filter_website_scraping", "type": "main", "index": 0}]]}, "website_scraping": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Filter_website_scraping": {"main": [[{"node": "Add User Message to DB1", "type": "main", "index": 0}]]}, "Add User Message to DB1": {"main": [[{"node": "normalization", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}], [{"node": "get_scraping_website", "type": "main", "index": 0}]]}, "normalization": {"main": [[{"node": "split_out_urls", "type": "main", "index": 0}]]}, "split_out_urls": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "get_scraping_website": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Add User Message to DB2", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "5553bff2-5359-41ea-906a-04a0278d76fd", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "v2Dyb7UQW3T6qPd4", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}