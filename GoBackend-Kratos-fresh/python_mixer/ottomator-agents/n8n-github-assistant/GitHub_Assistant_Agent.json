{"name": "GitHub Assistant Agent", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "2da24576-e0e8-4e91-9138-4f022e167476", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1700, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "8a1853f3-714d-4fec-be4d-9fd346d2fad1", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [760, 400]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "31f877cd-5d80-446e-ba86-c35891bd6ea8", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1460, 400]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields').item.json.session_id}}", "tableName": "messages", "contextWindowLength": 1}, "id": "f640e793-22ca-4eb6-98c3-f13ac2f336b0", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [1060, 620], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {}, "id": "bf645d5c-1c61-4fe6-93bf-300f351fb22d", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [580, 1000]}, {"parameters": {"jsCode": "function parseGitHubUrl(githubUrl) {\n    const regex = /github\\.com[:/]([^/]+)\\/([^/]+?)(?:\\.git)?$/;\n    const match = githubUrl.match(regex);\n    \n    if (!match) {\n        return \"Invalid GitHub URL format\";\n    }\n    \n    const [, org, repo] = match;\n    return { org, repo };\n}\n\nreturn parseGitHubUrl($('Execute Workflow Trigger').item.json.query.repository_url)"}, "id": "f126ee92-6d6d-4784-ba97-3191891a1f81", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [820, 1000]}, {"parameters": {"url": "={{ `https://api.github.com/repos/${$('Code').item.json.org}/${$('Code').item.json.repo}/git/trees/main?recursive=1` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi", "options": {}}, "id": "************************************", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, 920], "credentials": {"githubApi": {"id": "iSKkYzHzUhtOqGyX", "name": "GitHub account"}}}, {"parameters": {"jsCode": "function formatRepoStructure(tree) {    \n    // Build directory structure\n    const excludedPaths = ['.git/', 'node_modules/', '__pycache__/'];\n    \n    const structure = tree\n        .filter(item => !excludedPaths.some(excluded => item.path.includes(excluded)))\n        .map(item => `${item.type === 'tree' ? '📁 ' : '📄 '}${item.path}`);\n    \n    return structure.join('\\n');\n}\n\nreturn {structure: formatRepoStructure($('HTTP Request').all()[0].json.tree)}"}, "id": "9cfda6d0-b9bf-4fe2-93b3-4158ec0a70e9", "name": "Code1", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1860, 920]}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Execute Workflow Trigger').item.json.action }}", "rightValue": "get_repo_structure", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d2a78a7b-4708-4efd-a219-ed609b37a399", "leftValue": "={{ $('Execute Workflow Trigger').item.json.action }}", "rightValue": "get_file_content", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {}}, "id": "70ff44de-25f1-49a3-b8b1-c6fb6788007b", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [1080, 1000]}, {"parameters": {"url": "={{ `https://raw.githubusercontent.com/${$('Code').item.json.org}/${$('Code').item.json.repo}/main/${$('Execute Workflow Trigger').item.json.query.file_path}` }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "githubApi", "options": {}}, "id": "************************************", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1620, 1180], "credentials": {"githubApi": {"id": "iSKkYzHzUhtOqGyX", "name": "GitHub account"}}}, {"parameters": {"name": "get_file_content", "description": "Use this tool to get the contents of a file in the GitHub repository once you know the path to the file. This is the tool to use to search around in files to get the answer for the user.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "responsePropertyName": "data", "fields": {"values": [{"name": "session_id", "stringValue": "={{ $json.session_id }}"}, {"name": "action", "stringValue": "get_file_content"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"repository_url\": \"The URL to the GitHub repository\",\n    \"file_path\": \"The path to the file in the repository (README.md, public/icon.svg, etc.)\"\n}"}, "id": "9a41aa4c-7590-4ed1-9c31-882b45cf6108", "name": "Call Get Repo File Content Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1380, 620]}, {"parameters": {"name": "get_repo_structure", "description": "Use this tool to get the full structure of a GitHub repository so you know how to navigate it and get contents of specific files in the repo.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "responsePropertyName": "structure", "fields": {"values": [{"name": "session_id", "stringValue": "={{ $json.session_id }}"}, {"name": "action", "stringValue": "get_repo_structure"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"repository_url\": \"The URL to the GitHub repository\"\n}"}, "id": "987f3dad-7e61-4852-b06c-6d595ed9323a", "name": "Call Get Repo Structure Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1220, 620]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Execute Workflow Trigger').item.json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"-> Getting file structure of repository...\",\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "8384d48f-24c1-42d2-8987-8f975a8dd9b6", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1360, 920], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Execute Workflow Trigger').item.json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `-> Getting contents of: ${$('Execute Workflow Trigger').item.json.query.file_path}`,\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "eddd49d0-84f2-4705-8e83-a5ffc820ce62", "name": "Add AI Message to DB1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1360, 1180], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"modelName": "models/gemini-2.0-flash-exp", "options": {}}, "id": "7288c0ee-84ff-4a6e-a6cf-cbdb73ad7758", "name": "Google Gemini Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "typeVersion": 1, "position": [920, 620], "credentials": {"googlePalmApi": {"id": "mOuOVUXJMjWz8V9h", "name": "Google Gemini(PaLM) Api account"}}}, {"parameters": {"promptType": "define", "text": "={{$('Prep Input Fields').item.json.query}}", "options": {"systemMessage": "=You are a coding expert with access to GitHub to help the user manage their repository and get information from it.\n\nYour only job is to assist with this and you don't answer other questions besides describing what you are able to do.\n\nDon't ask the user before taking an action, just do it.\n\nWhen answering a question about the repo, always start your answer with the full repo URL in brackets and then give your answer on a newline. Like:\n\n[Using https://github.com/[repo URL from the user]]\n\nYour answer here..."}}, "id": "c05c1e39-a355-4bfb-9bdd-9a3e99eeefb2", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [1040, 400]}, {"parameters": {"httpMethod": "POST", "path": "invoke-gith<PERSON>-assistant", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "264d0388-9c1a-4a0a-beb0-4546d0b94957", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [500, 400], "webhookId": "e470c2d1-e16e-4c21-9b06-8093d4d76056", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "# GitHub Assistant Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nThis n8n-powered agent serves as your intelligent GitHub repository assistant. It can analyze and navigate through GitHub repositories, helping you understand their structure and content. The agent can answer questions about specific files, explore repository layouts, and provide detailed information about repository contents.\n\n## Features\n\n- Repository structure analysis and navigation\n- File content lookup and exploration\n- Intelligent context tracking for follow-up questions\n- Secure GitHub API integration\n\n## Use Cases\n\n1. **Repository Exploration**\n   - Understand the structure of any GitHub repository\n   - Navigate through directories and files\n   - Locate specific files or code snippets\n\n2. **Code Understanding**\n   - Get explanations about specific files\n   - Understand code organization\n   - Find relevant documentation\n\n3. **Repository Analysis**\n   - Analyze repository structure\n   - Identify key components and files\n   - Understand project organization", "height": 813.463606813769, "width": 651.0139534883727, "color": 6}, "id": "2b32d71d-a6e7-49c8-a8f3-10eb88fd64f0", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-240, 400]}], "pinData": {"Execute Workflow Trigger": [{"json": {"session_id": "test", "action1": "get_file_content", "action": "get_repo_structure", "query": {"repository_url": "https://github.com/stackblitz-labs/bolt.diy", "file_path": "README.md"}}}], "Webhook": [{"json": {"headers": {"host": "n8n.[your n8n url].com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer YOUR BEARER TOKEN", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.[your n8n url].com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "https://github.com/stackblitz-labs/bolt.diy Read the contents of all CI/CD files and describe what is being done.", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.[your n8n url].com/webhook-test/invoke-agent", "executionMode": "test"}}]}, "connections": {"Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Code1", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}], [{"node": "Add AI Message to DB1", "type": "main", "index": 0}]]}, "Call Get Repo Structure Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Call Get Repo File Content Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "Add AI Message to DB1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Google Gemini Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8601877f-ed4f-4d45-a4a8-a23e3c296e18", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "Q70IsMusOVLpZYtD", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}