{"name": "MCP Agent <PERSON>", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "711b50da-0246-45c7-877a-cefef621de04", "name": "When chat message received", "webhookId": "742af357-a45c-4670-a8c1-f0319a4fb25b"}, {"parameters": {"options": {"systemMessage": "=You are a helpful assistant who has access to a bunch of tools to assist with user queries. Before you try to execute any tool, you need to call the tool to list available tools for the capability you want to leverage.\n\nWhen you list tools available, you'll get a list back of items that look like:\n\nname:[tool_name]\ndescription:[tool description to tell you when and how to use the tool]\nschema\n0:[param 1]\n1:[param 2]\n...\nn-1:[param n]\n\nThen when you call a tool, you need to give the tool name exactly as given to you, and the tool parameters need to be a json object like:\n\n{\n  \"param 1\": \"param 1 value\",\n  ...\n  \"param n\": \"param n value\"\n}\n\nIf there are no parameters for the tool, just pass in an empty object.\n\nFor the file system, you have access to the /files directory and that is it."}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [312, 0], "id": "209b7dea-c14a-4b89-b95b-bfd23d5899b3", "name": "AI Agent"}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [280, 220], "id": "0085bedd-77b0-4deb-aba8-8bb56ad82c00", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to list all the Brave search tools that are available."}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [440, 220], "id": "688f7c08-d47a-4068-88a4-5ea993199e0e", "name": "List Brave Tools", "credentials": {"mcpClientApi": {"id": "8dZpCr8DTCqWHuKl", "name": "Brave Search"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to execute a Brave tool once you have called the tool to list available Brave tools.", "operation": "executeTool", "toolName": "={{ $fromAI('tool_name') }}", "toolParameters": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Tool_Parameters', ``, 'json') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [600, 220], "id": "9e99c89a-a1c2-4061-9d68-2a91be5cf450", "name": "Execute Brave Tool", "credentials": {"mcpClientApi": {"id": "8dZpCr8DTCqWHuKl", "name": "Brave Search"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to list all the tools available to you to perform Convex actions. Only ever call this tool once!"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [780, 220], "id": "9d766e0b-29a1-449a-a1df-585aca1d6739", "name": "List Convex Tools", "credentials": {"mcpClientApi": {"id": "6wKkBajlitsRH7dL", "name": "Convex"}}}, {"parameters": {"descriptionType": "manual", "toolDescription": "Use this tool to execute tools related to Convex.", "operation": "executeTool", "toolName": "={{ $fromAI('tool_name') }}", "toolParameters": "={{ $fromAI('Tool_Parameters') }}"}, "type": "n8n-nodes-mcp.mcpClientTool", "typeVersion": 1, "position": [940, 220], "id": "6b21a7da-9769-4c44-9fca-80f290409ef3", "name": "Execute Convex Tool", "credentials": {"mcpClientApi": {"id": "6wKkBajlitsRH7dL", "name": "Convex"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [120, 220], "id": "702e1bad-25af-4010-a390-a3b9ea1d64e5", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}], "pinData": {}, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "List Brave Tools": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Execute Brave Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "List Convex Tools": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Execute Convex Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "8898335f-da6e-4458-b7f7-537f9b62f159", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "ldC1RYzwYGzlH4qS", "tags": []}