{"name": "Reddit Small Business Researcher", "nodes": [{"parameters": {"operation": "search", "subreddit": "smallbusiness", "keyword": "={{ $('Edit Fields').first().json.query }}", "limit": 10, "additionalFields": {"sort": "hot"}}, "id": "b5436343-6048-4f41-a60d-77c7cb637c1f", "name": "Reddit", "type": "n8n-nodes-base.reddit", "position": [-1420, 580], "typeVersion": 1, "credentials": {"redditOAuth2Api": {"id": "XYuYmVhQhAN0zACJ", "name": "Reddit account"}}}, {"parameters": {"keepOnlySet": true, "values": {"number": [{"name": "upvotes", "value": "={{ $json.ups }}"}, {"name": "subredditSize", "value": "={{ $json.subreddit_subscribers }}"}], "string": [{"name": "originalPost", "value": "={{ $json.selftext }}"}, {"name": "subreddit", "value": "={{ $json.subreddit }}"}, {"name": "date", "value": "={{ DateTime.fromSeconds($json.created).toLocaleString() }}"}, {"name": "url", "value": "={{ $json.url }}"}]}, "options": {}}, "id": "6a6c9729-2e1f-4865-9cc6-7bb308cd1380", "name": "Set", "type": "n8n-nodes-base.set", "position": [-600, 740], "typeVersion": 1}, {"parameters": {"conditions": {"number": [{"value1": "={{ $json.ups }}", "operation": "largerEqual", "value2": "=2"}], "string": [{"value1": "={{ $json.selftext }}", "operation": "isNotEmpty"}], "dateTime": [{"value1": "={{ DateTime.fromSeconds($json.created).toISO() }}", "value2": "={{ $today.minus({days: 180}).toISO() }}"}]}}, "id": "785fc0ed-6e1d-4968-976d-0215d89f4069", "name": "IF", "type": "n8n-nodes-base.if", "position": [-1220, 820], "typeVersion": 1}, {"parameters": {"mode": "combine", "combinationMode": "mergeByPosition", "options": {"fuzzyCompare": false, "includeUnpaired": true}}, "id": "b397a537-7f53-4995-af43-5e464facc383", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [-80, 800], "typeVersion": 2}, {"parameters": {"options": {}}, "id": "81b4aca8-1f2e-4321-9391-5be6024632e7", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-580, 520], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9ababf7b-4d85-4d5d-854b-6f0db81ea88b", "leftValue": "={{ $json.output }}", "rightValue": "Yes", "operator": {"type": "string", "operation": "startsWith"}}], "combinator": "and"}, "options": {}}, "id": "a33f150d-7787-4f4b-bc4a-5a06b9ec7b52", "name": "Filter", "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [140, 720]}, {"parameters": {"options": {}}, "id": "eb7fdf59-7919-4a1f-b525-9816ff19754a", "name": "OpenAI Chat Model2", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [260, 480], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {}, "id": "c9e3853d-7eac-41eb-aaca-0ad06a3cbbdf", "name": "Limit", "type": "n8n-nodes-base.limit", "typeVersion": 1, "position": [-840, 820]}, {"parameters": {"options": {}}, "id": "40edfc98-f3f6-4f74-b69e-2761bcc903fd", "name": "Summarization Chain", "type": "@n8n/n8n-nodes-langchain.chainSummarization", "typeVersion": 2, "position": [460, 460]}, {"parameters": {"mode": "combine", "combineBy": "combineByPosition", "numberInputs": 3, "options": {}}, "id": "69f53f33-8802-4469-87ed-312fb37de59c", "name": "Merge1", "type": "n8n-nodes-base.merge", "typeVersion": 3, "position": [920, 800]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Edit Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ { \"type\": \"ai\", \"content\": `Summary of post for inspiration: ${$json.response.text}\\n\\n${$json.message.content}`, \"data\": {}, \"additional_kwargs\": {}, \"response_metadata\": {} } }}"}]}}, "id": "5a12c2d7-2d36-43d1-8b0e-6acb00d429aa", "name": "Supabase", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1140, 800], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true\n}", "options": {}}, "id": "4fc55105-a5ef-41cf-b1e2-a564984f38a6", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1360, 800]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "b4ecc4d9-6272-4d3e-b07e-cfe0b34e5296", "name": "Supabase2", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [-1620, 820], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "72196ae5-7d70-4332-9058-a8f4bde9677b", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1820, 580]}, {"parameters": {"agent": "conversationalAgent", "promptType": "define", "text": "=Decide whether this Reddit post is describing a business-related problem or a need for a solution. The post should mention a specific challenge or requirement that a business is trying to address.\n\nReddit Post: {{ $json.originalPost }}\n\nIs this post about a business problem or need for a solution? output only Yes or No", "options": {}}, "id": "56960826-57e4-4058-ad9c-27adbb920b08", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-400, 540]}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Based on the following Reddit post, suggest a business idea or service that I could create to help solve this problem for this business and others with similar needs. The solution must relate to the topic of {{ $('Edit Fields').first().json.query }}\n\nReddit Post: \"{{ $json.originalPost }}\"\n\nProvide a concise description of a business idea or service that would address this issue effectively for multiple businesses facing similar challenges.\n\n"}]}, "options": {}}, "id": "cb0a8a7f-8a91-45c3-8bac-26541cd892d9", "name": "OpenAI", "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.6, "position": [460, 640], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"httpMethod": "POST", "path": "invoke-business-idea-researcher", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "059130a6-f166-4e63-b569-3b203c03d535", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-2040, 820], "webhookId": "ba5e9235-01af-47f8-9f72-29df862bfa0f", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "# Reddit Small Business Researcher\n\nAuthor: [<PERSON><PERSON><PERSON>](https://www.youtube.com/@AI-GPTWorkshop)\n\nAn n8n-powered agent that researches small business ideas by analyzing Reddit discussions from r/smallbusiness. It helps validate business concepts by finding relevant experiences, challenges, and insights from real business owners.\n\n## Features\n\n- Searches r/smallbusiness for relevant discussions\n- Filters high-quality posts based on:\n  - Upvotes (minimum of 2)\n  - Post content length\n  - Recent posts (within last 180 days)\n- Uses AI to analyze and summarize findings\n- Provides actionable insights and potential challenges\n", "height": 445.**************, "width": 652.*************, "color": 6}, "id": "37a330f9-43d5-4ce6-8617-43d7d233e827", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-2780, 540]}], "pinData": {}, "connections": {"IF": {"main": [[{"node": "Limit", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Reddit": {"main": [[{"node": "IF", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Filter": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}, {"node": "Summarization Chain", "type": "main", "index": 0}, {"node": "Merge1", "type": "main", "index": 2}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Summarization Chain", "type": "ai_languageModel", "index": 0}]]}, "Limit": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}, "Summarization Chain": {"main": [[{"node": "Merge1", "type": "main", "index": 0}]]}, "Merge1": {"main": [[{"node": "Supabase", "type": "main", "index": 0}]]}, "Supabase": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Supabase2", "type": "main", "index": 0}]]}, "Supabase2": {"main": [[{"node": "Reddit", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Merge1", "type": "main", "index": 1}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "ffff9fe3-17e4-4263-bf2f-4aaeb8068704", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "jy56YXivjHFu6OEJ", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}