{"name": "Smart Select Multi Tool Agent", "nodes": [{"parameters": {"operation": "clone", "repositoryPath": "={{ $json.git_directory }}", "sourceRepository": "https://github.com/danielmiessler/fabric.git"}, "type": "n8n-nodes-base.git", "typeVersion": 1, "position": [1260, 1100], "id": "9f7e006d-1e60-4f40-a69c-b5245d5c7551", "name": "Clone fabric git", "onError": "continueRegularOutput"}, {"parameters": {"assignments": {"assignments": [{"id": "2f090819-692c-4f18-818d-5084726b6e10", "name": "git_directory", "value": "/tmp/fabric", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1040, 1100], "id": "58053e19-f5cf-43ce-9432-8c620cd106b0", "name": "set git path"}, {"parameters": {"command": "=ls {{ $('set git path').item.json.git_directory }}/patterns "}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [1500, 1100], "id": "38f86a94-f784-46af-b1f5-f8a258e2f7c0", "name": "get available prompts"}, {"parameters": {"jsCode": "// Input Data (replace with the actual data input in n8n)\nconst inputString = $json[\"stdout\"];\n\n// Split the input string into lines\nconst lines = inputString.split('\\n').map(line => line.trim());\n\n// Return the list as a single item with a \"patterns\" property\nreturn [{ patterns: lines }];\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1740, 1100], "id": "276f1855-eb26-4ca7-bf40-fa5d7972f59f", "name": "transform pattern list to json"}, {"parameters": {"promptType": "define", "text": "={{ $('cut first 100 characters').item.json.truncatedText }}", "messages": {"messageValues": [{"message": "=You are highly intelligent and accurate sentiment analyzer. Analyze the sentiment of the provided text. Categorize it into one of the following: {{ $json.patterns }}. Output the pattern (e.g. agility_story) and nothing else."}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [2080, 1100], "id": "0526b68f-2255-475a-991e-8eb34f9e19eb", "name": "decide what pattern should be used"}, {"parameters": {"jsCode": "// n8n Function Node\nreturn items.map(item => {\n    // Get the input text\n    const inputText = item.json.message.content || '';\n    \n    // Cut the text to the first 100 characters\n    const truncatedText = inputText.substring(0, 100);\n    \n    // Return the truncated text\n    return {\n        json: {\n            truncatedText,\n        },\n    };\n});\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [800, 1100], "id": "0803454c-adb0-4889-a02e-fe56f9e8cb03", "name": "cut first 100 characters"}, {"parameters": {"command": "=cat {{ $('set git path').item.json.git_directory }}/patterns/{{ $json.text }}/system.md"}, "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [2560, 1100], "id": "0d36c23f-35f2-4cc9-a81e-9565071b12e1", "name": "get_selected_pattern_system_prompt"}, {"parameters": {"assignments": {"assignments": [{"id": "18b7c3b4-8581-44ca-a75e-324b4c7db6a5", "name": "system_prompt", "value": "={{ $json.stdout }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2860, 1240], "id": "a152911e-06b7-4f65-93ee-49917c80acbb", "name": "set_system_prompt"}, {"parameters": {"promptType": "define", "text": "={{ $('Prep Input Fields').item.json.query }}", "messages": {"messageValues": [{"message": "={{ $json.system_prompt }}"}]}}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [240, 1520], "id": "907320fc-0559-405e-8209-44008c248d3c", "name": "Run chatInput with specialized tool"}, {"parameters": {"assignments": {"assignments": [{"id": "6bf569bc-9c4a-471c-a9c7-0fb0d660de4f", "name": "output", "value": "=Based on the Request: {{ $('cut first 100 characters').item.json.truncatedText }}\n\nI decided to use the tool \"{{ $('decide what pattern should be used').item.json.text }}\"\n\nThis is the Result:\n{{ $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [620, 1520], "id": "6b3206c1-ddd1-44a3-aaa6-d9dd3927039d", "name": "set output"}, {"parameters": {"content": "## This is basically sentiment analysis but with the available categories as dynamic variables.", "height": 360, "width": 320, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2040, 900], "id": "a0bc524c-262f-4a9c-8cf7-7927dac1f4b2", "name": "Sticky Note5"}, {"parameters": {"content": "## Tip:\nDepending on Model and Hardware you should experiment with context limit and max token lenth for respond to fin etune", "height": 280, "width": 420, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1580, 1540], "id": "6bd0983f-1fa4-432d-8996-74daa138f920", "name": "Sticky Note6"}, {"parameters": {"content": "# This is a smart selecting Multi-Tool-Agent / Masteragent based on fabric\n\n## it decides what tool to use based on the first 100 characters of the prompt. All tools/patterns/prompts from the fabric repository are imported.\n\nSetup needs to be done manually via clicking \"Test workflow\". You may need to adjust directory and or permissions.\n\n\nList of available prompts:\nhttps://github.com/danielmiessler/fabric/tree/main/patterns\n\nGithub Repository of fabric:\nhttps://github.com/danielmiessler/fabric", "height": 340, "width": 2780}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0], "id": "4771c111-7d89-4f31-8573-2f2e185de6b1", "name": "Sticky Note4"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1640, 1700], "id": "d7869962-80ec-424c-8748-f0edaf3b7967", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [1840, 1700], "id": "0316b440-828b-47db-b57d-73ba78d521b3", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": ""}]}}}, "id": "32dd6495-0870-427b-9f32-a4bbe367c67a", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1340, 1520]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "052c37ad-e9d7-4df6-848d-f3cedff4112f", "name": "Add User Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [560, 1100], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json.output,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "7df20368-6ae6-49c2-b38a-80ee3ba03dc4", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [860, 1520], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "bf3d7819-97dd-4737-9950-1d301997e260", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [320, 1100]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "6dde63a9-cf9b-486e-8784-fa62ea2e6a34", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1100, 1520]}, {"parameters": {"httpMethod": "POST", "path": "invoke-fabric-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "3564683a-6840-4234-a260-4811df45ec07", "name": "Webhook1", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [100, 1100], "webhookId": "9a6c4630-b422-4d42-b894-81ecfe881ffe", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}], "pinData": {"Webhook1": [{"json": {"headers": {"host": "n8n.[your n8n url].com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer YOUR BEARER TOKEN", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.[your n8n url].com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "Supabase", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.[your n8n url].com/webhook-test/invoke-agent", "executionMode": "test"}}]}, "connections": {"set git path": {"main": [[{"node": "Clone fabric git", "type": "main", "index": 0}]]}, "get available prompts": {"main": [[{"node": "transform pattern list to json", "type": "main", "index": 0}]]}, "transform pattern list to json": {"main": [[{"node": "decide what pattern should be used", "type": "main", "index": 0}]]}, "decide what pattern should be used": {"main": [[{"node": "get_selected_pattern_system_prompt", "type": "main", "index": 0}]]}, "cut first 100 characters": {"main": [[{"node": "set git path", "type": "main", "index": 0}]]}, "get_selected_pattern_system_prompt": {"main": [[{"node": "set_system_prompt", "type": "main", "index": 0}]]}, "set_system_prompt": {"main": [[{"node": "Run chatInput with specialized tool", "type": "main", "index": 0}]]}, "Run chatInput with specialized tool": {"main": [[{"node": "set output", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Run chatInput with specialized tool", "type": "ai_languageModel", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "decide what pattern should be used", "type": "ai_languageModel", "index": 0}]]}, "Add User Message to DB": {"main": [[{"node": "cut first 100 characters", "type": "main", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook1": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "set output": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Clone fabric git": {"main": [[{"node": "get available prompts", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "bb23f0d1-7e70-4409-b1b5-a2933c1523a5", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "cdqdZBYXEvuSPsfp", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}