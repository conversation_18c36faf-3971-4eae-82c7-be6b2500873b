Introduction

The Live Agent Studio allows users to interact with community-created AI agents through a unified system. As a developer, you can create an agent that can be plugged into this platform, enabling users to easily try your agent without you having to manage authentication, handle rate limiting, or build a frontend.
The key to integration is ensuring your agent communicates using the expected input/output formats and manages conversation history as specified.
You can even test if your agent is compatible with the Live Agent Studio and use the Studio as a frontend for your agent with our Agent 0 Playground:
Agent 0 Playground

Agent Requirements
Technical Requirements
To ensure compatibility with the Live Agent Studio, your agent must:
	•	Accept specific input parameters in a POST request.
	•	Produce output JSON in the required format.
	•	Manage conversation history by storing messages in a SQL database (preferably Supabase).
NOTE: These requirements don't apply to Voiceflow agents because of the direct integration with the Studio.
Access and Authorization
	•	If your agent requires acessing user's third party accounts (e.g., Gmail, Slack, etc.), you must implement the authorization flow yourself.
Language Models
	•	For the Hackathon specifically, we recommend using small and fast LLMs that cost less than $0.1 per 1M input tokens, such as:
	•	Gemini 2.0 Flash
	•	Claude 3.5 Haiku
	•	Llama 3.3 70b
	•	GPT-4o-mini
	•	With a good agent architecture, larger models like o1 should not be necessary. Take it as a challenge if anything!
API Usage
	•	Only use APIs that:
	•	Cost less than $0.01 per agent execution
	•	Don't have rate limits that would prevent hundreds of voters from trying the agent
	•	Example: Use the Brave API instead of SerpAPI for web search
Development Platforms
The agent can be built with any of the following:
	•	Anything if it's within a Docker container
	•	Any Python framework. Suggestions for frameworks:
	•	Pydantic AI
	•	LangChain
	•	LlamaIndex
	•	n8n
	•	Flowise
	•	Voiceflow

Sample Agents
To help you get started, we have provided two sample agent implementations:
	•	Sample n8n Agent - A barebones example of implementing an n8n agent compatible with the Live Agent Studio.
	•	Sample Python Agent - A basic implementation example using Python with FastAPI.
	•	For Voiceflow, there is a direct integration with the Live Agent Studio so no need to create anything custom!
Input Parameters
When the Live Agent Studio invokes your agent, it will send a POST request to your agent's webhook URL with the following JSON payload:
{
  "query": "User's input text",
  "user_id": "Unique user identifier",
  "request_id": "Unique request identifier",
  "session_id": "Conversation session identifier",
  "files": [... see the Handling File Uploads section if applicable to your agent]
}
Parameter Details
	•	query: (String) The user's input or question. It is sanitized and limited to 1000 characters.
	•	user_id: (String) A unique identifier for the authenticated user.
	•	request_id: (String) A unique ID for the request to prevent duplicate processing.
	•	session_id: (String) A unique ID representing the conversation session.
	•	files: (Array, optional) An array of file objects that were uploaded by the user. See the Handling File Uploads section for details.

Output Parameters
Your agent must respond with a JSON object indicating the success or failure of processing the user's query:
{
  "success": true
}
Response Details
	•	success: (Boolean) Indicates whether the agent successfully processed the request. Use true for success and false for failure.
Note: Since all the AI messages and data are stored directly in the database, you do not need to return any additional data in the response.
Data Output Format
While the response JSON is simplified, your agent can still include additional data in the messages stored in the database. This data can be utilized by the frontend to enhance the user experience.
Storing Messages in the Database
Your agent is responsible for storing the conversation history by inserting messages into a SQL database unless you are building a Voiceflow agent. This ensures that the conversation history is maintained and can be retrieved by the Live Agent Studio. While any PostgreSQL-compatible database can be used, using Supabase directly is preferred due to its seamless integration with our platform.
Use this SQL to create the messages table:
-- Enable the pgcrypto extension for UUID generation
-- Note: If you're using Supabase, the pgcrypto extension is already enabled by default.
CREATE EXTENSION IF NOT EXISTS pgcrypto;

CREATE TABLE messages (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    session_id TEXT NOT NULL,
    message JSONB NOT NULL
);

CREATE INDEX idx_messages_session_id ON messages(session_id);
CREATE INDEX idx_messages_created_at ON messages(created_at);
For n8n, if you use the Agent node the table will automatically be created for you so running this SQL is not necessary.
If you're using Supabase, you'll also want to enable realtime updates for testing (with Agent 0) by running this SQL command:
alter publication supabase_realtime add table messages;
Required Fields
When inserting a message into the messages table, the following fields are required:
	•	session_id: The conversation session ID provided in the input parameters.
	•	message: A JSON (jsonb) object containing the message details.

Message Field Structure
The message field must be a JSON object with the following structure (compatible with the AI agent node in n8n):
For User Messages
{
  "type": "human",
  "content": "User's input text"
}
For Agent Responses
{
  "type": "ai",
  "content": "Agent's response text",
  "data": {
    "...additional info for the frontend..."
  }
}
Field Descriptions
	•	type: (String) Indicates the message source. Use "human" for user messages and "ai" for agent responses.
	•	content: (String) The text content of the message.
	•	data: (Object) Optional. Additional data for the frontend, matching the data field in your processing.
Inserting into Supabase
To insert a message into the messages table:
	1	Use the session_id from the input parameters.
	2	Create the message JSON object as per the structure above.
	3	Insert a new record with the session_id and message fields.

Example SQL-like pseudocode:
INSERT INTO messages (session_id, message)
VALUES ('<session_id>', '<message_json_object>');
Note: If you are using a different PostgreSQL database, ensure that the table structure and insertion methods are compatible.
Hosting and Deployment
To ensure that your agent fully integrates with the Live Agent Studio's features, we offer to host and run your agent on our infrastructure. The biggest benefit to you is you don’t have to pay for the LLM usage! On top of that, this provides:
	•	Consistency: By hosting the agent, we can ensure it adheres to all platform requirements, including security, scalability, and performance.
	•	Feature Integration: Hosting the agent allows us to handle features like authentication, rate limiting, and logging without additional effort on your part.
	•	Maintenance: Our team will manage the operational aspects, so you can focus on developing and improving your agent's capabilities.
How It Works
	1	Code Submission:
	•	Provide us with your agent's codebase or the workflow JSON if it’s an n8n/Voiceflow agent, ensuring it follows the guidelines outlined in this document.
	2	Deployment:
	•	Our team will deploy the agent within our infrastructure, configuring it to work seamlessly with the Live Agent Studio.
	3	Testing:
	•	We'll perform comprehensive testing to ensure your agent functions correctly and efficiently.
	4	Monitoring and Updates:
	•	We'll monitor the agent's performance and work with you on any necessary updates or improvements.
Information to Provide
To integrate your agent into Live Agent Studio, please provide the following information:
	•	Your Name: Your full name or your organization's name.
	•	Your Email: Contact email address for communication.
	•	Agent Name: A unique and descriptive name for your agent.
	•	Agent Description: A brief description of your agent's functionality and purpose.
	•	Agent Code/Workflow JSON: Everything we need to run the agent for you.
	•	Handling Extra Data: Details on how the data field in your agent's response should be used by the frontend (if applicable). Include any specific frontend components or rendering instructions.
	•	Credit URL: A URL that users can visit to learn more about your agent or your work. This can be a website, a GitHub repository, or any relevant link.

Submit Your Agent
Handling File Uploads
When a user uploads files to your agent, they will be included in the input parameters as base64-encoded strings. This section expands on the file upload portion of the input parameters shown at the beginning of this guide.
View Example File Agent
Supported File Types
	•	Images: PNG, JPEG, GIF
	•	Documents: TXT, CSV, XLSX
File Size Limits
	•	Maximum file size: 5MB per file
	•	Maximum files per message: 5 files

File Upload Input Format
Here is an expanded example of the input parameters when files are included:
{
  "query": "Here are some files to process",
  "user_id": "user123",
  "request_id": "req456",
  "session_id": "sess789",
  "files": [
    {
      "name": "data.csv",
      "type": "text/csv",
      "base64": "Y29sdW1uMSxjb2x1bW4yDQp2YWx1ZTEsdmFsdWUy"
    },
    {
      "name": "image.png",
      "type": "image/png",
      "base64": "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8BQDwAEhQGAhKmMIQAAAABJRU5ErkJggg=="
    }
  ]
}

File Object Properties
	•	name: (String) The original filename
	•	type: (String) The MIME type of the file
	•	base64: (String) The file contents encoded in base64

Processing Files
When processing files in your agent:
	•	Always validate the file type and size on your end
	•	Decode the base64 string to get the file contents
	•	Consider implementing rate limiting for file uploads
	•	Handle errors gracefully and provide clear error messages

Handling Extra Data
If your agent includes additional data in the data field of the messages, you MUST provide a React component to display this data in the frontend. This component will be used to render any custom UI elements or visualizations specific to your agent.
Frontend Component Requirements
	•	Component Structure: Create a React component that accepts the following props:interface YourAgentProps {
	•	  data: any;  // The data field from your agent's response
	•	  handleSendMessage: (message: string, messageData?: string) => Promise<void>;
	•	}
	•	Styling: Use Tailwind CSS for styling to maintain consistency with the Live Agent Studio's design system.
	•	Interactive Elements: If your component includes buttons or other interactive elements, use the handleSendMessage callback to communicate with your agent.
	•	message vs messageData: message is by default what is used to display the user's message in the frontend and communicate with your agent. Use messageData to override just what is sent to your agent.
Example Implementation
For a complete example of a frontend component implementation, refer to the Voiceflow integration:View Example Component

Integration Notes
	•	Component Export: Export your component as the default export from a file named after your agent (e.g., YourAgentComponent.tsx).
	•	Error Handling: Include appropriate error states and loading states in your component.
	•	Type Safety: Define proper TypeScript interfaces for your data structures.
Building an Agent with Voiceflow
Voiceflow is integrated directly with the Live Agent Studio through their Dialog API. This means you can create conversational AI agents using Voiceflow's powerful visual builder and they'll automatically be compatible with our platform.
Getting Started
	•	Create your agent in Voiceflow using their visual builder
	•	(Optional) Reference the Dialog API documentation for advanced use cases and custom actions
	•	The Live Agent Studio will automatically handle the integration with Voiceflow's API
Integration
Our integration with Voiceflow is open source! For a detailed look, check out ourVoiceflow + Live Agent Studio integration. This shows:
	•	How the Dialog API is used to manage conversations within the Studio
	•	The custom frontend component for rendering Voiceflow's responses
	•	Handling interactive elements like buttons and choices

Building an Agent with n8n
If you choose to build your agent using n8n, follow these steps:
	1	Create a Webhook Node:
	•	Set the method to POST and secure it using an authorization token (we will replace this with our own when we deploy your agent).
	2	Process Input Parameters:
	•	Use a Set node to extract and assign query, user_id, request_id, and session_id.
	3	Store User Message:
	•	Use the Supabase node to insert the user's message into the messages table using the provided structures.
	4	Generate Agent Response:
	•	Implement your logic using AI models or other services to process the query.
	5	Store Agent Response:
	•	Insert the agent's response into the messages table. You can also insert more than one agent message, and the frontend will display the messages as they come into the database!
	6	Prepare the Response:
	•	Use a Set node to create the response object with { "success": true } or { "success": false }.
	7	Respond to Live Agent Studio:
	•	Use the Respond to Webhook node to send back the response.
Note: You can use the AI Agent node from n8n, and as long as you change the table to messages from the default n8n_chat_history, you don’t have to worry about creating the user or agent messages in the database!

Building an Agent with Custom Code
To build your agent using custom code:
	1	Set Up the API Endpoint:
	•	Create a POST endpoint using your preferred programming language and framework.
	2	Implement Authentication:
	•	Validate the Authorization header against an authorization token (we will replace this with our own when we deploy your agent).
	3	Handle Input Parameters:
	•	Parse the incoming JSON payload to extract query, user_id, request_id, and session_id.
	4	Store User Message:
	•	Insert the user's message into the messages table in the database.
	5	Process the Query:
	•	Use AI models or your custom logic to generate a response.
	6	Store Agent Response:
	•	Insert the agent's response into the messages table. You can also insert more than one agent message, and the frontend will display the messages as they come into the database!
	7	Prepare the Response:
	•	Create a JSON object containing { "success": true } or { "success": false }.
	8	Respond to Live Agent Studio:
	•	Send the JSON response back to Live Agent Studio.
Note: Ensure your endpoint is robust, handles exceptions, and returns appropriate HTTP status codes.

Testing Your Agent
Before submitting your agent to the Live Agent Studio, it's crucial to thoroughly test it to ensure it works as expected. The best way to do this is using our Agent 0 Playground, which lets you test your local agent using the actual Live Agent Studio interface:
Test in the Agent 0 Playground

This will save you time by validating that your agent is fully compatible with the Live Agent Studio before submission. The playground provides the same interface and functionality as the main platform, making it the perfect testing environment.
Local Testing:
	•	Use tools like Postman or cURL to simulate requests from Live Agent Studio.
	•	Verify that your agent handles input parameters correctly and produces the expected output.
	•	Ensure messages are correctly stored in the database.
	•	Test how your agent handles invalid inputs, missing parameters, or unauthorized access.
	•	Ensure that appropriate error messages and HTTP status codes are returned.
	•	Make sure your agent responds within the acceptable time frame (ideally under 60 seconds) to prevent timeouts.
	•	Test your agent under different load conditions to ensure scalability.

Best Practices
Security:
	•	Always validate input parameters to prevent injection attacks.
	•	Secure your webhook endpoint using an authorization token you define yourself (we will replace this with our own when we deploy your agent).
Performance:
	•	Optimize your agent to respond quickly to avoid timeouts.
Scalability:
	•	Design your agent to handle multiple concurrent requests efficiently.
Error Handling:
	•	Return meaningful error messages and appropriate HTTP status codes.
Logging:
	•	Implement logging for monitoring and debugging purposes.
Documentation:
	•	Keep your code well-documented to facilitate maintenance and updates.
Credit and Attribution
We value the contributions of developers and want to ensure proper credit is given. By providing a Credit URL, users can learn more about you or your organization.
Display:
The frontend will display a link or attribution note associated with your agent’s responses.
Purpose:
This enhances transparency and allows users to explore your work further.

Frequently Asked Questions
Will Live Agent Studio handle hosting and running my agent?
Yes, we offer to host and run your agent on our infrastructure. This ensures full integration with Live Agent Studio’s features and offloads the operational aspects from you. Additionally, you won’t have to pay for LLM usage. Please provide your agent’s codebase, and we’ll handle the rest.

How should I handle the data field in the messages?
The data field is optional and can include any additional information you want to pass to the frontend. Ensure it’s a valid JSON object and provide instructions on how it should be used.

What if my agent doesn’t need to store any extra data?
If you don’t have additional data to provide, you can omit the data field or include it as an empty object:
"data": {}

How do I secure my agent’s webhook endpoint?
Use an authorization token to authenticate requests. Validate the Authorization header in incoming requests against this token (we will replace this with our own when we deploy your agent).

Can I use a different database instead of Supabase?
Yes, you can use any PostgreSQL-compatible database. However, using Supabase is preferred due to its seamless integration with our platform.

How can I ensure my agent is added to the platform?
Provide all the required information listed in the Information to Provide section. Our team will handle the integration process.

What happens if my agent takes longer than 60 seconds to respond?
Live Agent Studio may time out and return an error to the user. To prevent this, ensure your agent responds promptly, ideally within 60 seconds.
Can I include additional endpoints or functionalities in my agent?
Yes, but the primary interaction with Live Agent Studio should be through the specified webhook endpoint and data formats. Additional functionalities should not interfere with this integration.

Who should I contact if I have questions or need assistance?
Please reach out to Cole Medin at <EMAIL> for any questions or assistance during development.
By following this guide, you can create an AI agent that integrates smoothly with the Live Agent Studio. Your agent will be able to process user queries, manage conversation history, and enhance the user experience on our platform.
Last Updated: 2024-12-23
