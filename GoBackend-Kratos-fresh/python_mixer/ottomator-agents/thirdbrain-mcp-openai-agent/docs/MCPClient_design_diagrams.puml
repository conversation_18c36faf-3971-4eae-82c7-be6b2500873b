@startuml
!include <C4/C4_Container>

Person(user, "User", "Interacts with the system")
Container_Boundary(app, "MCP Client Application") {
    Container(mcpClient, "MCPClient", "Python Class", "Manages MCP server connections and tool interactions")
    Container(mcpServer, "MCP Server", "External Service", "Provides tools and services")
    Container(configFile, "mcp_config.json", "Configuration File", "Stores server configurations")
}

Rel(user, mcpClient, "Sends commands and queries")
Rel(mcpClient, mcpServer, "Connects to and interacts with")
Rel(mcpClient, configFile, "Reads and writes configurations")
@enduml
