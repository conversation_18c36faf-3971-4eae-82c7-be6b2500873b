{"name": "YouTube Educator Plus", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": ""}]}}}, "id": "83c4ebd2-7f63-458c-b4f3-993e3a8df72a", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2040, 600]}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "224931a5-fb7a-4930-b045-a312d7f09238", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1200, 600]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "414a4a3e-b3e1-4322-abdb-c345e986f563", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1800, 600]}, {"parameters": {"httpMethod": "POST", "path": "invoke-youtube-educator-plus", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "a1468e31-a970-4158-a34b-b25f80279665", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1420, 600], "webhookId": "d9fec84b-86f0-4230-9fd4-c1cb392ff8b5", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Prep Input Fields').item.json.query }}", "hasOutputParser": true, "options": {"systemMessage": "=You are an AI agent with access to the following tools:\n\n\nYouTube Video Transcript Finder: Retrieves the transcript of a given YouTube video.\n\nWeb Search Tool: Finds additional resources related to the video's content.\n\nTasks (FOLLOW EXACT STEPS IN THE ORDER GIVEN):\n\n\n1. Extract the Transcript: Take a YouTube link provided by the user and extract its transcript using the YouTube Video Transcript Finder. Don't worry about guessing the video title.\n\n2. Find the main ideas that matter the most to the user. Ask yourself, what does the user want to learn from this video? What practical information should I focus on?\n\n3. Create a Fill-in-the-Blank Worksheet using the main ideas and the transcript. Make sure that the blanks are large enough to write the answer in. (Put the answers at the end of the notes).(Example: \"The five major workflows discussed in the video for different AI agent recipes are ______________, ______________, ______________, ______________, and ______________\" is much better than: \"The five major workflows discussed in the video for different AI agent recipes are ______________, routing, paralyzation, orchestrated workers, and evaluator optimizer.\" Because you want the user to learn as much as possible. )\n\n4. Create a quiz, don't include fill in the blank questions. Include multiple choice, short answer, and/or other types of questions. Make sure that there is enough space for the user to write in the answer fields. Put the answers to the quiz at the end.\n\n4. Create a query for and search for three supplemental resources related to the video's main ideas USING THE GIVEN TOOLS.\n\n5. USE THE RESOURCE FINDER TOOL TO FIND 3 REAL LINKS (DON'T INVENT ANY LINKS)\n\n5. Combine the fill in the blank notes, the quiz, and the resources that you found with the resources tool. (Don't include the main ideas or the transcript summary in the final output.) \n\n6. Output in markdown formatting. "}}, "id": "1ab098fb-8938-4239-bf11-461cd9bbc79f", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-480, 600]}, {"parameters": {"toolDescription": "Get youtube transcript using youtube link", "url": "https://api.supadata.ai/v1/youtube/transcript", "sendQuery": true, "parametersQuery": {"values": [{"name": "url", "valueProvider": "=fieldValue", "value": "={{ $('Get link').item.json.message.content }}"}, {"name": "text", "valueProvider": "fieldValue", "value": "true"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "x-api-key", "valueProvider": "fieldValue", "value": "Your Supadata API key"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-360, 1040], "id": "63007a12-855f-41bb-a9ed-c40224d40f47", "name": "Get transcript"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [-840, 1040], "id": "c6c66bf0-3f5a-4953-89c9-1fb433fddc4b", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Webhook').item.json.body.session_id }}", "tableName": "messages"}, "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [-640, 1040], "id": "d60219e0-74d3-4f50-86d5-6550cc53f4e7", "name": "Postgres Chat Memory", "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Output the youtube link and only the youtube link in this message:{{ $json.query }}. If there is no youtube link then return \"LinkNotFound\" "}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-940, 600], "id": "906c53a6-3670-4aed-9947-a6f2e0de8a6e", "name": "Get link", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"method": "POST", "url": "https://api.pdfendpoint.com/v1/convert", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Authorization", "value": "Bearer [Your PDFEndpoint API Key]"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "margin_top", "value": "1cm"}, {"name": "margin_bottom", "value": "1cm"}, {"name": "margin_right", "value": "1cm"}, {"name": "margin_left", "value": "1cm"}, {"name": "no_backgrounds", "value": "false"}, {"name": "html", "value": "={{ $json.html }}"}, {"name": "parse_liquid", "value": "true"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [760, 600], "id": "55fb3ce6-6a9c-475f-be69-dd1f94603b91", "name": "HTML to PDF"}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json['PDF URL'],\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "4c92958f-a365-4d42-bbcf-9c95eb2929ab", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1440, 600], "credentials": {"supabaseApi": {"id": "Eu6anmgplJHxqZu2", "name": "Studio Test"}}}, {"parameters": {"assignments": {"assignments": [{"id": "e57fa8e4-77ca-48b2-88da-872510e9bc89", "name": "PDF URL", "value": "=Here is a PDF version: {{ $json.data.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 600], "id": "216fd5f5-bcf0-4937-adb3-1e040fc9a38e", "name": "<PERSON>"}, {"parameters": {"mode": "markdownToHtml", "markdown": "={{ $json.output }}", "destinationKey": "html", "options": {"backslashEscapesHTMLTags": false, "completeHTMLDocument": true, "literalMidWordUnderscores": false}}, "type": "n8n-nodes-base.markdown", "typeVersion": 1, "position": [340, 600], "id": "a4715017-0520-44e0-a565-e57a7be32f4f", "name": "Markdown1"}, {"parameters": {"toolDescription": "Use this tool to search the web for additional resources.", "url": "https://api.search.brave.com/res/v1/web/search", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}, {"name": "Accept-Encoding", "valueProvider": "fieldValue", "value": "gzip"}, {"name": "X-Subscription-Token", "valueProvider": "fieldValue", "value": "Your Brave API Key"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-500, 1040], "id": "1021f80c-f7b9-439f-971c-4ca741756399", "name": "Web Searcher"}], "pinData": {}, "connections": {"Prep Input Fields": {"main": [[{"node": "Get link", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Markdown1", "type": "main", "index": 0}]]}, "Get transcript": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Get link": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "HTML to PDF": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Markdown1": {"main": [[{"node": "HTML to PDF", "type": "main", "index": 0}]]}, "Web Searcher": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "87f3bc74-5271-49b2-a034-413727e49908", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "dbL0pf3cURL9Mhcy", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}