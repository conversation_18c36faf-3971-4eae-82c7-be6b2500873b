{"name": "Base Agent", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "d8db86be-41fa-4c77-a5db-e2c693f809d5", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1860, 460]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"human\",\n\"content\": $json.query,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "107240de-b57c-4d5e-bd85-e65a7636bbf5", "name": "Add User Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [920, 460], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').first().json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json.output,\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "9ab25498-76c0-43c9-a477-fc8329cce13b", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1400, 460], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "a5644326-09b5-4927-a317-103cfbd91c75", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [700, 460]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "f0da2b47-0252-45bf-948b-3729acbaeccd", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1640, 460]}, {"parameters": {"content": "# Base Sample Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nThis is a sample n8n workflow that demonstrates the minimal required components to build an agent for the oTTomator Live Agent Studio. It serves as a template and reference implementation for creating new agents.\n\n## Core Components\n\n1. **Webhook Endpoint**\n   - Accepts POST requests with authentication\n   - Processes incoming queries with user and session information\n   - Provides secure communication via header authentication\n\n2. **Input Processing**\n   - Extracts key fields from incoming requests:\n     - query: The user's question or command\n     - user_id: Unique identifier for the user\n     - request_id: Request tracking ID\n     - session_id: Current session identifier\n\n3. **Database Integration**\n   - Uses Supabase for message storage\n   - Records both user messages and AI responses\n   - Maintains conversation history with metadata\n\n4. **Response Handling**\n   - Structured response format for consistency\n   - Includes success/failure status\n   - Returns formatted responses via webhook", "height": 763.4375, "width": 589.875, "color": 6}, "id": "5100d7c2-0c89-4922-a0ce-9e0df54714f9", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-220, 140]}, {"parameters": {"content": "## Add agent logic here\n\nThis can be anything that generates output to store as the \"AI message\" in the node to the right.\n\nSee the other example n8n workflow in GitHub to see how to use the \"Agent\" node in n8n instead of this setup.", "height": 235, "width": 267.5, "color": 6}, "id": "9c0e546d-57d9-476c-bba5-100731cbb950", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1080, 460]}, {"parameters": {"httpMethod": "POST", "path": "9a6c4630-b422-4d42-b894-81ecfe881ffe", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "85c600ae-5695-4bf5-b6dd-9e7426fb89df", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [460, 460], "webhookId": "9a6c4630-b422-4d42-b894-81ecfe881ffe", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.[your n8n url].com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer YOUR BEARER TOKEN", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.[your n8n url].com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "Supabase", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.[your n8n url].com/webhook-test/invoke-agent", "executionMode": "test"}}]}, "connections": {"Add User Message to DB": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "11f80a9b-342c-4619-b972-0d925afb09fa", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "y0OhDoLH2yJZVJpU", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}