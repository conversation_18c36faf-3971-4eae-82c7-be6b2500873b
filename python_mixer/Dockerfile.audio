# Audio Processing Dockerfile for HVAC CRM
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies for audio processing
RUN apt-get update && apt-get install -y \
    ffmpeg \
    libsndfile1 \
    libsndfile1-dev \
    libasound2-dev \
    portaudio19-dev \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements
COPY requirements.txt .

# Install Python dependencies for audio processing
RUN pip install --no-cache-dir \
    librosa \
    soundfile \
    pydub \
    scipy \
    numpy \
    aiohttp \
    aiofiles \
    celery \
    redis \
    flask \
    requests

# Install additional requirements
RUN pip install --no-cache-dir -r requirements.txt

# Copy audio processing modules
COPY document_processing/ ./document_processing/
COPY audio_worker.py .
COPY audio_api.py .

# Create necessary directories
RUN mkdir -p /app/audio_temp /app/audio_processed /app/logs

# Expose port for audio API
EXPOSE 8766

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8766/health || exit 1

# Default command (can be overridden)
CMD ["python", "audio_api.py"]
