# Multi-stage build for HVAC Python Mixer with Graphiti Integration
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    libpq-dev \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Copy requirements files
COPY requirements.txt .
COPY graphiti_integration/requirements_graphiti.txt ./graphiti_integration/

# Install Python dependencies
RUN pip install --upgrade pip
RUN pip install -r requirements.txt
RUN pip install -r graphiti_integration/requirements_graphiti.txt

# Development stage
FROM base as development

# Install development dependencies
RUN pip install \
    pytest \
    pytest-asyncio \
    black \
    isort \
    mypy \
    jupyter \
    ipython

# Copy source code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/config

# Set permissions
RUN chmod +x /app/start_gradio.py

# Expose ports
EXPOSE 8001 7860

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Default command
CMD ["python", "-m", "uvicorn", "graphiti_integration.api:app", "--host", "0.0.0.0", "--port", "8001", "--reload"]

# Production stage
FROM base as production

# Copy only necessary files
COPY graphiti_integration/ ./graphiti_integration/
COPY email_processing/ ./email_processing/
COPY database/ ./database/
COPY config.py .
COPY main.py .

# Create non-root user
RUN groupadd -r hvac && useradd -r -g hvac hvac
RUN chown -R hvac:hvac /app
USER hvac

# Expose ports
EXPOSE 8001 7860

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8001/health || exit 1

# Production command
CMD ["python", "-m", "uvicorn", "graphiti_integration.api:app", "--host", "0.0.0.0", "--port", "8001"]
