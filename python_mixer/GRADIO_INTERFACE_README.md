# 🏠 Enhanced HVAC Gradio Interface

Kompleksowy interfejs Gradio dla systemu HVAC Multi-Agent z zaawansowanymi funkcjami analizy emaili, generowania ofert i zarządzania danymi.

## ✨ Nowe Funkcje

### 📧 Zaawansowana Analiza Emaili
- **Multi-framework support**: LangGraph, CrewAI, OpenAI Swarm
- **Real-time analysis**: Ana<PERSON>za sentymentu, rozpoznawanie encji, klasyfikacja intencji
- **Email templates**: Gotowe szablony testowe (serwis, oferta, reklamacja)
- **Interactive metrics**: Wizualizacja wyników analizy z Plotly
- **Progress indicators**: Real-time status updates

### 📋 Generator Profesjonalnych Ofert
- **Smart calculations**: Automatyczne kalkulacje kosztów na podstawie parametrów
- **AI suggestions**: Inteligentne sugestie systemów HVAC
- **Multi-tab interface**: Podgląd, kalk<PERSON><PERSON><PERSON>, specyfika<PERSON><PERSON> techniczna, dokumenty
- **Cost breakdown**: Szczegółowa kalkulacja z pozycjami
- **Technical specs**: Automatyczne generowanie specyfikacji technicznej

### 🔧 Baza Sprzętu HVAC
- **Advanced search**: Wyszukiwanie z filtrami (producent, typ, model)
- **Equipment management**: Dodawanie nowego sprzętu
- **Real-time updates**: Dynamiczne aktualizacje listy

### 🌐 Krabulon Data Enrichment
- **Multi-manufacturer crawling**: Automatyczne wzbogacanie bazy danych
- **Progress tracking**: Monitoring procesu crawlingu
- **Statistics dashboard**: Szczegółowe statystyki wzbogacania

### ⚙️ Zarządzanie Systemem
- **Database management**: Inicjalizacja, backup, restore
- **Health monitoring**: Sprawdzanie stanu systemu
- **System logs**: Przeglądanie logów z różnymi poziomami

### 📊 Analityka i Raporty
- **Interactive charts**: Wykresy z Plotly
- **Multiple report types**: Analiza emaili, statystyki ofert, trendy
- **Data export**: Export do CSV/PDF

## 🚀 Instalacja i Uruchomienie

### Wymagania
```bash
pip install -r requirements.txt
```

### Podstawowe uruchomienie
```bash
python enhanced_gradio_launcher.py
```

### Zaawansowane opcje
```bash
# Uruchomienie na porcie 8080
python enhanced_gradio_launcher.py --port 8080

# Tryb debug z publicznym udostępnianiem
python enhanced_gradio_launcher.py --debug --share

# Nasłuchiwanie na wszystkich interfejsach
python enhanced_gradio_launcher.py --host 0.0.0.0

# Z uwierzytelnianiem
python enhanced_gradio_launcher.py --auth admin password123

# Z SSL/HTTPS
python enhanced_gradio_launcher.py --ssl-keyfile key.pem --ssl-certfile cert.pem
```

## 🎨 Interfejs Użytkownika

### Główne Zakładki

1. **📧 Analiza Emaili**
   - Wprowadzanie treści emaila
   - Wybór framework AI
   - Opcje analizy (sentiment, encje, intencje, priorytet)
   - Wyniki w formie JSON, insights, rekomendacji i wykresów

2. **📋 Generator Ofert**
   - Formularz danych klienta
   - Parametry projektu (powierzchnia, pomieszczenia, wymagania)
   - AI-powered suggestions
   - Podgląd oferty z kalkulacją kosztów

3. **🔧 Baza Sprzętu**
   - Wyszukiwanie z filtrami
   - Lista sprzętu w tabeli
   - Dodawanie nowego sprzętu

4. **🌐 Krabulon**
   - Wybór producentów do crawlingu
   - Opcje crawlingu (max strony, opóźnienie)
   - Monitoring postępu i statystyki

5. **⚙️ Zarządzanie Systemem**
   - Zarządzanie bazami danych
   - Sprawdzanie zdrowia systemu
   - Przeglądanie logów

6. **📊 Analityka**
   - Generowanie raportów
   - Interaktywne wykresy
   - Export danych

## 🔧 Konfiguracja

### CSS Styling
Interface używa custom CSS dla:
- Responsive design (max-width: 1400px)
- Gradient headers
- Status indicators (success/error)
- Metric cards z shadow effects

### Themes
- Domyślny theme: `gr.themes.Soft()`
- Możliwość customizacji kolorów i fontów

## 📱 Responsywność

Interface jest w pełni responsywny z:
- Adaptive layouts z `gr.Row()` i `gr.Column()`
- Proper scaling ratios
- Mobile-friendly components
- Progressive disclosure z accordions

## 🔍 Funkcje AI

### Email Analysis
- Sentiment analysis z confidence scores
- Named Entity Recognition (NER)
- Intent classification
- Priority assessment
- Framework comparison (LangGraph vs CrewAI vs Swarm)

### Quote Generation
- Smart equipment matching
- Cost calculations based on area/rooms
- Budget-aware recommendations
- Technical specifications generation

## 📊 Wizualizacje

### Plotly Charts
- Real-time metrics visualization
- Interactive bar charts dla analysis results
- Time series dla email analytics
- Customizable themes i colors

### Data Tables
- Sortable equipment lists
- Cost breakdown tables
- Report data with export options

## 🔐 Bezpieczeństwo

### Authentication
- Basic auth support
- SSL/HTTPS encryption
- Secure file handling

### Data Protection
- Input validation
- Error handling
- Secure database connections

## 🚀 Performance

### Optimizations
- Lazy loading dla heavy components
- Efficient data caching
- Async operations gdzie możliwe
- Memory management

### Monitoring
- Real-time system health checks
- Performance metrics
- Error tracking i logging

## 🔄 Integracja z Memory MCP

Interface automatycznie zapisuje kluczowe informacje do Memory MCP:
- Analysis results
- Generated quotes
- System status
- User interactions

## 📝 Logging

Comprehensive logging z loguru:
- Structured logs
- Multiple log levels
- File rotation
- Console i file output

## 🎯 Roadmap

### Planowane funkcje:
- [ ] Real-time collaboration
- [ ] Advanced AI models integration
- [ ] Mobile app companion
- [ ] API endpoints exposure
- [ ] Advanced analytics dashboard
- [ ] Multi-language support

## 🤝 Contributing

1. Fork repository
2. Create feature branch
3. Implement changes
4. Add tests
5. Submit pull request

## 📞 Support

Dla wsparcia technicznego:
- GitHub Issues
- Email: <EMAIL>
- Documentation: `/docs`

---

**Powered by Gradio 4.0+ | Enhanced with Plotly | Styled with Custom CSS**
