"""
Audio Processing API for HVAC CRM
Provides REST endpoints for audio transcription and processing.
"""

from flask import Flask, request, jsonify, send_file
import os
import tempfile
import logging
from pathlib import Path
import json
import time
from werkzeug.utils import secure_filename

from audio_worker import celery, transcribe_audio, process_audio_batch, health_check

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB max file size

# Allowed audio file extensions
ALLOWED_EXTENSIONS = {'.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac', '.mp4'}

def allowed_file(filename):
    """Check if file extension is allowed."""
    return Path(filename).suffix.lower() in ALLOWED_EXTENSIONS

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint."""
    try:
        # Check Celery worker
        inspect = celery.control.inspect()
        active_workers = inspect.active()
        
        worker_healthy = active_workers is not None and len(active_workers) > 0
        
        return jsonify({
            'status': 'healthy' if worker_healthy else 'unhealthy',
            'workers_active': len(active_workers) if active_workers else 0,
            'timestamp': time.time()
        })
    except Exception as e:
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/transcribe', methods=['POST'])
def transcribe():
    """Transcribe audio file endpoint."""
    try:
        # Check if file is present
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not supported'}), 400
        
        # Get options
        options = {}
        if 'options' in request.form:
            try:
                options = json.loads(request.form['options'])
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid options JSON'}), 400
        
        # Save file to temporary location
        filename = secure_filename(file.filename)
        temp_dir = Path('/app/audio_temp')
        temp_dir.mkdir(exist_ok=True)
        
        temp_path = temp_dir / f"{int(time.time())}_{filename}"
        file.save(str(temp_path))
        
        # Queue transcription task
        task = transcribe_audio.delay(str(temp_path), options)
        
        return jsonify({
            'task_id': task.id,
            'status': 'queued',
            'message': 'Transcription task queued successfully'
        })
        
    except Exception as e:
        logger.error(f"Transcription request failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/transcribe/sync', methods=['POST'])
def transcribe_sync():
    """Synchronous transcription endpoint (for small files)."""
    try:
        # Check if file is present
        if 'audio' not in request.files:
            return jsonify({'error': 'No audio file provided'}), 400
        
        file = request.files['audio']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'File type not supported'}), 400
        
        # Check file size (limit sync processing to 10MB)
        file.seek(0, 2)  # Seek to end
        file_size = file.tell()
        file.seek(0)  # Reset to beginning
        
        if file_size > 10 * 1024 * 1024:  # 10MB
            return jsonify({
                'error': 'File too large for synchronous processing. Use async endpoint.'
            }), 413
        
        # Get options
        options = {}
        if 'options' in request.form:
            try:
                options = json.loads(request.form['options'])
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid options JSON'}), 400
        
        # Save file to temporary location
        filename = secure_filename(file.filename)
        temp_dir = Path('/app/audio_temp')
        temp_dir.mkdir(exist_ok=True)
        
        temp_path = temp_dir / f"{int(time.time())}_{filename}"
        file.save(str(temp_path))
        
        try:
            # Execute transcription synchronously with timeout
            task = transcribe_audio.apply_async(args=[str(temp_path), options])
            result = task.get(timeout=300)  # 5 minute timeout
            
            return jsonify({
                'status': 'completed',
                'result': result
            })
            
        finally:
            # Clean up temp file
            if temp_path.exists():
                temp_path.unlink()
        
    except Exception as e:
        logger.error(f"Sync transcription failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/transcribe/batch', methods=['POST'])
def transcribe_batch():
    """Batch transcription endpoint."""
    try:
        files = request.files.getlist('audio')
        if not files:
            return jsonify({'error': 'No audio files provided'}), 400
        
        # Get options
        options = {}
        if 'options' in request.form:
            try:
                options = json.loads(request.form['options'])
            except json.JSONDecodeError:
                return jsonify({'error': 'Invalid options JSON'}), 400
        
        # Save files and prepare file paths
        temp_dir = Path('/app/audio_temp')
        temp_dir.mkdir(exist_ok=True)
        
        file_paths = []
        for file in files:
            if file.filename and allowed_file(file.filename):
                filename = secure_filename(file.filename)
                temp_path = temp_dir / f"{int(time.time())}_{filename}"
                file.save(str(temp_path))
                file_paths.append(str(temp_path))
        
        if not file_paths:
            return jsonify({'error': 'No valid audio files provided'}), 400
        
        # Queue batch processing task
        task = process_audio_batch.delay(file_paths, options)
        
        return jsonify({
            'task_id': task.id,
            'status': 'queued',
            'files_count': len(file_paths),
            'message': 'Batch transcription task queued successfully'
        })
        
    except Exception as e:
        logger.error(f"Batch transcription request failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/task/<task_id>', methods=['GET'])
def get_task_status(task_id):
    """Get task status and result."""
    try:
        task = celery.AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'task_id': task_id,
                'status': 'pending',
                'message': 'Task is waiting to be processed'
            }
        elif task.state == 'PROGRESS':
            response = {
                'task_id': task_id,
                'status': 'processing',
                'progress': task.info.get('progress', 0),
                'message': task.info.get('message', 'Processing...')
            }
        elif task.state == 'SUCCESS':
            response = {
                'task_id': task_id,
                'status': 'completed',
                'result': task.result
            }
        else:  # FAILURE
            response = {
                'task_id': task_id,
                'status': 'failed',
                'error': str(task.info)
            }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"Task status request failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/tasks', methods=['GET'])
def list_tasks():
    """List active tasks."""
    try:
        inspect = celery.control.inspect()
        
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        
        return jsonify({
            'active_tasks': active_tasks,
            'scheduled_tasks': scheduled_tasks
        })
        
    except Exception as e:
        logger.error(f"Task listing failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get processing statistics."""
    try:
        inspect = celery.control.inspect()
        
        stats = inspect.stats()
        active = inspect.active()
        
        total_active = sum(len(tasks) for tasks in active.values()) if active else 0
        
        return jsonify({
            'workers': len(stats) if stats else 0,
            'active_tasks': total_active,
            'stats': stats
        })
        
    except Exception as e:
        logger.error(f"Stats request failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/cleanup', methods=['POST'])
def cleanup():
    """Trigger cleanup of old files."""
    try:
        from audio_worker import cleanup_old_audio_files
        
        max_age_hours = request.json.get('max_age_hours', 24) if request.json else 24
        
        task = cleanup_old_audio_files.delay(max_age_hours)
        
        return jsonify({
            'task_id': task.id,
            'message': f'Cleanup task queued for files older than {max_age_hours} hours'
        })
        
    except Exception as e:
        logger.error(f"Cleanup request failed: {e}")
        return jsonify({'error': str(e)}), 500

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    # Create necessary directories
    Path('/app/audio_temp').mkdir(exist_ok=True)
    Path('/app/audio_processed').mkdir(exist_ok=True)
    Path('/app/logs').mkdir(exist_ok=True)
    
    # Start Flask app
    app.run(host='0.0.0.0', port=8766, debug=False)
