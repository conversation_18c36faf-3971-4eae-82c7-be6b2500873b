"""
Celery worker for audio processing tasks.
"""

import os
import asyncio
import logging
from celery import Celery
from pathlib import Path
import json

from document_processing.transcription_processor import TranscriptionProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery
redis_url = os.getenv('REDIS_URL', 'redis://localhost:6380')
celery = Celery('audio_worker', broker=redis_url, backend=redis_url)

# Configure Celery
celery.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Europe/Warsaw',
    enable_utc=True,
    task_routes={
        'audio_worker.transcribe_audio': {'queue': 'audio_transcription'},
        'audio_worker.process_audio_batch': {'queue': 'audio_batch'},
    }
)

# Initialize transcription processor
nemo_url = os.getenv('NEMO_SERVICE_URL', 'http://localhost:8765')
whisper_url = os.getenv('WHISPER_SERVICE_URL', 'http://localhost:9000')
transcription_processor = TranscriptionProcessor(nemo_url)

@celery.task(bind=True, max_retries=3)
def transcribe_audio(self, audio_file_path: str, options: dict = None):
    """Transcribe audio file task."""
    try:
        logger.info(f"Starting transcription for: {audio_file_path}")
        
        # Run async transcription in sync context
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                transcription_processor.transcribe_audio_file(audio_file_path)
            )
            
            # Convert result to dict for JSON serialization
            result_dict = {
                'text': result.text,
                'confidence': result.confidence,
                'language': result.language,
                'duration': result.duration,
                'segments': result.segments,
                'speaker_labels': result.speaker_labels,
                'processing_time': result.processing_time,
                'model_used': result.model_used,
                'audio_quality': result.audio_quality,
                'hvac_keywords': result.hvac_keywords or []
            }
            
            logger.info(f"Transcription completed for: {audio_file_path}")
            return result_dict
            
        finally:
            loop.close()
            
    except Exception as e:
        logger.error(f"Transcription failed for {audio_file_path}: {e}")
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        # Return error result
        return {
            'text': f'Błąd transkrypcji: {str(e)}',
            'confidence': 0.0,
            'language': 'pl',
            'duration': 0.0,
            'segments': [],
            'speaker_labels': None,
            'processing_time': 0.0,
            'model_used': 'error',
            'audio_quality': 'unknown',
            'hvac_keywords': [],
            'error': str(e)
        }

@celery.task(bind=True)
def process_audio_batch(self, audio_files: list, options: dict = None):
    """Process multiple audio files in batch."""
    results = []
    
    for audio_file in audio_files:
        try:
            result = transcribe_audio.delay(audio_file, options)
            results.append({
                'file': audio_file,
                'task_id': result.id,
                'status': 'queued'
            })
        except Exception as e:
            results.append({
                'file': audio_file,
                'task_id': None,
                'status': 'failed',
                'error': str(e)
            })
    
    return results

@celery.task
def cleanup_old_audio_files(max_age_hours: int = 24):
    """Clean up old audio files."""
    import time
    from pathlib import Path
    
    audio_temp_dir = Path('/app/audio_temp')
    audio_processed_dir = Path('/app/audio_processed')
    
    current_time = time.time()
    max_age_seconds = max_age_hours * 3600
    
    cleaned_files = []
    
    for directory in [audio_temp_dir, audio_processed_dir]:
        if directory.exists():
            for file_path in directory.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        try:
                            file_path.unlink()
                            cleaned_files.append(str(file_path))
                        except Exception as e:
                            logger.error(f"Failed to delete {file_path}: {e}")
    
    logger.info(f"Cleaned up {len(cleaned_files)} old audio files")
    return cleaned_files

@celery.task
def health_check():
    """Health check task."""
    try:
        # Test transcription service connection
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            is_healthy = loop.run_until_complete(
                transcription_processor.test_nemo_service()
            )
            return {
                'status': 'healthy' if is_healthy else 'unhealthy',
                'nemo_service': is_healthy,
                'timestamp': time.time()
            }
        finally:
            loop.close()
            
    except Exception as e:
        return {
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': time.time()
        }

# Periodic tasks
from celery.schedules import crontab

celery.conf.beat_schedule = {
    'cleanup-old-files': {
        'task': 'audio_worker.cleanup_old_audio_files',
        'schedule': crontab(hour=2, minute=0),  # Run daily at 2 AM
        'args': (24,)  # Clean files older than 24 hours
    },
    'health-check': {
        'task': 'audio_worker.health_check',
        'schedule': 300.0,  # Every 5 minutes
    },
}

if __name__ == '__main__':
    celery.start()
