#!/usr/bin/env python3
"""Demo script for HVAC Gradio Interface - Quick Start Guide."""

import sys
import os
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def print_banner():
    """Print welcome banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        🏠 HVAC Multi-Agent System - Gradio Interface        ║
    ║                                                              ║
    ║     Comprehensive HVAC CRM with AI-powered analysis         ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    
    ✨ Features:
    📧 Advanced Email Analysis (LangGraph, CrewAI, OpenAI Swarm)
    📋 Professional Quote Generator with AI suggestions
    🔧 Equipment Database Management
    🌐 Krabulon Data Enrichment
    ⚙️ System Management Dashboard
    📊 Analytics & Reporting
    
    🚀 Enhanced with:
    • Real-time visualizations (Plotly)
    • Responsive design
    • Progressive disclosure
    • Custom CSS styling
    • SSL/Auth support
    """
    print(banner)

def check_requirements():
    """Check if requirements are installed."""
    required = ['gradio', 'plotly', 'pandas', 'loguru']
    missing = []
    
    for package in required:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("📦 Install with: pip install -r requirements.txt")
        return False
    
    print("✅ All required packages are installed!")
    return True

def demo_quick_start():
    """Quick start demo."""
    print("\n🚀 Quick Start Options:")
    print("1. Basic launch:           python enhanced_gradio_launcher.py")
    print("2. Custom port:            python enhanced_gradio_launcher.py --port 8080")
    print("3. Debug mode:             python enhanced_gradio_launcher.py --debug")
    print("4. Public sharing:         python enhanced_gradio_launcher.py --share")
    print("5. With authentication:    python enhanced_gradio_launcher.py --auth admin password")
    print("6. All interfaces:         python enhanced_gradio_launcher.py --host 0.0.0.0")

def demo_features():
    """Demo key features."""
    print("\n🎯 Key Features Demo:")
    
    features = [
        {
            "name": "📧 Email Analysis",
            "description": "Analyze HVAC emails with AI frameworks",
            "demo": "Try the service request template for instant analysis"
        },
        {
            "name": "📋 Quote Generator", 
            "description": "Generate professional HVAC quotes",
            "demo": "Fill customer info and get instant cost breakdown"
        },
        {
            "name": "🔧 Equipment Database",
            "description": "Search and manage HVAC equipment",
            "demo": "Search for 'LG' or 'Daikin' to see equipment list"
        },
        {
            "name": "🌐 Krabulon Enrichment",
            "description": "Automatically enrich equipment database",
            "demo": "Select manufacturers and start crawling process"
        },
        {
            "name": "⚙️ System Management",
            "description": "Monitor system health and logs",
            "demo": "Check database status and view system logs"
        },
        {
            "name": "📊 Analytics",
            "description": "Generate reports and visualizations",
            "demo": "Create email analysis reports with charts"
        }
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"\n{i}. {feature['name']}")
        print(f"   {feature['description']}")
        print(f"   💡 Demo: {feature['demo']}")

def demo_advanced_usage():
    """Demo advanced usage scenarios."""
    print("\n🔧 Advanced Usage Scenarios:")
    
    scenarios = [
        {
            "title": "Production Deployment",
            "command": "python enhanced_gradio_launcher.py --host 0.0.0.0 --port 80 --auth admin secure_password --ssl-keyfile key.pem --ssl-certfile cert.pem"
        },
        {
            "title": "Development with Debug",
            "command": "python enhanced_gradio_launcher.py --debug --port 7860"
        },
        {
            "title": "Public Demo",
            "command": "python enhanced_gradio_launcher.py --share --port 7860"
        },
        {
            "title": "Team Collaboration",
            "command": "python enhanced_gradio_launcher.py --host 0.0.0.0 --port 8080 --auth team hvac2024"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['title']}:")
        print(f"   {scenario['command']}")

def demo_interface_tour():
    """Provide interface tour guide."""
    print("\n🗺️ Interface Tour Guide:")
    
    tour_steps = [
        "1. 🏠 Header: System status and refresh button",
        "2. 📧 Email Analysis Tab:",
        "   • Paste email content or use templates",
        "   • Select AI framework (LangGraph/CrewAI/Swarm)",
        "   • Configure analysis options",
        "   • View results in multiple tabs",
        "3. 📋 Quote Generator Tab:",
        "   • Fill customer information",
        "   • Set project requirements",
        "   • Get AI suggestions",
        "   • Generate professional quote with cost breakdown",
        "4. 🔧 Equipment Database Tab:",
        "   • Search equipment by manufacturer/type",
        "   • Add new equipment to database",
        "   • View detailed specifications",
        "5. 🌐 Krabulon Tab:",
        "   • Select manufacturers for data enrichment",
        "   • Configure crawling parameters",
        "   • Monitor progress and statistics",
        "6. ⚙️ System Management Tab:",
        "   • Initialize databases",
        "   • Check system health",
        "   • View system logs",
        "7. 📊 Analytics Tab:",
        "   • Generate various reports",
        "   • View interactive charts",
        "   • Export data to CSV/PDF"
    ]
    
    for step in tour_steps:
        print(f"   {step}")

def main():
    """Main demo function."""
    print_banner()
    
    if not check_requirements():
        return
    
    demo_quick_start()
    demo_features()
    demo_advanced_usage()
    demo_interface_tour()
    
    print("\n" + "="*60)
    print("🎉 Ready to launch! Choose your preferred startup command above.")
    print("📚 For detailed documentation, see: GRADIO_INTERFACE_README.md")
    print("🔧 For system configuration, check: config.py")
    print("📞 For support: GitHub <NAME_EMAIL>")
    print("="*60)
    
    # Ask user if they want to launch now
    try:
        choice = input("\n🚀 Launch interface now with default settings? (y/N): ").strip().lower()
        if choice in ['y', 'yes']:
            print("\n🌐 Launching HVAC Gradio Interface...")
            from enhanced_gradio_launcher import main as launch_main
            launch_main()
    except KeyboardInterrupt:
        print("\n👋 Demo completed. Launch manually when ready!")

if __name__ == "__main__":
    main()
