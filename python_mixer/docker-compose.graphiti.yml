version: '3.8'

services:
  # Neo4j for Graphiti Knowledge Graph
  neo4j-graphiti:
    image: neo4j:5.26-community
    container_name: hvac-neo4j-graphiti
    environment:
      - NEO4J_AUTH=neo4j/graphiti_password
      - NEO4J_PLUGINS=["apoc", "graph-data-science"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*,gds.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*,gds.*
      - NEO4J_dbms_memory_heap_initial_size=1G
      - NEO4J_dbms_memory_heap_max_size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_default_listen_address=0.0.0.0
      - NEO4J_dbms_connector_bolt_listen_address=0.0.0.0:7687
      - NEO4J_dbms_connector_http_listen_address=0.0.0.0:7474
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_graphiti_data:/data
      - neo4j_graphiti_logs:/logs
      - neo4j_graphiti_import:/var/lib/neo4j/import
      - neo4j_graphiti_plugins:/plugins
    networks:
      - hvac-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "graphiti_password", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis for caching and session management
  redis-cache:
    image: redis:7-alpine
    container_name: hvac-redis-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_cache_data:/data
    networks:
      - hvac-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Python Mixer with Graphiti Integration
  python-mixer-graphiti:
    build:
      context: .
      dockerfile: Dockerfile.graphiti
    container_name: hvac-python-mixer-graphiti
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - NEO4J_URI=bolt://neo4j-graphiti:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=graphiti_password
      - REDIS_URL=redis://redis-cache:6379
      - MODEL_CHOICE=gpt-4o-mini
      - POSTGRES_HOST=${POSTGRES_HOST:-**************}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      - POSTGRES_DB=${POSTGRES_DB:-hvac_crm}
      - POSTGRES_USER=${POSTGRES_USER:-koldbringer}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - MONGODB_HOST=${MONGODB_HOST:-**************}
      - MONGODB_PORT=${MONGODB_PORT:-27017}
      - MINIO_ENDPOINT=${MINIO_ENDPOINT:-**************:9000}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY:-koldbringer}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY:-Blaeritipol1}
    ports:
      - "8001:8001"  # Graphiti Agent API
      - "7860:7860"  # Gradio Interface
    volumes:
      - ./graphiti_integration:/app/graphiti_integration
      - ./email_processing:/app/email_processing
      - ./database:/app/database
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - hvac-network
    depends_on:
      neo4j-graphiti:
        condition: service_healthy
      redis-cache:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jupyter Lab for Development and Analysis
  jupyter-lab:
    image: jupyter/scipy-notebook:latest
    container_name: hvac-jupyter-lab
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=hvac_analysis_token
    ports:
      - "8888:8888"
    volumes:
      - ./notebooks:/home/<USER>/work
      - ./graphiti_integration:/home/<USER>/work/graphiti_integration
      - ./email_processing:/home/<USER>/work/email_processing
    networks:
      - hvac-network
    restart: unless-stopped

  # Grafana for Knowledge Graph Visualization
  grafana-graphiti:
    image: grafana/grafana:latest
    container_name: hvac-grafana-graphiti
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=graphiti_admin
      - GF_INSTALL_PLUGINS=neo4j-datasource
    ports:
      - "3001:3000"
    volumes:
      - grafana_graphiti_data:/var/lib/grafana
      - ./monitoring/grafana-graphiti:/etc/grafana/provisioning
    networks:
      - hvac-network
    restart: unless-stopped
    depends_on:
      - neo4j-graphiti

  # Prometheus for Metrics Collection
  prometheus-graphiti:
    image: prom/prometheus:latest
    container_name: hvac-prometheus-graphiti
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus-graphiti.yml:/etc/prometheus/prometheus.yml
      - prometheus_graphiti_data:/prometheus
    networks:
      - hvac-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'

volumes:
  neo4j_graphiti_data:
    driver: local
  neo4j_graphiti_logs:
    driver: local
  neo4j_graphiti_import:
    driver: local
  neo4j_graphiti_plugins:
    driver: local
  redis_cache_data:
    driver: local
  grafana_graphiti_data:
    driver: local
  prometheus_graphiti_data:
    driver: local

networks:
  hvac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
