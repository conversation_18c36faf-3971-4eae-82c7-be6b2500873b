version: '3.8'

services:
  # NVIDIA NeMo STT Service for Polish
  nemo-stt-polish:
    image: nvcr.io/nvidia/nemo:24.01.speech
    container_name: hvac-nemo-stt-polish
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      - NEMO_MODEL=stt_pl_fastconformer_ctc_large
      - NEMO_LANGUAGE=pl
      - NEMO_BATCH_SIZE=1
      - NEMO_MAX_DURATION=300
      - PYTHONPATH=/workspace/NeMo
    ports:
      - "8765:8765"
    volumes:
      - ./nemo_models:/workspace/models
      - ./nemo_cache:/root/.cache
      - ./audio_temp:/workspace/audio_temp
      - ./nemo_config:/workspace/config
    networks:
      - hvac-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8765/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    command: >
      bash -c "
        echo 'Starting NVIDIA NeMo STT Service for Polish...' &&
        python -c '
        import nemo.collections.asr as nemo_asr
        import torch
        from flask import Flask, request, jsonify
        import tempfile
        import os
        import json
        import logging
        
        # Configure logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        app = Flask(__name__)
        
        # Load Polish ASR model
        logger.info(\"Loading Polish FastConformer model...\")
        try:
            model = nemo_asr.models.EncDecCTCModel.from_pretrained(\"stt_pl_fastconformer_ctc_large\")
            model.eval()
            if torch.cuda.is_available():
                model = model.cuda()
            logger.info(\"Model loaded successfully\")
        except Exception as e:
            logger.error(f\"Failed to load model: {e}\")
            model = None
        
        @app.route(\"/health\", methods=[\"GET\"])
        def health():
            return jsonify({\"status\": \"healthy\", \"model_loaded\": model is not None})
        
        @app.route(\"/transcribe\", methods=[\"POST\"])
        def transcribe():
            if model is None:
                return jsonify({\"error\": \"Model not loaded\"}), 500
            
            try:
                # Get audio file
                audio_file = request.files.get(\"audio\")
                if not audio_file:
                    return jsonify({\"error\": \"No audio file provided\"}), 400
                
                # Get config
                config_str = request.form.get(\"config\", \"{}\")
                config = json.loads(config_str)
                
                # Save audio to temp file
                with tempfile.NamedTemporaryFile(suffix=\".wav\", delete=False) as temp_file:
                    audio_file.save(temp_file.name)
                    temp_path = temp_file.name
                
                try:
                    # Transcribe
                    transcription = model.transcribe([temp_path])
                    
                    # Process result
                    if transcription and len(transcription) > 0:
                        text = transcription[0]
                        
                        # Apply HVAC context if requested
                        if config.get(\"hvac_context\", False):
                            text = apply_hvac_corrections(text, config.get(\"keywords\", []))
                        
                        result = {
                            \"text\": text,
                            \"confidence\": 0.85,  # NeMo doesnt provide confidence by default
                            \"language\": \"pl\",
                            \"model\": \"stt_pl_fastconformer_ctc_large\",
                            \"segments\": [{
                                \"start\": 0.0,
                                \"end\": 10.0,  # Would need duration calculation
                                \"text\": text,
                                \"confidence\": 0.85
                            }]
                        }
                        
                        return jsonify(result)
                    else:
                        return jsonify({\"error\": \"Transcription failed\"}), 500
                        
                finally:
                    # Clean up temp file
                    if os.path.exists(temp_path):
                        os.unlink(temp_path)
                        
            except Exception as e:
                logger.error(f\"Transcription error: {e}\")
                return jsonify({\"error\": str(e)}), 500
        
        def apply_hvac_corrections(text, keywords):
            \"\"\"Apply HVAC-specific corrections to transcribed text.\"\"\"
            corrections = {
                \"klimatyzator\": [\"klimatyzator\", \"klimatyzacja\"],
                \"split\": [\"split\", \"splitu\"],
                \"serwis\": [\"serwis\", \"service\"],
                \"montaż\": [\"montaż\", \"montażu\"],
                \"naprawa\": [\"naprawa\", \"naprawy\"],
                \"awaria\": [\"awaria\", \"awarii\"],
                \"LG\": [\"LG\", \"el dżi\", \"eldżi\"],
                \"Daikin\": [\"Daikin\", \"dajkin\"],
                \"Mitsubishi\": [\"Mitsubishi\", \"mitsubishi\"]
            }
            
            corrected_text = text
            for correct_form, variants in corrections.items():
                for variant in variants:
                    if variant != correct_form:
                        corrected_text = corrected_text.replace(variant, correct_form)
            
            return corrected_text
        
        # Start Flask app
        logger.info(\"Starting Flask server on port 8765...\")
        app.run(host=\"0.0.0.0\", port=8765, debug=False)
        '"

  # Alternative: Whisper-based STT (fallback)
  whisper-stt:
    image: onerahmet/openai-whisper-asr-webservice:latest
    container_name: hvac-whisper-stt
    environment:
      - ASR_MODEL=large-v2
      - ASR_ENGINE=openai_whisper
    ports:
      - "9000:9000"
    volumes:
      - ./whisper_models:/root/.cache/whisper
    networks:
      - hvac-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Audio preprocessing service
  audio-preprocessor:
    build:
      context: .
      dockerfile: Dockerfile.audio
    container_name: hvac-audio-preprocessor
    environment:
      - PYTHONUNBUFFERED=1
    ports:
      - "8766:8766"
    volumes:
      - ./audio_temp:/app/audio_temp
      - ./audio_processed:/app/audio_processed
    networks:
      - hvac-network
    restart: unless-stopped
    depends_on:
      - nemo-stt-polish

  # Redis for audio processing queue
  redis-audio:
    image: redis:7-alpine
    container_name: hvac-redis-audio
    ports:
      - "6380:6379"
    volumes:
      - redis_audio_data:/data
    networks:
      - hvac-network
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru

  # Audio processing worker
  audio-worker:
    build:
      context: .
      dockerfile: Dockerfile.audio
    container_name: hvac-audio-worker
    environment:
      - PYTHONUNBUFFERED=1
      - REDIS_URL=redis://redis-audio:6379
      - NEMO_SERVICE_URL=http://nemo-stt-polish:8765
      - WHISPER_SERVICE_URL=http://whisper-stt:9000
    volumes:
      - ./audio_temp:/app/audio_temp
      - ./audio_processed:/app/audio_processed
      - ./logs:/app/logs
    networks:
      - hvac-network
    restart: unless-stopped
    depends_on:
      - redis-audio
      - nemo-stt-polish
    command: python -m celery worker -A audio_worker.celery --loglevel=info

volumes:
  redis_audio_data:
    driver: local

networks:
  hvac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
