# 🔧 HVAC Advanced Document Processing System

## Przegląd

Kompletny system przetwarzania dokumentów dla HVAC CRM z zaawansowanymi możliwościami AI:

- 📄 **Ekstrakcja dokumentów** z PDF, DOCX, Excel, obrazów (OCR)
- 🤖 **Analiza AI** z Gemma3-4b via LM Studio
- 🎤 **Transkrypcja audio** z NVIDIA NeMo (polski FastConformer)
- 🔧 **Analiza HVAC** z wykrywaniem sprzętu i specyfikacji
- 💰 **Ekstrakcja cen** i wymagań serwisowych

## 🏗️ Architektura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gradio UI     │    │  Attachment     │    │   Document      │
│   Port 7861     │◄──►│  Processor      │◄──►│   Extractor     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│  Gemma3-4b      │◄─────────────┘
                        │  LM Studio      │
                        │  *************  │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  NVIDIA NeMo    │
                        │  STT Service    │
                        │  Port 8765      │
                        └─────────────────┘
```

## 🚀 Szybki Start

### 1. Wymagania

**Systemowe:**
- Python 3.11+
- NVIDIA GPU (opcjonalne, dla NeMo)
- 8GB RAM minimum
- Tesseract OCR

**Usługi zewnętrzne:**
- LM Studio z Gemma3-4b na *************:1234
- NVIDIA NeMo (opcjonalne) na localhost:8765

### 2. Instalacja

```bash
# Zainstaluj zależności
pip install -r requirements.txt

# Zainstaluj Tesseract OCR (Ubuntu/Debian)
sudo apt-get install tesseract-ocr tesseract-ocr-pol

# Zainstaluj Tesseract OCR (macOS)
brew install tesseract tesseract-lang

# Zainstaluj Tesseract OCR (Windows)
# Pobierz z: https://github.com/UB-Mannheim/tesseract/wiki
```

### 3. Konfiguracja LM Studio

```bash
# 1. Uruchom LM Studio na *************
# 2. Załaduj model Gemma3-4b
# 3. Włącz serwer API na porcie 1234
# 4. Sprawdź dostępność:
curl http://*************:1234/v1/models
```

### 4. Uruchomienie NVIDIA NeMo (opcjonalne)

```bash
# Uruchom kontener NeMo
docker-compose -f docker-compose.nemo.yml up -d

# Sprawdź status
curl http://localhost:8765/health
```

### 5. Uruchomienie systemu

```bash
# Test systemu
python test_document_processing.py

# Uruchom interfejs Gradio
python document_processing/gradio_document_interface.py
```

**Dostęp:** http://localhost:7861

## 📊 Możliwości

### Obsługiwane formaty plików

**Dokumenty:**
- PDF (z tekstem i OCR)
- DOCX, DOC
- XLSX, XLS
- TXT, CSV

**Obrazy:**
- PNG, JPG, JPEG
- TIFF, BMP
- GIF (pierwszy frame)

**Audio:**
- MP3, WAV
- M4A, AAC
- OGG, FLAC

### Wykrywanie sprzętu HVAC

System automatycznie wykrywa:
- **Marki:** LG, Daikin, Mitsubishi, Panasonic, Fujitsu, Samsung
- **Modele:** S12ET, FTXS25K, MSZ-AP25VG, itp.
- **Parametry:** BTU, kW, COP, SEER, EER
- **Czynniki:** R32, R410A, R407C

### Analiza AI (Gemma3-4b)

- Podsumowanie dokumentów w języku polskim
- Wykrywanie wymagań serwisowych
- Klasyfikacja pilności (niski/średni/wysoki/krytyczny)
- Ekstrakcja informacji cenowych
- Identyfikacja specyfikacji technicznych

### Transkrypcja (NVIDIA NeMo)

- Model FastConformer dla języka polskiego
- Wysoka dokładność dla terminologii HVAC
- Automatyczna korekcja nazw marek i modeli
- Wykrywanie słów kluczowych HVAC

## 🔧 Użycie

### Interfejs Gradio

1. **Pojedynczy plik:**
   - Wybierz plik (dokument/audio)
   - Włącz/wyłącz analizę AI i transkrypcję
   - Kliknij "Przetwórz plik"

2. **Przetwarzanie wsadowe:**
   - Wybierz wiele plików (max 10)
   - Skonfiguruj opcje przetwarzania
   - Kliknij "Przetwórz pliki"

3. **Wyniki:**
   - Podsumowanie przetwarzania
   - Szczegółowa analiza HVAC
   - Wynik JSON dla integracji API

### API programowe

```python
from document_processing import AttachmentProcessor

# Inicjalizacja
processor = AttachmentProcessor(
    lm_studio_url="http://*************:1234",
    nemo_service_url="http://localhost:8765"
)

# Przetwarzanie pliku
with open("dokument.pdf", "rb") as f:
    file_data = f.read()

result = await processor.process_attachment(
    file_data, 
    "dokument.pdf",
    enable_ai_analysis=True,
    enable_transcription=False
)

print(f"Sukces: {result.success}")
print(f"Podsumowanie: {result.hvac_summary}")
print(f"Wykryty sprzęt: {len(result.equipment_detected)}")
```

### Przetwarzanie wsadowe

```python
# Wiele plików jednocześnie
attachments = [
    (file_data_1, "oferta.pdf"),
    (file_data_2, "raport.docx"),
    (file_data_3, "nagranie.mp3")
]

results = await processor.process_multiple_attachments(
    attachments, 
    max_concurrent=3
)

for result in results:
    print(f"{result.filename}: {result.service_priority}")
```

## 📈 Monitoring i statystyki

### Statystyki przetwarzania

```python
stats = processor.get_processing_stats()
print(f"Łącznie: {stats['total_processed']}")
print(f"Sukces: {stats['success_rate']:.1%}")
```

### Test połączeń

```python
service_status = await processor.test_services()
print(f"Gemma: {service_status['gemma']}")
print(f"NeMo: {service_status['nemo']}")
```

## 🔧 Konfiguracja zaawansowana

### Zmienne środowiskowe

```bash
# LM Studio
export LM_STUDIO_URL="http://*************:1234"

# NVIDIA NeMo
export NEMO_SERVICE_URL="http://localhost:8765"

# Katalogi tymczasowe
export TEMP_DIR="/tmp/hvac_processing"
```

### Dostosowanie wzorców HVAC

```python
# Dodanie nowych wzorców wykrywania
extractor = DocumentExtractor()
extractor.hvac_patterns['new_equipment'] = [
    r'\bNOWA_MARKA\s+[A-Z0-9-]+\b'
]
```

### Konfiguracja OCR

```python
# Dostosowanie OCR dla lepszej jakości
extractor.ocr_config = '--oem 3 --psm 6 -l pol+eng'
```

## 🚨 Rozwiązywanie problemów

### Częste problemy

1. **LM Studio niedostępny:**
   ```bash
   # Sprawdź połączenie
   curl http://*************:1234/v1/models
   
   # Sprawdź czy model jest załadowany
   # Uruchom ponownie LM Studio
   ```

2. **Błędy OCR:**
   ```bash
   # Zainstaluj Tesseract
   sudo apt-get install tesseract-ocr tesseract-ocr-pol
   
   # Sprawdź instalację
   tesseract --version
   ```

3. **Problemy z audio:**
   ```bash
   # Zainstaluj ffmpeg
   sudo apt-get install ffmpeg
   
   # Sprawdź NeMo service
   curl http://localhost:8765/health
   ```

### Logi i debugowanie

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Włącz szczegółowe logi
processor = AttachmentProcessor()
```

## 📚 Przykłady użycia

### Analiza oferty HVAC

```python
# Przetwarzanie oferty PDF
result = await processor.process_attachment(
    pdf_data, "oferta_lg_s12et.pdf"
)

print(f"Wykryty sprzęt: {result.equipment_detected}")
print(f"Szacowana wartość: {result.estimated_value} PLN")
print(f"Priorytet: {result.service_priority}")
```

### Transkrypcja zgłoszenia serwisowego

```python
# Przetwarzanie nagrania audio
result = await processor.process_attachment(
    audio_data, "zgloszenie_awarii.mp3",
    enable_transcription=True
)

print(f"Transkrypcja: {result.transcription.text}")
print(f"Słowa kluczowe HVAC: {result.transcription.hvac_keywords}")
```

### Analiza raportu serwisowego

```python
# Przetwarzanie raportu DOCX
result = await processor.process_attachment(
    docx_data, "raport_serwisowy.docx"
)

if result.gemma_analysis:
    print(f"Podsumowanie: {result.gemma_analysis.summary}")
    print(f"Wymagania serwisowe: {result.gemma_analysis.service_requirements}")
```

## 🎯 Integracja z HVAC CRM

System jest zaprojektowany do integracji z głównym systemem HVAC CRM:

```python
# Integracja z email processing
from email_processing import EmailParser
from graphiti_integration import HVACKnowledgeExtractor

# Przetwarzanie załączników email
for attachment in email.attachments:
    result = await processor.process_attachment(
        attachment.data, attachment.filename
    )
    
    # Dodaj do knowledge graph
    await knowledge_extractor.add_processing_result(result)
```

---

**HVAC Advanced Document Processing System** | 
Powered by Gemma3-4b, NVIDIA NeMo & Advanced OCR
