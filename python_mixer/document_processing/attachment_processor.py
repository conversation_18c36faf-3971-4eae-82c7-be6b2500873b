"""
Comprehensive Attachment Processor for HVAC CRM
Integrates document extraction, Gemma analysis, and transcription processing.
"""

import asyncio
import logging
import mimetypes
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

from .document_extractor import DocumentExtractor, ExtractedContent
from .gemma_analyzer import <PERSON>D<PERSON>ument<PERSON>nal<PERSON><PERSON>, GemmaAnalysisResult
from .transcription_processor import TranscriptionProcessor, TranscriptionResult

logger = logging.getLogger(__name__)

@dataclass
class ProcessedAttachment:
    """Complete processed attachment result."""
    filename: str
    file_type: str
    processing_method: str
    
    # Document content
    extracted_content: Optional[ExtractedContent] = None
    
    # AI analysis
    gemma_analysis: Optional[GemmaAnalysisResult] = None
    
    # Transcription (for audio files)
    transcription: Optional[TranscriptionResult] = None
    
    # Processing metadata
    processing_time: float = 0.0
    success: bool = False
    error_message: str = ""
    confidence_score: float = 0.0
    
    # HVAC-specific insights
    hvac_summary: str = ""
    equipment_detected: List[Dict[str, Any]] = None
    service_priority: str = "medium"
    estimated_value: Optional[float] = None

class AttachmentProcessor:
    """Comprehensive processor for email attachments."""
    
    def __init__(self, 
                 lm_studio_url: str = "http://*************:1234",
                 nemo_service_url: str = "http://localhost:8765",
                 temp_dir: str = "/tmp/hvac_attachments"):
        
        # Initialize processors
        self.document_extractor = DocumentExtractor(temp_dir)
        self.gemma_analyzer = GemmaDocumentAnalyzer(lm_studio_url)
        self.transcription_processor = TranscriptionProcessor(nemo_service_url, temp_dir)
        
        # Supported file types
        self.document_types = {'.pdf', '.docx', '.doc', '.xlsx', '.xls', '.txt', '.csv'}
        self.image_types = {'.png', '.jpg', '.jpeg', '.tiff', '.bmp', '.gif'}
        self.audio_types = {'.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'}
        
        # Processing statistics
        self.stats = {
            'total_processed': 0,
            'successful': 0,
            'failed': 0,
            'documents': 0,
            'images': 0,
            'audio': 0
        }
    
    async def process_attachment(self, 
                               file_data: bytes, 
                               filename: str,
                               enable_ai_analysis: bool = True,
                               enable_transcription: bool = True) -> ProcessedAttachment:
        """Process a single attachment comprehensively."""
        start_time = datetime.now()
        
        result = ProcessedAttachment(
            filename=filename,
            file_type=self._get_file_type(filename),
            processing_method="comprehensive",
            equipment_detected=[]
        )
        
        try:
            logger.info(f"Processing attachment: {filename}")
            
            # Determine processing strategy based on file type
            file_extension = Path(filename).suffix.lower()
            
            if file_extension in self.document_types or file_extension in self.image_types:
                await self._process_document_attachment(file_data, filename, result, enable_ai_analysis)
                self.stats['documents'] += 1
                
            elif file_extension in self.audio_types:
                await self._process_audio_attachment(file_data, filename, result, enable_transcription, enable_ai_analysis)
                self.stats['audio'] += 1
                
            else:
                # Try document processing as fallback
                await self._process_unknown_attachment(file_data, filename, result, enable_ai_analysis)
            
            # Calculate overall confidence and success
            result.confidence_score = self._calculate_overall_confidence(result)
            result.success = result.confidence_score > 0.3
            
            # Generate HVAC summary
            result.hvac_summary = self._generate_hvac_summary(result)
            
            # Determine service priority
            result.service_priority = self._determine_service_priority(result)
            
            # Estimate value if pricing information available
            result.estimated_value = self._estimate_value(result)
            
            if result.success:
                self.stats['successful'] += 1
            else:
                self.stats['failed'] += 1
                
        except Exception as e:
            logger.error(f"Error processing attachment {filename}: {e}")
            result.error_message = str(e)
            result.success = False
            self.stats['failed'] += 1
        
        finally:
            result.processing_time = (datetime.now() - start_time).total_seconds()
            self.stats['total_processed'] += 1
        
        return result
    
    async def process_multiple_attachments(self, 
                                         attachments: List[Tuple[bytes, str]],
                                         max_concurrent: int = 3) -> List[ProcessedAttachment]:
        """Process multiple attachments concurrently."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single(file_data: bytes, filename: str) -> ProcessedAttachment:
            async with semaphore:
                return await self.process_attachment(file_data, filename)
        
        tasks = [process_single(data, name) for data, name in attachments]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                error_result = ProcessedAttachment(
                    filename=attachments[i][1],
                    file_type="error",
                    processing_method="failed",
                    error_message=str(result),
                    success=False
                )
                processed_results.append(error_result)
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def _process_document_attachment(self, 
                                         file_data: bytes, 
                                         filename: str, 
                                         result: ProcessedAttachment,
                                         enable_ai_analysis: bool) -> None:
        """Process document/image attachment."""
        
        # Extract document content
        try:
            extracted_content = self.document_extractor.extract_from_attachment(file_data, filename)
            result.extracted_content = extracted_content
            
            # Collect equipment information
            if extracted_content.hvac_entities:
                result.equipment_detected.extend(extracted_content.hvac_entities)
            
        except Exception as e:
            logger.error(f"Document extraction failed for {filename}: {e}")
            result.error_message = f"Document extraction failed: {e}"
        
        # Perform AI analysis if enabled and content available
        if enable_ai_analysis and result.extracted_content and result.extracted_content.text.strip():
            try:
                gemma_analysis = await self.gemma_analyzer.analyze_document(result.extracted_content)
                result.gemma_analysis = gemma_analysis
                
                # Merge equipment information
                if gemma_analysis.hvac_entities:
                    result.equipment_detected.extend(gemma_analysis.hvac_entities)
                
            except Exception as e:
                logger.error(f"Gemma analysis failed for {filename}: {e}")
                if not result.error_message:
                    result.error_message = f"AI analysis failed: {e}"
    
    async def _process_audio_attachment(self, 
                                      file_data: bytes, 
                                      filename: str, 
                                      result: ProcessedAttachment,
                                      enable_transcription: bool,
                                      enable_ai_analysis: bool) -> None:
        """Process audio attachment."""
        
        # Perform transcription if enabled
        if enable_transcription:
            try:
                transcription = await self.transcription_processor.transcribe_audio_data(file_data, filename)
                result.transcription = transcription
                
                # Create extracted content from transcription for AI analysis
                if transcription.text and transcription.text.strip():
                    from .document_extractor import ExtractedContent, DocumentMetadata
                    
                    metadata = DocumentMetadata(
                        filename=filename,
                        file_type="Audio",
                        file_size=len(file_data),
                        processing_method="transcription",
                        confidence_score=transcription.confidence
                    )
                    
                    result.extracted_content = ExtractedContent(
                        text=transcription.text,
                        metadata=metadata
                    )
                
            except Exception as e:
                logger.error(f"Transcription failed for {filename}: {e}")
                result.error_message = f"Transcription failed: {e}"
        
        # Perform AI analysis on transcribed text if available
        if (enable_ai_analysis and 
            result.extracted_content and 
            result.extracted_content.text.strip()):
            try:
                gemma_analysis = await self.gemma_analyzer.analyze_text(
                    result.extracted_content.text, 
                    "service_analysis"  # Use service-focused analysis for audio
                )
                result.gemma_analysis = gemma_analysis
                
                if gemma_analysis.hvac_entities:
                    result.equipment_detected.extend(gemma_analysis.hvac_entities)
                
            except Exception as e:
                logger.error(f"AI analysis of transcription failed for {filename}: {e}")
    
    async def _process_unknown_attachment(self, 
                                        file_data: bytes, 
                                        filename: str, 
                                        result: ProcessedAttachment,
                                        enable_ai_analysis: bool) -> None:
        """Process unknown file type as fallback."""
        
        try:
            # Try document extraction with OCR fallback
            extracted_content = self.document_extractor.extract_from_attachment(file_data, filename)
            result.extracted_content = extracted_content
            
            if extracted_content.text.strip() and enable_ai_analysis:
                gemma_analysis = await self.gemma_analyzer.analyze_document(extracted_content)
                result.gemma_analysis = gemma_analysis
                
        except Exception as e:
            logger.error(f"Unknown file processing failed for {filename}: {e}")
            result.error_message = f"Unknown file type processing failed: {e}"
    
    def _calculate_overall_confidence(self, result: ProcessedAttachment) -> float:
        """Calculate overall confidence score."""
        scores = []
        
        # Document extraction confidence
        if result.extracted_content and result.extracted_content.metadata:
            scores.append(result.extracted_content.metadata.confidence_score)
        
        # AI analysis confidence
        if result.gemma_analysis:
            scores.append(result.gemma_analysis.confidence_score)
        
        # Transcription confidence
        if result.transcription:
            scores.append(result.transcription.confidence)
        
        if not scores:
            return 0.0
        
        # Weighted average with higher weight for AI analysis
        if len(scores) == 1:
            return scores[0]
        elif len(scores) == 2:
            return (scores[0] * 0.4 + scores[1] * 0.6)
        else:
            return (scores[0] * 0.3 + scores[1] * 0.5 + scores[2] * 0.2)
    
    def _generate_hvac_summary(self, result: ProcessedAttachment) -> str:
        """Generate HVAC-focused summary."""
        summary_parts = []
        
        # Add Gemma analysis summary
        if result.gemma_analysis and result.gemma_analysis.summary:
            summary_parts.append(result.gemma_analysis.summary)
        
        # Add equipment information
        if result.equipment_detected:
            equipment_list = [eq.get('value', '') for eq in result.equipment_detected[:3]]
            if equipment_list:
                summary_parts.append(f"Wykryty sprzęt: {', '.join(equipment_list)}")
        
        # Add transcription summary for audio
        if result.transcription and result.transcription.hvac_keywords:
            keywords = ', '.join(result.transcription.hvac_keywords[:5])
            summary_parts.append(f"Słowa kluczowe HVAC: {keywords}")
        
        # Add file type info
        summary_parts.append(f"Typ pliku: {result.file_type}")
        
        return " | ".join(summary_parts) if summary_parts else "Brak szczegółowych informacji"
    
    def _determine_service_priority(self, result: ProcessedAttachment) -> str:
        """Determine service priority based on content."""
        
        # Check Gemma analysis urgency
        if result.gemma_analysis:
            urgency = result.gemma_analysis.urgency_level.lower()
            if urgency in ['wysoki', 'krytyczny', 'pilny']:
                return "high"
            elif urgency in ['niski', 'planowany']:
                return "low"
        
        # Check for urgent keywords in transcription
        if result.transcription:
            urgent_keywords = ['awaria', 'pilne', 'natychmiast', 'nie działa', 'zepsuty']
            text_lower = result.transcription.text.lower()
            if any(keyword in text_lower for keyword in urgent_keywords):
                return "high"
        
        # Check document content for urgency
        if result.extracted_content:
            urgent_keywords = ['awaria', 'pilne', 'natychmiast', 'emergency']
            text_lower = result.extracted_content.text.lower()
            if any(keyword in text_lower for keyword in urgent_keywords):
                return "high"
        
        return "medium"  # Default priority
    
    def _estimate_value(self, result: ProcessedAttachment) -> Optional[float]:
        """Estimate monetary value from pricing information."""
        
        if not result.gemma_analysis or not result.gemma_analysis.pricing_information:
            return None
        
        total_value = 0.0
        for price_info in result.gemma_analysis.pricing_information:
            try:
                price_str = price_info.get('price', '0')
                # Extract numeric value
                import re
                numbers = re.findall(r'\d+[.,]?\d*', price_str)
                if numbers:
                    value = float(numbers[0].replace(',', '.'))
                    total_value += value
            except (ValueError, TypeError):
                continue
        
        return total_value if total_value > 0 else None
    
    def _get_file_type(self, filename: str) -> str:
        """Determine file type category."""
        extension = Path(filename).suffix.lower()
        
        if extension in self.document_types:
            return "Document"
        elif extension in self.image_types:
            return "Image"
        elif extension in self.audio_types:
            return "Audio"
        else:
            return "Unknown"
    
    async def test_services(self) -> Dict[str, bool]:
        """Test all integrated services."""
        results = {}
        
        # Test Gemma analyzer
        try:
            results['gemma'] = await self.gemma_analyzer.test_connection()
        except Exception as e:
            logger.error(f"Gemma test failed: {e}")
            results['gemma'] = False
        
        # Test transcription service
        try:
            results['nemo'] = await self.transcription_processor.test_nemo_service()
        except Exception as e:
            logger.error(f"NeMo test failed: {e}")
            results['nemo'] = False
        
        return results
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics."""
        return {
            **self.stats,
            'success_rate': self.stats['successful'] / max(1, self.stats['total_processed']),
            'document_ratio': self.stats['documents'] / max(1, self.stats['total_processed']),
            'audio_ratio': self.stats['audio'] / max(1, self.stats['total_processed'])
        }
