"""
Advanced Document Extractor for HVAC CRM
Supports PDF, DOCX, images, and various document formats with OCR.
"""

import os
import logging
import mimetypes
from pathlib import Path
from typing import Dict, List, Any, Optional, Union, Tuple
from dataclasses import dataclass
import hashlib
from datetime import datetime

# Document processing libraries
import PyPDF2
import pdfplumber
from docx import Document
import openpyxl
from PIL import Image
import pytesseract
import fitz  # PyMuPDF for advanced PDF processing

# OCR and image processing
import cv2
import numpy as np
from pdf2image import convert_from_path

# Text processing
import re
from langdetect import detect

logger = logging.getLogger(__name__)

@dataclass
class DocumentMetadata:
    """Document metadata structure."""
    filename: str
    file_type: str
    file_size: int
    page_count: Optional[int] = None
    language: Optional[str] = None
    creation_date: Optional[datetime] = None
    modification_date: Optional[datetime] = None
    author: Optional[str] = None
    title: Optional[str] = None
    subject: Optional[str] = None
    keywords: List[str] = None
    confidence_score: float = 0.0
    processing_method: str = ""
    ocr_applied: bool = False
    
@dataclass
class ExtractedContent:
    """Extracted document content structure."""
    text: str
    metadata: DocumentMetadata
    pages: List[Dict[str, Any]] = None
    tables: List[Dict[str, Any]] = None
    images: List[Dict[str, Any]] = None
    structured_data: Dict[str, Any] = None
    hvac_entities: List[Dict[str, Any]] = None

class DocumentExtractor:
    """Advanced document extractor with multiple processing methods."""
    
    def __init__(self, temp_dir: str = "/tmp/hvac_docs"):
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        
        # HVAC-specific patterns
        self.hvac_patterns = {
            'equipment_models': [
                r'\b(LG|DAIKIN|MITSUBISHI|PANASONIC|FUJITSU)\s+[A-Z0-9-]+\b',
                r'\bS\d{2}ET\b',  # LG models
                r'\bFTXS\d{2}[A-Z]+\b',  # Daikin models
                r'\bMSZ-[A-Z0-9]+\b'  # Mitsubishi models
            ],
            'technical_specs': [
                r'\d+\s*BTU',
                r'\d+\s*kW',
                r'\d+\s*m²',
                r'COP\s*[\d.,]+',
                r'SEER\s*[\d.,]+',
                r'EER\s*[\d.,]+',
                r'R-?\d{3}[A-Z]?'  # Refrigerant codes
            ],
            'prices': [
                r'\d+[.,]\d{2}\s*zł',
                r'\d+\s*PLN',
                r'\d+[.,]\d{2}\s*EUR'
            ],
            'dates': [
                r'\d{1,2}[./]\d{1,2}[./]\d{4}',
                r'\d{4}-\d{2}-\d{2}'
            ]
        }
        
        # Configure OCR for Polish
        self.ocr_config = '--oem 3 --psm 6 -l pol+eng'
        
    def extract_from_file(self, file_path: Union[str, Path]) -> ExtractedContent:
        """Extract content from any supported file type."""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Determine file type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        file_extension = file_path.suffix.lower()
        
        logger.info(f"Processing file: {file_path.name} (type: {mime_type})")
        
        try:
            if file_extension == '.pdf':
                return self._extract_pdf(file_path)
            elif file_extension in ['.docx', '.doc']:
                return self._extract_docx(file_path)
            elif file_extension in ['.xlsx', '.xls']:
                return self._extract_excel(file_path)
            elif file_extension in ['.png', '.jpg', '.jpeg', '.tiff', '.bmp']:
                return self._extract_image(file_path)
            elif file_extension in ['.txt', '.csv']:
                return self._extract_text(file_path)
            else:
                # Try OCR as fallback
                logger.warning(f"Unknown file type {file_extension}, attempting OCR")
                return self._extract_with_ocr(file_path)
                
        except Exception as e:
            logger.error(f"Error extracting from {file_path}: {e}")
            # Return basic metadata even if extraction fails
            return ExtractedContent(
                text="",
                metadata=self._get_basic_metadata(file_path, error=str(e))
            )
    
    def extract_from_attachment(self, attachment_data: bytes, filename: str) -> ExtractedContent:
        """Extract content from email attachment data."""
        # Save attachment to temp file
        temp_file = self.temp_dir / f"temp_{hashlib.md5(attachment_data).hexdigest()}_{filename}"
        
        try:
            with open(temp_file, 'wb') as f:
                f.write(attachment_data)
            
            result = self.extract_from_file(temp_file)
            return result
            
        finally:
            # Clean up temp file
            if temp_file.exists():
                temp_file.unlink()
    
    def _extract_pdf(self, file_path: Path) -> ExtractedContent:
        """Extract content from PDF using multiple methods."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "PDF"
        
        # Try pdfplumber first (better for tables and layout)
        try:
            with pdfplumber.open(file_path) as pdf:
                text_content = []
                tables = []
                pages_data = []
                
                metadata.page_count = len(pdf.pages)
                
                for page_num, page in enumerate(pdf.pages, 1):
                    page_text = page.extract_text() or ""
                    page_tables = page.extract_tables()
                    
                    pages_data.append({
                        'page_number': page_num,
                        'text': page_text,
                        'table_count': len(page_tables) if page_tables else 0
                    })
                    
                    text_content.append(f"[Strona {page_num}]\n{page_text}\n")
                    
                    # Process tables
                    if page_tables:
                        for table_idx, table in enumerate(page_tables):
                            tables.append({
                                'page': page_num,
                                'table_index': table_idx,
                                'data': table,
                                'rows': len(table),
                                'columns': len(table[0]) if table else 0
                            })
                
                full_text = "\n".join(text_content)
                metadata.processing_method = "pdfplumber"
                
        except Exception as e:
            logger.warning(f"pdfplumber failed, trying PyPDF2: {e}")
            # Fallback to PyPDF2
            full_text, pages_data = self._extract_pdf_pypdf2(file_path)
            tables = []
            metadata.processing_method = "PyPDF2"
        
        # If text extraction failed, try OCR
        if not full_text.strip():
            logger.info("Text extraction failed, attempting OCR")
            return self._extract_pdf_with_ocr(file_path)
        
        # Extract HVAC-specific entities
        hvac_entities = self._extract_hvac_entities(full_text)
        
        # Detect language
        try:
            metadata.language = detect(full_text[:1000])
        except:
            metadata.language = "unknown"
        
        metadata.confidence_score = self._calculate_confidence_score(full_text, hvac_entities)
        
        return ExtractedContent(
            text=full_text,
            metadata=metadata,
            pages=pages_data,
            tables=tables,
            hvac_entities=hvac_entities
        )
    
    def _extract_pdf_with_ocr(self, file_path: Path) -> ExtractedContent:
        """Extract PDF content using OCR."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "PDF (OCR)"
        metadata.ocr_applied = True
        metadata.processing_method = "OCR"
        
        try:
            # Convert PDF to images
            images = convert_from_path(file_path, dpi=300)
            text_content = []
            pages_data = []
            
            metadata.page_count = len(images)
            
            for page_num, image in enumerate(images, 1):
                # Preprocess image for better OCR
                processed_image = self._preprocess_image_for_ocr(image)
                
                # Extract text with OCR
                page_text = pytesseract.image_to_string(processed_image, config=self.ocr_config)
                
                pages_data.append({
                    'page_number': page_num,
                    'text': page_text,
                    'ocr_confidence': self._get_ocr_confidence(processed_image)
                })
                
                text_content.append(f"[Strona {page_num} - OCR]\n{page_text}\n")
            
            full_text = "\n".join(text_content)
            hvac_entities = self._extract_hvac_entities(full_text)
            
            # Calculate confidence based on OCR quality
            avg_confidence = sum(p.get('ocr_confidence', 0) for p in pages_data) / len(pages_data)
            metadata.confidence_score = avg_confidence / 100.0
            
            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                pages=pages_data,
                hvac_entities=hvac_entities
            )
            
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)
    
    def _extract_docx(self, file_path: Path) -> ExtractedContent:
        """Extract content from DOCX files."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "DOCX"
        metadata.processing_method = "python-docx"
        
        try:
            doc = Document(file_path)
            
            # Extract document properties
            if doc.core_properties.title:
                metadata.title = doc.core_properties.title
            if doc.core_properties.author:
                metadata.author = doc.core_properties.author
            if doc.core_properties.subject:
                metadata.subject = doc.core_properties.subject
            
            # Extract text content
            text_content = []
            tables = []
            
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # Extract tables
            for table_idx, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = [cell.text.strip() for cell in row.cells]
                    table_data.append(row_data)
                
                tables.append({
                    'table_index': table_idx,
                    'data': table_data,
                    'rows': len(table_data),
                    'columns': len(table_data[0]) if table_data else 0
                })
            
            full_text = "\n".join(text_content)
            hvac_entities = self._extract_hvac_entities(full_text)
            
            metadata.confidence_score = self._calculate_confidence_score(full_text, hvac_entities)
            
            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                tables=tables,
                hvac_entities=hvac_entities
            )
            
        except Exception as e:
            logger.error(f"DOCX extraction failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)
    
    def _extract_excel(self, file_path: Path) -> ExtractedContent:
        """Extract content from Excel files."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "Excel"
        metadata.processing_method = "openpyxl"
        
        try:
            workbook = openpyxl.load_workbook(file_path, data_only=True)
            
            text_content = []
            tables = []
            structured_data = {}
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                sheet_data = []
                
                # Extract all cell values
                for row in sheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):
                        row_data = [str(cell) if cell is not None else "" for cell in row]
                        sheet_data.append(row_data)
                        text_content.append(" | ".join(row_data))
                
                if sheet_data:
                    tables.append({
                        'sheet_name': sheet_name,
                        'data': sheet_data,
                        'rows': len(sheet_data),
                        'columns': len(sheet_data[0]) if sheet_data else 0
                    })
                    
                    structured_data[sheet_name] = sheet_data
            
            full_text = "\n".join(text_content)
            hvac_entities = self._extract_hvac_entities(full_text)
            
            metadata.confidence_score = self._calculate_confidence_score(full_text, hvac_entities)
            
            return ExtractedContent(
                text=full_text,
                metadata=metadata,
                tables=tables,
                structured_data=structured_data,
                hvac_entities=hvac_entities
            )
            
        except Exception as e:
            logger.error(f"Excel extraction failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)
    
    def _extract_image(self, file_path: Path) -> ExtractedContent:
        """Extract text from images using OCR."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "Image"
        metadata.processing_method = "OCR"
        metadata.ocr_applied = True
        
        try:
            # Load and preprocess image
            image = Image.open(file_path)
            processed_image = self._preprocess_image_for_ocr(image)
            
            # Extract text with OCR
            text = pytesseract.image_to_string(processed_image, config=self.ocr_config)
            
            # Get OCR confidence
            ocr_confidence = self._get_ocr_confidence(processed_image)
            metadata.confidence_score = ocr_confidence / 100.0
            
            hvac_entities = self._extract_hvac_entities(text)
            
            return ExtractedContent(
                text=text,
                metadata=metadata,
                hvac_entities=hvac_entities
            )
            
        except Exception as e:
            logger.error(f"Image OCR failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)
    
    def _extract_text(self, file_path: Path) -> ExtractedContent:
        """Extract content from plain text files."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "Text"
        metadata.processing_method = "direct"
        
        try:
            # Try different encodings
            encodings = ['utf-8', 'cp1250', 'iso-8859-2', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        text = f.read()
                    break
                except UnicodeDecodeError:
                    continue
            else:
                raise UnicodeDecodeError("Could not decode file with any encoding")
            
            hvac_entities = self._extract_hvac_entities(text)
            metadata.confidence_score = 1.0  # Direct text extraction is always confident
            
            return ExtractedContent(
                text=text,
                metadata=metadata,
                hvac_entities=hvac_entities
            )
            
        except Exception as e:
            logger.error(f"Text extraction failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)

    def _extract_hvac_entities(self, text: str) -> List[Dict[str, Any]]:
        """Extract HVAC-specific entities from text."""
        entities = []

        for category, patterns in self.hvac_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, text, re.IGNORECASE)
                for match in matches:
                    entities.append({
                        'category': category,
                        'value': match.group(),
                        'start': match.start(),
                        'end': match.end(),
                        'confidence': 0.9
                    })

        return entities

    def _calculate_confidence_score(self, text: str, hvac_entities: List[Dict]) -> float:
        """Calculate confidence score based on content quality."""
        if not text.strip():
            return 0.0

        score = 0.5  # Base score

        # Boost for HVAC content
        if hvac_entities:
            score += min(0.3, len(hvac_entities) * 0.05)

        # Boost for Polish content
        polish_words = ['klimatyzacja', 'serwis', 'montaż', 'naprawa', 'awaria']
        polish_count = sum(1 for word in polish_words if word in text.lower())
        score += min(0.2, polish_count * 0.04)

        return min(1.0, score)

    def _preprocess_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """Preprocess image for better OCR results."""
        # Convert PIL to OpenCV
        img_array = np.array(image)
        if len(img_array.shape) == 3:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

        # Convert to grayscale
        gray = cv2.cvtColor(img_array, cv2.COLOR_BGR2GRAY)

        # Apply denoising
        denoised = cv2.fastNlMeansDenoising(gray)

        # Apply adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            denoised, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )

        # Convert back to PIL
        return Image.fromarray(thresh)

    def _get_ocr_confidence(self, image: Image.Image) -> float:
        """Get OCR confidence score."""
        try:
            data = pytesseract.image_to_data(image, config=self.ocr_config, output_type=pytesseract.Output.DICT)
            confidences = [int(conf) for conf in data['conf'] if int(conf) > 0]
            return sum(confidences) / len(confidences) if confidences else 0
        except:
            return 50.0  # Default confidence

    def _get_basic_metadata(self, file_path: Path, error: str = None) -> DocumentMetadata:
        """Get basic file metadata."""
        stat = file_path.stat()

        return DocumentMetadata(
            filename=file_path.name,
            file_type=file_path.suffix.upper().lstrip('.'),
            file_size=stat.st_size,
            creation_date=datetime.fromtimestamp(stat.st_ctime),
            modification_date=datetime.fromtimestamp(stat.st_mtime),
            confidence_score=0.0 if error else 1.0,
            processing_method="basic_metadata"
        )

    def _extract_pdf_pypdf2(self, file_path: Path) -> Tuple[str, List[Dict]]:
        """Fallback PDF extraction using PyPDF2."""
        text_content = []
        pages_data = []

        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)

            for page_num, page in enumerate(reader.pages, 1):
                page_text = page.extract_text()
                pages_data.append({
                    'page_number': page_num,
                    'text': page_text
                })
                text_content.append(f"[Strona {page_num}]\n{page_text}\n")

        return "\n".join(text_content), pages_data

    def _extract_with_ocr(self, file_path: Path) -> ExtractedContent:
        """Generic OCR extraction for unknown file types."""
        metadata = self._get_basic_metadata(file_path)
        metadata.file_type = "Unknown (OCR)"
        metadata.processing_method = "OCR_fallback"
        metadata.ocr_applied = True

        try:
            # Try to open as image
            image = Image.open(file_path)
            processed_image = self._preprocess_image_for_ocr(image)
            text = pytesseract.image_to_string(processed_image, config=self.ocr_config)

            hvac_entities = self._extract_hvac_entities(text)
            metadata.confidence_score = self._calculate_confidence_score(text, hvac_entities)

            return ExtractedContent(
                text=text,
                metadata=metadata,
                hvac_entities=hvac_entities
            )

        except Exception as e:
            logger.error(f"OCR fallback failed: {e}")
            metadata.confidence_score = 0.0
            return ExtractedContent(text="", metadata=metadata)
