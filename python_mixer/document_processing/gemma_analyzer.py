"""
Gemma3-4b Document Analyzer for HVAC CRM
Integrates with LM Studio for advanced document analysis.
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from .document_extractor import ExtractedContent

logger = logging.getLogger(__name__)

@dataclass
class GemmaAnalysisResult:
    """Result from Gemma3-4b analysis."""
    summary: str
    hvac_entities: List[Dict[str, Any]]
    technical_specifications: Dict[str, Any]
    pricing_information: List[Dict[str, Any]]
    service_requirements: List[str]
    urgency_level: str
    confidence_score: float
    processing_time: float
    model_used: str
    raw_response: str

class GemmaDocumentAnalyzer:
    """Document analyzer using Gemma3-4b via LM Studio."""
    
    def __init__(self, lm_studio_url: str = "http://*************:1234"):
        self.lm_studio_url = lm_studio_url.rstrip('/')
        self.api_endpoint = f"{self.lm_studio_url}/v1/chat/completions"
        
        # HVAC-specific analysis prompts
        self.analysis_prompts = {
            'document_analysis': """
Jesteś ekspertem HVAC analizującym dokumenty techniczne. Przeanalizuj poniższy tekst i wyodrębnij:

1. PODSUMOWANIE (2-3 zdania)
2. SPRZĘT HVAC (modele, marki, specyfikacje)
3. DANE TECHNICZNE (moc, wydajność, parametry)
4. INFORMACJE CENOWE (ceny, koszty, budżet)
5. WYMAGANIA SERWISOWE (instalacja, konserwacja, naprawa)
6. POZIOM PILNOŚCI (niski/średni/wysoki/krytyczny)

Odpowiedz w formacie JSON:
{
    "summary": "krótkie podsumowanie",
    "hvac_entities": [{"type": "typ", "value": "wartość", "confidence": 0.9}],
    "technical_specs": {"parametr": "wartość"},
    "pricing": [{"item": "pozycja", "price": "cena", "currency": "PLN"}],
    "service_requirements": ["wymaganie1", "wymaganie2"],
    "urgency": "poziom",
    "confidence": 0.85
}

TEKST DO ANALIZY:
""",
            
            'equipment_focus': """
Jako specjalista HVAC, zidentyfikuj i przeanalizuj wszystkie urządzenia klimatyzacyjne w tekście:

- Marki: LG, Daikin, Mitsubishi, Panasonic, Fujitsu, Samsung
- Typy: split, multi-split, VRF, VRV, pompy ciepła, wentylacja
- Modele: konkretne oznaczenia (np. LG S12ET, Daikin Sensira)
- Parametry: moc, BTU, kW, COP, SEER, EER
- Stan: nowy, używany, do naprawy, do wymiany

Zwróć szczegółowe informacje w JSON.

TEKST:
""",
            
            'service_analysis': """
Przeanalizuj dokument pod kątem potrzeb serwisowych HVAC:

1. Typ usługi (montaż, naprawa, konserwacja, przegląd)
2. Pilność (natychmiastowa, w ciągu dnia, w tygodniu, planowana)
3. Wymagane części zamienne
4. Szacowany czas realizacji
5. Poziom skomplikowania (prosty, średni, złożony)
6. Wymagane kwalifikacje technika

Odpowiedz w JSON z polskimi opisami.

TEKST:
"""
        }
    
    async def analyze_document(self, extracted_content: ExtractedContent) -> GemmaAnalysisResult:
        """Analyze extracted document content with Gemma3-4b."""
        start_time = datetime.now()
        
        try:
            # Prepare text for analysis
            text_to_analyze = self._prepare_text_for_analysis(extracted_content)
            
            if not text_to_analyze.strip():
                return self._create_empty_result("No text content to analyze")
            
            # Perform comprehensive analysis
            analysis_result = await self._perform_comprehensive_analysis(text_to_analyze)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            analysis_result.processing_time = processing_time
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Document analysis failed: {e}")
            return self._create_empty_result(f"Analysis failed: {str(e)}")
    
    async def analyze_text(self, text: str, analysis_type: str = "document_analysis") -> GemmaAnalysisResult:
        """Analyze raw text with specified analysis type."""
        start_time = datetime.now()
        
        try:
            if analysis_type not in self.analysis_prompts:
                analysis_type = "document_analysis"
            
            prompt = self.analysis_prompts[analysis_type] + text
            
            # Call LM Studio API
            response = await self._call_lm_studio(prompt)
            
            # Parse response
            analysis_result = self._parse_gemma_response(response)
            analysis_result.processing_time = (datetime.now() - start_time).total_seconds()
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"Text analysis failed: {e}")
            return self._create_empty_result(f"Analysis failed: {str(e)}")
    
    async def _perform_comprehensive_analysis(self, text: str) -> GemmaAnalysisResult:
        """Perform comprehensive analysis using multiple prompts."""
        
        # Primary document analysis
        primary_analysis = await self.analyze_text(text, "document_analysis")
        
        # If document contains equipment info, do focused equipment analysis
        if any('equipment' in entity.get('type', '').lower() 
               for entity in primary_analysis.hvac_entities):
            equipment_analysis = await self.analyze_text(text, "equipment_focus")
            # Merge equipment entities
            primary_analysis.hvac_entities.extend(equipment_analysis.hvac_entities)
        
        # If document mentions service/repair, do service analysis
        service_keywords = ['serwis', 'naprawa', 'montaż', 'konserwacja', 'awaria']
        if any(keyword in text.lower() for keyword in service_keywords):
            service_analysis = await self.analyze_text(text, "service_analysis")
            # Merge service requirements
            primary_analysis.service_requirements.extend(service_analysis.service_requirements)
        
        return primary_analysis
    
    async def _call_lm_studio(self, prompt: str) -> str:
        """Call LM Studio API with the prompt."""
        payload = {
            "model": "gemma-2-2b-it",  # Adjust model name as needed
            "messages": [
                {
                    "role": "system",
                    "content": "Jesteś ekspertem HVAC z 20-letnim doświadczeniem w analizie dokumentów technicznych. Odpowiadaj precyzyjnie w języku polskim."
                },
                {
                    "role": "user", 
                    "content": prompt
                }
            ],
            "temperature": 0.3,
            "max_tokens": 2048,
            "stream": False
        }
        
        timeout = aiohttp.ClientTimeout(total=60)  # 60 second timeout
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(self.api_endpoint, json=payload) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result['choices'][0]['message']['content']
                    else:
                        error_text = await response.text()
                        raise Exception(f"LM Studio API error {response.status}: {error_text}")
                        
            except aiohttp.ClientError as e:
                raise Exception(f"Connection to LM Studio failed: {e}")
    
    def _parse_gemma_response(self, response: str) -> GemmaAnalysisResult:
        """Parse Gemma3-4b response into structured result."""
        try:
            # Try to extract JSON from response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                parsed_data = json.loads(json_str)
                
                return GemmaAnalysisResult(
                    summary=parsed_data.get('summary', ''),
                    hvac_entities=parsed_data.get('hvac_entities', []),
                    technical_specifications=parsed_data.get('technical_specs', {}),
                    pricing_information=parsed_data.get('pricing', []),
                    service_requirements=parsed_data.get('service_requirements', []),
                    urgency_level=parsed_data.get('urgency', 'średni'),
                    confidence_score=parsed_data.get('confidence', 0.5),
                    processing_time=0.0,
                    model_used="gemma-3-4b",
                    raw_response=response
                )
            else:
                # Fallback: treat entire response as summary
                return GemmaAnalysisResult(
                    summary=response[:500],
                    hvac_entities=[],
                    technical_specifications={},
                    pricing_information=[],
                    service_requirements=[],
                    urgency_level='średni',
                    confidence_score=0.3,
                    processing_time=0.0,
                    model_used="gemma-3-4b",
                    raw_response=response
                )
                
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            # Extract key information using regex as fallback
            return self._extract_fallback_analysis(response)
    
    def _extract_fallback_analysis(self, response: str) -> GemmaAnalysisResult:
        """Extract analysis using regex patterns as fallback."""
        import re
        
        # Extract equipment mentions
        equipment_patterns = [
            r'\b(LG|DAIKIN|MITSUBISHI|PANASONIC|FUJITSU)\s+[A-Z0-9-]+\b',
            r'\bS\d{2}ET\b',
            r'\bFTXS\d{2}[A-Z]+\b'
        ]
        
        hvac_entities = []
        for pattern in equipment_patterns:
            matches = re.finditer(pattern, response, re.IGNORECASE)
            for match in matches:
                hvac_entities.append({
                    'type': 'equipment',
                    'value': match.group(),
                    'confidence': 0.7
                })
        
        # Extract prices
        price_patterns = [r'\d+[.,]\d{2}\s*zł', r'\d+\s*PLN']
        pricing_info = []
        for pattern in price_patterns:
            matches = re.finditer(pattern, response)
            for match in matches:
                pricing_info.append({
                    'item': 'unknown',
                    'price': match.group(),
                    'currency': 'PLN'
                })
        
        # Determine urgency
        urgency = 'średni'
        if any(word in response.lower() for word in ['pilne', 'natychmiast', 'awaria']):
            urgency = 'wysoki'
        elif any(word in response.lower() for word in ['planowane', 'przyszłość']):
            urgency = 'niski'
        
        return GemmaAnalysisResult(
            summary=response[:300] + "..." if len(response) > 300 else response,
            hvac_entities=hvac_entities,
            technical_specifications={},
            pricing_information=pricing_info,
            service_requirements=[],
            urgency_level=urgency,
            confidence_score=0.4,
            processing_time=0.0,
            model_used="gemma-3-4b-fallback",
            raw_response=response
        )
    
    def _prepare_text_for_analysis(self, extracted_content: ExtractedContent) -> str:
        """Prepare extracted content for analysis."""
        text_parts = []
        
        # Add main text
        if extracted_content.text:
            text_parts.append(extracted_content.text)
        
        # Add metadata context
        if extracted_content.metadata:
            metadata_text = f"Plik: {extracted_content.metadata.filename}"
            if extracted_content.metadata.page_count:
                metadata_text += f", Strony: {extracted_content.metadata.page_count}"
            text_parts.append(metadata_text)
        
        # Add table data if available
        if extracted_content.tables:
            for table in extracted_content.tables[:3]:  # Limit to first 3 tables
                table_text = "TABELA:\n"
                if 'data' in table:
                    for row in table['data'][:10]:  # Limit rows
                        table_text += " | ".join(str(cell) for cell in row) + "\n"
                text_parts.append(table_text)
        
        # Combine and limit length
        full_text = "\n\n".join(text_parts)
        
        # Limit to ~4000 characters for Gemma processing
        if len(full_text) > 4000:
            full_text = full_text[:4000] + "\n[TEKST SKRÓCONY]"
        
        return full_text
    
    def _create_empty_result(self, error_message: str) -> GemmaAnalysisResult:
        """Create empty result with error message."""
        return GemmaAnalysisResult(
            summary=f"Błąd analizy: {error_message}",
            hvac_entities=[],
            technical_specifications={},
            pricing_information=[],
            service_requirements=[],
            urgency_level='nieznany',
            confidence_score=0.0,
            processing_time=0.0,
            model_used="gemma-3-4b",
            raw_response=""
        )
    
    async def test_connection(self) -> bool:
        """Test connection to LM Studio."""
        try:
            test_prompt = "Odpowiedz krótko: Czy jesteś gotowy do analizy dokumentów HVAC?"
            response = await self._call_lm_studio(test_prompt)
            return len(response.strip()) > 0
        except Exception as e:
            logger.error(f"LM Studio connection test failed: {e}")
            return False
