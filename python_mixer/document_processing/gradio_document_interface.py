"""
Gradio Interface for Advanced Document Processing
Integrates document extraction, Gemma analysis, and transcription.
"""

import gradio as gr
import asyncio
import json
import pandas as pd
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import plotly.express as px
import plotly.graph_objects as go
from pathlib import Path
import tempfile
import os

from .attachment_processor import AttachmentProcessor, ProcessedAttachment
from .document_extractor import DocumentExtractor
from .gemma_analyzer import GemmaD<PERSON>ument<PERSON><PERSON>y<PERSON>
from .transcription_processor import TranscriptionProcessor

# Global processor instance
processor = None

async def initialize_processor():
    """Initialize the attachment processor."""
    global processor
    
    try:
        lm_studio_url = os.getenv('LM_STUDIO_URL', 'http://*************:1234')
        nemo_url = os.getenv('NEMO_SERVICE_URL', 'http://localhost:8765')
        
        processor = AttachmentProcessor(lm_studio_url, nemo_url)
        
        # Test services
        service_status = await processor.test_services()
        
        status_msg = "✅ Procesor dokumentów zainicjalizowany!\n"
        status_msg += f"Gemma3-4b (LM Studio): {'✅' if service_status.get('gemma', False) else '❌'}\n"
        status_msg += f"NVIDIA NeMo STT: {'✅' if service_status.get('nemo', False) else '❌'}"
        
        return status_msg
        
    except Exception as e:
        return f"❌ Błąd inicjalizacji: {str(e)}"

async def process_single_file(file_path: str, 
                            enable_ai: bool = True, 
                            enable_transcription: bool = True) -> Tuple[str, pd.DataFrame, str, str]:
    """Process a single uploaded file."""
    if not processor:
        return "❌ Procesor nie został zainicjalizowany", pd.DataFrame(), "", ""
    
    try:
        # Read file data
        with open(file_path, 'rb') as f:
            file_data = f.read()
        
        filename = Path(file_path).name
        
        # Process the file
        result = await processor.process_attachment(
            file_data, filename, enable_ai, enable_transcription
        )
        
        # Create summary
        summary = create_processing_summary(result)
        
        # Create details DataFrame
        details_df = create_details_dataframe(result)
        
        # Create HVAC analysis
        hvac_analysis = create_hvac_analysis(result)
        
        # Create JSON output
        json_output = create_json_output(result)
        
        return summary, details_df, hvac_analysis, json_output
        
    except Exception as e:
        error_msg = f"❌ Błąd przetwarzania pliku: {str(e)}"
        return error_msg, pd.DataFrame(), "", ""

async def process_multiple_files(files: List[str],
                               enable_ai: bool = True,
                               enable_transcription: bool = True) -> Tuple[str, pd.DataFrame, str]:
    """Process multiple files."""
    if not processor:
        return "❌ Procesor nie został zainicjalizowany", pd.DataFrame(), ""
    
    try:
        # Prepare file data
        attachments = []
        for file_path in files:
            with open(file_path, 'rb') as f:
                file_data = f.read()
            filename = Path(file_path).name
            attachments.append((file_data, filename))
        
        # Process files
        results = await processor.process_multiple_attachments(
            attachments, max_concurrent=2
        )
        
        # Create batch summary
        batch_summary = create_batch_summary(results)
        
        # Create batch DataFrame
        batch_df = create_batch_dataframe(results)
        
        # Create batch analysis
        batch_analysis = create_batch_analysis(results)
        
        return batch_summary, batch_df, batch_analysis
        
    except Exception as e:
        error_msg = f"❌ Błąd przetwarzania plików: {str(e)}"
        return error_msg, pd.DataFrame(), ""

def create_processing_summary(result: ProcessedAttachment) -> str:
    """Create processing summary text."""
    summary_parts = [
        f"📄 **Plik:** {result.filename}",
        f"📁 **Typ:** {result.file_type}",
        f"⚡ **Metoda:** {result.processing_method}",
        f"✅ **Sukces:** {'Tak' if result.success else 'Nie'}",
        f"🎯 **Pewność:** {result.confidence_score:.2%}",
        f"⏱️ **Czas:** {result.processing_time:.2f}s"
    ]
    
    if result.service_priority:
        priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(result.service_priority, "⚪")
        summary_parts.append(f"{priority_emoji} **Priorytet:** {result.service_priority}")
    
    if result.estimated_value:
        summary_parts.append(f"💰 **Szacowana wartość:** {result.estimated_value:.2f} PLN")
    
    if result.hvac_summary:
        summary_parts.append(f"\n📋 **Podsumowanie HVAC:**\n{result.hvac_summary}")
    
    if result.error_message:
        summary_parts.append(f"\n❌ **Błąd:** {result.error_message}")
    
    return "\n".join(summary_parts)

def create_details_dataframe(result: ProcessedAttachment) -> pd.DataFrame:
    """Create detailed information DataFrame."""
    details = []
    
    # Document extraction details
    if result.extracted_content:
        content = result.extracted_content
        details.append({
            "Kategoria": "Ekstrakcja dokumentu",
            "Parametr": "Tekst wyodrębniony",
            "Wartość": f"{len(content.text)} znaków" if content.text else "Brak",
            "Pewność": f"{content.metadata.confidence_score:.2%}" if content.metadata else "N/A"
        })
        
        if content.metadata and content.metadata.page_count:
            details.append({
                "Kategoria": "Ekstrakcja dokumentu",
                "Parametr": "Liczba stron",
                "Wartość": str(content.metadata.page_count),
                "Pewność": "100%"
            })
        
        if content.tables:
            details.append({
                "Kategoria": "Ekstrakcja dokumentu",
                "Parametr": "Tabele",
                "Wartość": f"{len(content.tables)} tabel",
                "Pewność": "100%"
            })
    
    # AI analysis details
    if result.gemma_analysis:
        analysis = result.gemma_analysis
        details.append({
            "Kategoria": "Analiza AI (Gemma3-4b)",
            "Parametr": "Podsumowanie",
            "Wartość": analysis.summary[:100] + "..." if len(analysis.summary) > 100 else analysis.summary,
            "Pewność": f"{analysis.confidence_score:.2%}"
        })
        
        if analysis.hvac_entities:
            details.append({
                "Kategoria": "Analiza AI (Gemma3-4b)",
                "Parametr": "Jednostki HVAC",
                "Wartość": f"{len(analysis.hvac_entities)} wykrytych",
                "Pewność": "Średnia"
            })
        
        if analysis.pricing_information:
            details.append({
                "Kategoria": "Analiza AI (Gemma3-4b)",
                "Parametr": "Informacje cenowe",
                "Wartość": f"{len(analysis.pricing_information)} pozycji",
                "Pewność": "Średnia"
            })
    
    # Transcription details
    if result.transcription:
        transcription = result.transcription
        details.append({
            "Kategoria": "Transkrypcja (NeMo)",
            "Parametr": "Tekst transkrypcji",
            "Wartość": f"{len(transcription.text)} znaków",
            "Pewność": f"{transcription.confidence:.2%}"
        })
        
        details.append({
            "Kategoria": "Transkrypcja (NeMo)",
            "Parametr": "Czas nagrania",
            "Wartość": f"{transcription.duration:.1f}s",
            "Pewność": "100%"
        })
        
        if transcription.hvac_keywords:
            details.append({
                "Kategoria": "Transkrypcja (NeMo)",
                "Parametr": "Słowa kluczowe HVAC",
                "Wartość": f"{len(transcription.hvac_keywords)} słów",
                "Pewność": "Wysoka"
            })
    
    return pd.DataFrame(details) if details else pd.DataFrame()

def create_hvac_analysis(result: ProcessedAttachment) -> str:
    """Create HVAC-specific analysis."""
    analysis_parts = []
    
    # Equipment detected
    if result.equipment_detected:
        analysis_parts.append("🔧 **Wykryty sprzęt HVAC:**")
        for i, equipment in enumerate(result.equipment_detected[:5], 1):
            eq_type = equipment.get('type', 'unknown')
            eq_value = equipment.get('value', 'N/A')
            eq_conf = equipment.get('confidence', 0.0)
            analysis_parts.append(f"{i}. {eq_value} (typ: {eq_type}, pewność: {eq_conf:.1%})")
    
    # Service requirements
    if result.gemma_analysis and result.gemma_analysis.service_requirements:
        analysis_parts.append("\n🛠️ **Wymagania serwisowe:**")
        for i, req in enumerate(result.gemma_analysis.service_requirements[:3], 1):
            analysis_parts.append(f"{i}. {req}")
    
    # Technical specifications
    if result.gemma_analysis and result.gemma_analysis.technical_specifications:
        analysis_parts.append("\n⚙️ **Specyfikacje techniczne:**")
        for key, value in list(result.gemma_analysis.technical_specifications.items())[:5]:
            analysis_parts.append(f"• {key}: {value}")
    
    # Pricing information
    if result.gemma_analysis and result.gemma_analysis.pricing_information:
        analysis_parts.append("\n💰 **Informacje cenowe:**")
        for i, price in enumerate(result.gemma_analysis.pricing_information[:3], 1):
            item = price.get('item', 'Pozycja')
            price_val = price.get('price', 'N/A')
            currency = price.get('currency', 'PLN')
            analysis_parts.append(f"{i}. {item}: {price_val} {currency}")
    
    return "\n".join(analysis_parts) if analysis_parts else "Brak szczegółowej analizy HVAC"

def create_json_output(result: ProcessedAttachment) -> str:
    """Create JSON output for API integration."""
    output_data = {
        "filename": result.filename,
        "file_type": result.file_type,
        "success": result.success,
        "confidence_score": result.confidence_score,
        "processing_time": result.processing_time,
        "service_priority": result.service_priority,
        "estimated_value": result.estimated_value,
        "hvac_summary": result.hvac_summary,
        "equipment_detected": result.equipment_detected,
        "error_message": result.error_message
    }
    
    # Add extracted text if available
    if result.extracted_content:
        output_data["extracted_text"] = result.extracted_content.text[:1000]  # First 1000 chars
    
    # Add AI analysis if available
    if result.gemma_analysis:
        output_data["ai_analysis"] = {
            "summary": result.gemma_analysis.summary,
            "urgency_level": result.gemma_analysis.urgency_level,
            "hvac_entities_count": len(result.gemma_analysis.hvac_entities),
            "pricing_items_count": len(result.gemma_analysis.pricing_information)
        }
    
    # Add transcription if available
    if result.transcription:
        output_data["transcription"] = {
            "text": result.transcription.text[:500],  # First 500 chars
            "confidence": result.transcription.confidence,
            "duration": result.transcription.duration,
            "hvac_keywords": result.transcription.hvac_keywords
        }
    
    return json.dumps(output_data, indent=2, ensure_ascii=False)

def create_batch_summary(results: List[ProcessedAttachment]) -> str:
    """Create batch processing summary."""
    total_files = len(results)
    successful = sum(1 for r in results if r.success)
    failed = total_files - successful
    
    avg_confidence = sum(r.confidence_score for r in results) / total_files if total_files > 0 else 0
    total_time = sum(r.processing_time for r in results)
    
    # Count file types
    file_types = {}
    for result in results:
        file_types[result.file_type] = file_types.get(result.file_type, 0) + 1
    
    summary_parts = [
        f"📊 **Podsumowanie przetwarzania wsadowego**",
        f"📁 **Łącznie plików:** {total_files}",
        f"✅ **Pomyślne:** {successful}",
        f"❌ **Nieudane:** {failed}",
        f"🎯 **Średnia pewność:** {avg_confidence:.2%}",
        f"⏱️ **Łączny czas:** {total_time:.2f}s",
        "",
        "📋 **Typy plików:**"
    ]
    
    for file_type, count in file_types.items():
        summary_parts.append(f"• {file_type}: {count}")
    
    return "\n".join(summary_parts)

def create_batch_dataframe(results: List[ProcessedAttachment]) -> pd.DataFrame:
    """Create batch results DataFrame."""
    data = []
    
    for result in results:
        data.append({
            "Plik": result.filename,
            "Typ": result.file_type,
            "Sukces": "✅" if result.success else "❌",
            "Pewność": f"{result.confidence_score:.1%}",
            "Priorytet": result.service_priority,
            "Czas [s]": f"{result.processing_time:.2f}",
            "Wartość [PLN]": f"{result.estimated_value:.2f}" if result.estimated_value else "N/A",
            "Błąd": result.error_message[:50] + "..." if result.error_message and len(result.error_message) > 50 else result.error_message or ""
        })
    
    return pd.DataFrame(data)

def create_batch_analysis(results: List[ProcessedAttachment]) -> str:
    """Create batch analysis summary."""
    # Collect all equipment
    all_equipment = []
    for result in results:
        if result.equipment_detected:
            all_equipment.extend(result.equipment_detected)
    
    # Count equipment types
    equipment_counts = {}
    for eq in all_equipment:
        eq_type = eq.get('type', 'unknown')
        equipment_counts[eq_type] = equipment_counts.get(eq_type, 0) + 1
    
    # Priority distribution
    priority_counts = {}
    for result in results:
        priority = result.service_priority
        priority_counts[priority] = priority_counts.get(priority, 0) + 1
    
    analysis_parts = [
        "🔧 **Analiza sprzętu HVAC:**",
        f"Łącznie wykrytych jednostek: {len(all_equipment)}"
    ]
    
    for eq_type, count in equipment_counts.items():
        analysis_parts.append(f"• {eq_type}: {count}")
    
    analysis_parts.extend([
        "",
        "🚨 **Rozkład priorytetów:**"
    ])
    
    for priority, count in priority_counts.items():
        emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(priority, "⚪")
        analysis_parts.append(f"{emoji} {priority}: {count}")
    
    return "\n".join(analysis_parts)

def create_gradio_interface():
    """Create the main Gradio interface."""

    with gr.Blocks(
        title="HVAC Document Processing System",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .tab-nav {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        .processing-status {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        """
    ) as interface:

        gr.Markdown("""
        # 🔧 HVAC Advanced Document Processing System

        **Kompletny system przetwarzania dokumentów z AI**

        Możliwości:
        - 📄 **Ekstrakcja dokumentów** (PDF, DOCX, Excel, obrazy) z OCR
        - 🤖 **Analiza AI** z Gemma3-4b via LM Studio (*************:1234)
        - 🎤 **Transkrypcja audio** z NVIDIA NeMo (polski FastConformer)
        - 🔧 **Analiza HVAC** z wykrywaniem sprzętu i specyfikacji
        - 💰 **Ekstrakcja cen** i wymagań serwisowych
        """)

        # Initialization section
        with gr.Row():
            init_btn = gr.Button("🚀 Inicjalizuj System", variant="primary", size="lg")
            init_status = gr.Textbox(
                label="Status systemu",
                interactive=False,
                lines=3,
                elem_classes=["processing-status"]
            )

        init_btn.click(
            fn=lambda: asyncio.run(initialize_processor()),
            outputs=init_status
        )

        # Main processing tabs
        with gr.Tabs():

            # Single file processing
            with gr.Tab("📄 Pojedynczy plik"):
                gr.Markdown("### Przetwarzanie pojedynczego dokumentu lub pliku audio")

                with gr.Row():
                    with gr.Column(scale=1):
                        file_input = gr.File(
                            label="Wybierz plik",
                            file_types=[
                                ".pdf", ".docx", ".doc", ".xlsx", ".xls", ".txt", ".csv",
                                ".png", ".jpg", ".jpeg", ".tiff", ".bmp",
                                ".mp3", ".wav", ".m4a", ".aac", ".ogg", ".flac"
                            ]
                        )

                        with gr.Row():
                            enable_ai_single = gr.Checkbox(
                                label="Analiza AI (Gemma3-4b)",
                                value=True
                            )
                            enable_transcription_single = gr.Checkbox(
                                label="Transkrypcja audio (NeMo)",
                                value=True
                            )

                        process_single_btn = gr.Button(
                            "🔄 Przetwórz plik",
                            variant="primary"
                        )

                    with gr.Column(scale=2):
                        single_summary = gr.Markdown(label="Podsumowanie")
                        single_details = gr.Dataframe(
                            label="Szczegóły przetwarzania",
                            headers=["Kategoria", "Parametr", "Wartość", "Pewność"]
                        )

                with gr.Row():
                    with gr.Column():
                        hvac_analysis_single = gr.Markdown(label="Analiza HVAC")
                    with gr.Column():
                        json_output_single = gr.Code(
                            label="Wynik JSON (API)",
                            language="json"
                        )

                process_single_btn.click(
                    fn=lambda f, ai, trans: asyncio.run(
                        process_single_file(f.name if f else "", ai, trans)
                    ) if f else ("❌ Nie wybrano pliku", pd.DataFrame(), "", ""),
                    inputs=[file_input, enable_ai_single, enable_transcription_single],
                    outputs=[single_summary, single_details, hvac_analysis_single, json_output_single]
                )

            # Batch processing
            with gr.Tab("📁 Przetwarzanie wsadowe"):
                gr.Markdown("### Przetwarzanie wielu plików jednocześnie")

                with gr.Row():
                    with gr.Column(scale=1):
                        files_input = gr.File(
                            label="Wybierz pliki (max 10)",
                            file_count="multiple",
                            file_types=[
                                ".pdf", ".docx", ".doc", ".xlsx", ".xls", ".txt", ".csv",
                                ".png", ".jpg", ".jpeg", ".tiff", ".bmp",
                                ".mp3", ".wav", ".m4a", ".aac", ".ogg", ".flac"
                            ]
                        )

                        with gr.Row():
                            enable_ai_batch = gr.Checkbox(
                                label="Analiza AI (Gemma3-4b)",
                                value=True
                            )
                            enable_transcription_batch = gr.Checkbox(
                                label="Transkrypcja audio (NeMo)",
                                value=True
                            )

                        process_batch_btn = gr.Button(
                            "🔄 Przetwórz pliki",
                            variant="primary"
                        )

                    with gr.Column(scale=2):
                        batch_summary = gr.Markdown(label="Podsumowanie wsadowe")
                        batch_results = gr.Dataframe(
                            label="Wyniki przetwarzania",
                            headers=["Plik", "Typ", "Sukces", "Pewność", "Priorytet", "Czas [s]", "Wartość [PLN]", "Błąd"]
                        )

                batch_analysis = gr.Markdown(label="Analiza wsadowa HVAC")

                process_batch_btn.click(
                    fn=lambda files, ai, trans: asyncio.run(
                        process_multiple_files(
                            [f.name for f in files] if files else [], ai, trans
                        )
                    ) if files else ("❌ Nie wybrano plików", pd.DataFrame(), ""),
                    inputs=[files_input, enable_ai_batch, enable_transcription_batch],
                    outputs=[batch_summary, batch_results, batch_analysis]
                )

        # Footer
        gr.Markdown("""
        ---
        **HVAC Advanced Document Processing System** |
        Powered by Gemma3-4b, NVIDIA NeMo & Advanced OCR
        """)

    return interface

def launch_interface():
    """Launch the Gradio interface."""
    interface = create_gradio_interface()

    interface.launch(
        server_name="0.0.0.0",
        server_port=7861,
        share=False,
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    launch_interface()
