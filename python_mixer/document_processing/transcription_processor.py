"""
NVIDIA NeMo Transcription Processor for HVAC CRM
Containerized Polish speech-to-text with FastConformer model.
"""

import asyncio
import aiohttp
import aiofiles
import logging
import tempfile
import os
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
import hashlib

# Audio processing
import librosa
import soundfile as sf
from pydub import AudioSegment

logger = logging.getLogger(__name__)

@dataclass
class TranscriptionResult:
    """Result from speech-to-text transcription."""
    text: str
    confidence: float
    language: str
    duration: float
    segments: List[Dict[str, Any]]
    speaker_labels: Optional[List[str]] = None
    processing_time: float = 0.0
    model_used: str = ""
    audio_quality: str = ""
    hvac_keywords: List[str] = None

@dataclass
class AudioMetadata:
    """Audio file metadata."""
    filename: str
    duration: float
    sample_rate: int
    channels: int
    format: str
    file_size: int
    quality_score: float

class TranscriptionProcessor:
    """NVIDIA NeMo-based transcription processor."""
    
    def __init__(self, 
                 nemo_service_url: str = "http://localhost:8765",
                 temp_dir: str = "/tmp/hvac_audio"):
        self.nemo_service_url = nemo_service_url.rstrip('/')
        self.temp_dir = Path(temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
        
        # HVAC-specific keywords for context
        self.hvac_keywords = [
            'klimatyzacja', 'klimatyzator', 'split', 'multi-split',
            'serwis', 'naprawa', 'montaż', 'instalacja',
            'awaria', 'usterka', 'problem', 'nie działa',
            'filtr', 'czyszczenie', 'konserwacja', 'przegląd',
            'temperatura', 'chłodzenie', 'grzanie', 'wentylacja',
            'LG', 'Daikin', 'Mitsubishi', 'Panasonic', 'Fujitsu',
            'BTU', 'kilowat', 'moc', 'wydajność', 'COP', 'SEER'
        ]
        
        # Audio preprocessing settings
        self.target_sample_rate = 16000
        self.target_channels = 1
        
    async def transcribe_audio_file(self, file_path: Union[str, Path]) -> TranscriptionResult:
        """Transcribe audio file to text."""
        file_path = Path(file_path)
        start_time = datetime.now()
        
        try:
            # Get audio metadata
            metadata = self._get_audio_metadata(file_path)
            logger.info(f"Processing audio: {metadata.filename} ({metadata.duration:.1f}s)")
            
            # Preprocess audio for better transcription
            processed_audio_path = await self._preprocess_audio(file_path)
            
            # Perform transcription
            transcription_result = await self._transcribe_with_nemo(processed_audio_path, metadata)
            
            # Post-process transcription
            transcription_result = self._post_process_transcription(transcription_result)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            transcription_result.processing_time = processing_time
            
            # Clean up temporary files
            if processed_audio_path != file_path:
                processed_audio_path.unlink(missing_ok=True)
            
            return transcription_result
            
        except Exception as e:
            logger.error(f"Transcription failed for {file_path}: {e}")
            return TranscriptionResult(
                text=f"Błąd transkrypcji: {str(e)}",
                confidence=0.0,
                language="pl",
                duration=0.0,
                segments=[],
                processing_time=(datetime.now() - start_time).total_seconds(),
                model_used="nemo-error"
            )
    
    async def transcribe_audio_data(self, audio_data: bytes, filename: str) -> TranscriptionResult:
        """Transcribe audio from bytes data."""
        # Save to temporary file
        temp_file = self.temp_dir / f"temp_{hashlib.md5(audio_data).hexdigest()}_{filename}"
        
        try:
            async with aiofiles.open(temp_file, 'wb') as f:
                await f.write(audio_data)
            
            result = await self.transcribe_audio_file(temp_file)
            return result
            
        finally:
            # Clean up
            temp_file.unlink(missing_ok=True)
    
    async def _preprocess_audio(self, file_path: Path) -> Path:
        """Preprocess audio for optimal transcription."""
        try:
            # Load audio
            audio, sr = librosa.load(str(file_path), sr=None)
            
            # Check if preprocessing is needed
            needs_processing = (
                sr != self.target_sample_rate or
                len(audio.shape) > 1 or
                self._needs_noise_reduction(audio)
            )
            
            if not needs_processing:
                return file_path
            
            # Resample to target sample rate
            if sr != self.target_sample_rate:
                audio = librosa.resample(audio, orig_sr=sr, target_sr=self.target_sample_rate)
            
            # Convert to mono if stereo
            if len(audio.shape) > 1:
                audio = librosa.to_mono(audio)
            
            # Apply noise reduction
            audio = self._reduce_noise(audio)
            
            # Normalize audio
            audio = librosa.util.normalize(audio)
            
            # Save processed audio
            processed_path = self.temp_dir / f"processed_{file_path.stem}.wav"
            sf.write(str(processed_path), audio, self.target_sample_rate)
            
            return processed_path
            
        except Exception as e:
            logger.warning(f"Audio preprocessing failed: {e}, using original file")
            return file_path
    
    async def _transcribe_with_nemo(self, audio_path: Path, metadata: AudioMetadata) -> TranscriptionResult:
        """Perform transcription using NVIDIA NeMo service."""
        
        # Prepare request data
        transcription_data = {
            'language': 'pl',
            'model': 'stt_pl_fastconformer_ctc_large',
            'enable_automatic_punctuation': True,
            'enable_speaker_diarization': metadata.duration > 30,  # Enable for longer audio
            'hvac_context': True,  # Custom flag for HVAC domain adaptation
            'keywords': self.hvac_keywords
        }
        
        try:
            # Upload audio file and get transcription
            async with aiohttp.ClientSession() as session:
                with open(audio_path, 'rb') as audio_file:
                    form_data = aiohttp.FormData()
                    form_data.add_field('audio', audio_file, filename=audio_path.name)
                    form_data.add_field('config', json.dumps(transcription_data))
                    
                    timeout = aiohttp.ClientTimeout(total=max(300, metadata.duration * 10))
                    
                    async with session.post(
                        f"{self.nemo_service_url}/transcribe",
                        data=form_data,
                        timeout=timeout
                    ) as response:
                        
                        if response.status == 200:
                            result = await response.json()
                            return self._parse_nemo_response(result, metadata)
                        else:
                            error_text = await response.text()
                            raise Exception(f"NeMo service error {response.status}: {error_text}")
                            
        except aiohttp.ClientError as e:
            logger.error(f"Connection to NeMo service failed: {e}")
            # Fallback to basic transcription
            return await self._fallback_transcription(audio_path, metadata)
    
    def _parse_nemo_response(self, response: Dict, metadata: AudioMetadata) -> TranscriptionResult:
        """Parse NeMo service response."""
        
        # Extract main transcription text
        text = response.get('text', '')
        confidence = response.get('confidence', 0.0)
        
        # Extract segments with timestamps
        segments = []
        if 'segments' in response:
            for seg in response['segments']:
                segments.append({
                    'start': seg.get('start', 0.0),
                    'end': seg.get('end', 0.0),
                    'text': seg.get('text', ''),
                    'confidence': seg.get('confidence', 0.0)
                })
        
        # Extract speaker labels if available
        speaker_labels = response.get('speaker_labels', None)
        
        # Determine audio quality
        audio_quality = self._assess_audio_quality(confidence, metadata)
        
        return TranscriptionResult(
            text=text,
            confidence=confidence,
            language='pl',
            duration=metadata.duration,
            segments=segments,
            speaker_labels=speaker_labels,
            model_used='nemo-fastconformer-pl',
            audio_quality=audio_quality
        )
    
    async def _fallback_transcription(self, audio_path: Path, metadata: AudioMetadata) -> TranscriptionResult:
        """Fallback transcription method when NeMo service is unavailable."""
        logger.warning("Using fallback transcription method")
        
        # This could integrate with other STT services like Whisper
        # For now, return a placeholder result
        return TranscriptionResult(
            text="[Transkrypcja niedostępna - serwis NeMo nieosiągalny]",
            confidence=0.0,
            language='pl',
            duration=metadata.duration,
            segments=[],
            model_used='fallback',
            audio_quality='unknown'
        )
    
    def _post_process_transcription(self, result: TranscriptionResult) -> TranscriptionResult:
        """Post-process transcription for HVAC domain."""
        
        # Identify HVAC keywords in transcription
        hvac_keywords_found = []
        text_lower = result.text.lower()
        
        for keyword in self.hvac_keywords:
            if keyword.lower() in text_lower:
                hvac_keywords_found.append(keyword)
        
        result.hvac_keywords = hvac_keywords_found
        
        # Apply domain-specific corrections
        corrected_text = self._apply_hvac_corrections(result.text)
        result.text = corrected_text
        
        # Boost confidence if HVAC keywords are present
        if hvac_keywords_found:
            keyword_boost = min(0.1, len(hvac_keywords_found) * 0.02)
            result.confidence = min(1.0, result.confidence + keyword_boost)
        
        return result
    
    def _apply_hvac_corrections(self, text: str) -> str:
        """Apply HVAC-specific text corrections."""
        corrections = {
            'klimatyzator': ['klimatyzator', 'klimatyzacja'],
            'split': ['split', 'splitu'],
            'serwis': ['serwis', 'service'],
            'montaż': ['montaż', 'montażu'],
            'naprawa': ['naprawa', 'naprawy'],
            'awaria': ['awaria', 'awarii'],
            'filtr': ['filtr', 'filtru', 'filtry'],
            'LG': ['LG', 'el dżi', 'eldżi'],
            'Daikin': ['Daikin', 'dajkin', 'daikin'],
            'Mitsubishi': ['Mitsubishi', 'mitsubishi', 'micubishi']
        }
        
        corrected_text = text
        for correct_form, variants in corrections.items():
            for variant in variants:
                if variant != correct_form:
                    corrected_text = corrected_text.replace(variant, correct_form)
        
        return corrected_text
    
    def _get_audio_metadata(self, file_path: Path) -> AudioMetadata:
        """Extract audio file metadata."""
        try:
            # Use librosa for detailed audio info
            audio, sr = librosa.load(str(file_path), sr=None)
            duration = librosa.get_duration(y=audio, sr=sr)
            
            # Get file info
            file_stat = file_path.stat()
            
            # Assess audio quality
            quality_score = self._calculate_audio_quality_score(audio, sr)
            
            return AudioMetadata(
                filename=file_path.name,
                duration=duration,
                sample_rate=sr,
                channels=1 if len(audio.shape) == 1 else audio.shape[0],
                format=file_path.suffix.lower(),
                file_size=file_stat.st_size,
                quality_score=quality_score
            )
            
        except Exception as e:
            logger.error(f"Failed to get audio metadata: {e}")
            # Return basic metadata
            file_stat = file_path.stat()
            return AudioMetadata(
                filename=file_path.name,
                duration=0.0,
                sample_rate=0,
                channels=0,
                format=file_path.suffix.lower(),
                file_size=file_stat.st_size,
                quality_score=0.0
            )
    
    def _calculate_audio_quality_score(self, audio: np.ndarray, sr: int) -> float:
        """Calculate audio quality score."""
        try:
            # Calculate various quality metrics
            
            # 1. Signal-to-noise ratio estimate
            rms = librosa.feature.rms(y=audio)[0]
            snr_estimate = np.mean(rms) / (np.std(rms) + 1e-8)
            
            # 2. Spectral centroid (frequency distribution)
            spectral_centroids = librosa.feature.spectral_centroid(y=audio, sr=sr)[0]
            spectral_score = np.mean(spectral_centroids) / (sr / 2)  # Normalize
            
            # 3. Zero crossing rate (speech characteristic)
            zcr = librosa.feature.zero_crossing_rate(audio)[0]
            zcr_score = 1.0 - abs(np.mean(zcr) - 0.1)  # Optimal around 0.1 for speech
            
            # Combine scores
            quality_score = (snr_estimate * 0.4 + spectral_score * 0.3 + zcr_score * 0.3)
            return min(1.0, max(0.0, quality_score))
            
        except Exception:
            return 0.5  # Default medium quality
    
    def _needs_noise_reduction(self, audio: np.ndarray) -> bool:
        """Determine if audio needs noise reduction."""
        try:
            # Simple noise detection based on signal characteristics
            rms = librosa.feature.rms(y=audio)[0]
            noise_threshold = np.percentile(rms, 10)  # Bottom 10% as noise estimate
            signal_threshold = np.percentile(rms, 90)  # Top 10% as signal
            
            snr_estimate = signal_threshold / (noise_threshold + 1e-8)
            return snr_estimate < 10  # Apply noise reduction if SNR < 10
            
        except Exception:
            return False
    
    def _reduce_noise(self, audio: np.ndarray) -> np.ndarray:
        """Apply basic noise reduction."""
        try:
            # Simple spectral gating noise reduction
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)
            
            # Estimate noise floor
            noise_floor = np.percentile(magnitude, 20, axis=1, keepdims=True)
            
            # Apply spectral gating
            mask = magnitude > (noise_floor * 2)  # Keep signal 2x above noise floor
            stft_cleaned = stft * mask
            
            # Reconstruct audio
            audio_cleaned = librosa.istft(stft_cleaned)
            return audio_cleaned
            
        except Exception:
            return audio  # Return original if noise reduction fails
    
    def _assess_audio_quality(self, confidence: float, metadata: AudioMetadata) -> str:
        """Assess overall audio quality."""
        if confidence > 0.9 and metadata.quality_score > 0.8:
            return "excellent"
        elif confidence > 0.7 and metadata.quality_score > 0.6:
            return "good"
        elif confidence > 0.5 and metadata.quality_score > 0.4:
            return "fair"
        else:
            return "poor"
    
    async def test_nemo_service(self) -> bool:
        """Test connection to NeMo transcription service."""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.nemo_service_url}/health") as response:
                    return response.status == 200
        except Exception as e:
            logger.error(f"NeMo service test failed: {e}")
            return False
