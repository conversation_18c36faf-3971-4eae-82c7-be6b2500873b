#!/usr/bin/env python3
"""Enhanced Gradio Interface Launcher for HVAC Multi-Agent System."""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from gradio_interface import HVACGradioInterface
from loguru import logger

def setup_logging(debug: bool = False):
    """Setup logging configuration."""
    log_level = "DEBUG" if debug else "INFO"
    
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file handler
    logger.add(
        "logs/hvac_gradio.log",
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )

def create_logs_directory():
    """Create logs directory if it doesn't exist."""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

def check_dependencies():
    """Check if all required dependencies are installed."""
    required_packages = [
        "gradio",
        "plotly",
        "pandas",
        "loguru"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        logger.error(f"Missing required packages: {', '.join(missing_packages)}")
        logger.info("Install missing packages with: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Enhanced HVAC Multi-Agent System Gradio Interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python enhanced_gradio_launcher.py                    # Launch with default settings
  python enhanced_gradio_launcher.py --port 8080        # Launch on port 8080
  python enhanced_gradio_launcher.py --debug --share    # Debug mode with public sharing
  python enhanced_gradio_launcher.py --host 0.0.0.0     # Listen on all interfaces
        """
    )
    
    parser.add_argument(
        "--host",
        default="127.0.0.1",
        help="Host to bind the server to (default: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=7860,
        help="Port to run the server on (default: 7860)"
    )
    
    parser.add_argument(
        "--share",
        action="store_true",
        help="Create a public shareable link"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="Enable debug mode with detailed logging"
    )
    
    parser.add_argument(
        "--auth",
        nargs=2,
        metavar=("USERNAME", "PASSWORD"),
        help="Enable basic authentication (username password)"
    )
    
    parser.add_argument(
        "--ssl-keyfile",
        help="Path to SSL key file for HTTPS"
    )
    
    parser.add_argument(
        "--ssl-certfile",
        help="Path to SSL certificate file for HTTPS"
    )
    
    args = parser.parse_args()
    
    # Setup logging
    create_logs_directory()
    setup_logging(args.debug)
    
    logger.info("🚀 Starting Enhanced HVAC Gradio Interface...")
    logger.info(f"Host: {args.host}, Port: {args.port}")
    
    # Check dependencies
    if not check_dependencies():
        sys.exit(1)
    
    try:
        # Initialize interface
        logger.info("🔧 Initializing HVAC interface...")
        interface_manager = HVACGradioInterface()
        interface = interface_manager.create_interface()
        
        # Prepare launch arguments
        launch_kwargs = {
            "server_name": args.host,
            "server_port": args.port,
            "share": args.share,
            "debug": args.debug,
            "show_error": True,
            "quiet": not args.debug,
            "favicon_path": None,  # Could add custom favicon
            "app_kwargs": {}
        }
        
        # Add authentication if specified
        if args.auth:
            launch_kwargs["auth"] = tuple(args.auth)
            logger.info("🔐 Authentication enabled")
        
        # Add SSL if specified
        if args.ssl_keyfile and args.ssl_certfile:
            launch_kwargs["ssl_keyfile"] = args.ssl_keyfile
            launch_kwargs["ssl_certfile"] = args.ssl_certfile
            logger.info("🔒 SSL/HTTPS enabled")
        
        # Launch interface
        logger.info("🌐 Launching Gradio interface...")
        logger.info(f"📱 Interface will be available at: http{'s' if args.ssl_keyfile else ''}://{args.host}:{args.port}")
        
        if args.share:
            logger.info("🌍 Public sharing enabled - link will be displayed")
        
        interface.launch(**launch_kwargs)
        
    except KeyboardInterrupt:
        logger.info("👋 Shutting down gracefully...")
    except Exception as e:
        logger.error(f"❌ Failed to start interface: {e}")
        if args.debug:
            logger.exception("Full traceback:")
        sys.exit(1)

if __name__ == "__main__":
    main()
