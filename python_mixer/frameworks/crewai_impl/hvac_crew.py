"""Enhanced CrewAI implementation for comprehensive HVAC business automation.

Based on GoBackend-Kratos-fresh intelligence and Enhanced Email Parser v2.0.
Provides complete multi-agent system for HVAC company operations including:
- Advanced email processing with AI analysis
- Customer relationship management
- Technical service coordination
- Business intelligence and analytics
- Equipment lifecycle management
- Financial analysis and forecasting
- Quality assurance and compliance
- Emergency response coordination
"""

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from langchain_openai import ChatOpenAI
from langchain_anthropic import <PERSON>t<PERSON>nthropic
from typing import Dict, List, Any, Optional, Union
import logging
import json
import asyncio
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum

from ...email_processing.email_parser import <PERSON>hanced<PERSON><PERSON><PERSON><PERSON><PERSON>, EmailContent, HVACEmailAnalysis
from ...email_processing.imap_client import IMAPClient, EmailFilter
from ...config import config

logger = logging.getLogger(__name__)


# Enhanced data structures for comprehensive HVAC operations
class AgentRole(Enum):
    """Specialized agent roles for HVAC business."""
    EMAIL_INTELLIGENCE = "email_intelligence"
    CUSTOMER_SUCCESS = "customer_success"
    TECHNICAL_COORDINATOR = "technical_coordinator"
    BUSINESS_ANALYST = "business_analyst"
    EQUIPMENT_SPECIALIST = "equipment_specialist"
    FINANCIAL_ADVISOR = "financial_advisor"
    QUALITY_MANAGER = "quality_manager"
    EMERGENCY_RESPONDER = "emergency_responder"
    SALES_STRATEGIST = "sales_strategist"
    COMPLIANCE_OFFICER = "compliance_officer"


class TaskPriority(Enum):
    """Task priority levels."""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class BusinessMetrics:
    """Business metrics and KPIs."""
    customer_satisfaction: float = 0.0
    response_time_avg: float = 0.0
    revenue_monthly: float = 0.0
    service_completion_rate: float = 0.0
    equipment_uptime: float = 0.0
    technician_utilization: float = 0.0
    lead_conversion_rate: float = 0.0
    churn_rate: float = 0.0


@dataclass
class ServiceTicket:
    """Enhanced service ticket structure."""
    ticket_id: str
    customer_id: str
    equipment_id: Optional[str]
    priority: TaskPriority
    service_type: str
    description: str
    technician_assigned: Optional[str]
    estimated_duration: timedelta
    scheduled_date: Optional[datetime]
    status: str
    parts_required: List[str] = field(default_factory=list)
    cost_estimate: Optional[float] = None
    customer_notes: str = ""
    internal_notes: str = ""
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)


class EmailFetchTool(BaseTool):
    """Tool for fetching emails from IMAP server."""
    
    name: str = "email_fetch_tool"
    description: str = "Fetch new emails from the HVAC business email account"
    
    def _run(self, max_emails: int = 5) -> str:
        """Fetch new emails and return summary."""
        try:
            imap_client = IMAPClient()
            with imap_client as client:
                email_filter = EmailFilter(
                    unseen_only=True,
                    max_emails=max_emails
                )
                
                emails = list(client.fetch_emails(email_filter))
                
                if emails:
                    return f"Fetched {len(emails)} new emails. First email preview: {emails[0][:200]}..."
                else:
                    return "No new emails found."
                    
        except Exception as e:
            return f"Error fetching emails: {str(e)}"


class EmailAnalysisTool(BaseTool):
    """Tool for analyzing email content."""
    
    name: str = "email_analysis_tool"
    description: str = "Analyze email content for HVAC-specific information"
    
    def __init__(self):
        super().__init__()
        self.email_parser = EmailParser()
    
    def _run(self, raw_email: str) -> str:
        """Analyze email and return structured analysis."""
        try:
            # Parse email
            parsed_email = self.email_parser.parse_email(raw_email)
            
            # Perform HVAC analysis
            analysis = self.email_parser.analyze_hvac_content(parsed_email)
            
            # Format analysis results
            result = f"""
Email Analysis Results:
- Subject: {parsed_email.subject}
- Sender: {parsed_email.sender}
- Type: {analysis.email_type}
- Priority: {analysis.priority}
- Customer: {analysis.customer_info.get('name', 'Unknown')}
- Equipment: {', '.join(analysis.equipment_mentioned) if analysis.equipment_mentioned else 'None'}
- Service Type: {analysis.service_type or 'Not specified'}
- Urgency: {', '.join(analysis.urgency_indicators) if analysis.urgency_indicators else 'None'}
- Estimated Value: {analysis.estimated_value or 'Not estimated'}
- Follow-up Required: {'Yes' if analysis.follow_up_required else 'No'}
- Sentiment: {analysis.sentiment}
"""
            return result
            
        except Exception as e:
            return f"Error analyzing email: {str(e)}"


class ResponseGenerationTool(BaseTool):
    """Tool for generating email responses."""
    
    name: str = "response_generation_tool"
    description: str = "Generate professional responses to HVAC customer emails"
    
    def _run(self, email_analysis: str, customer_name: str = "Valued Customer") -> str:
        """Generate response based on email analysis."""
        try:
            # Extract key information from analysis
            lines = email_analysis.split('\n')
            email_type = "general"
            priority = "medium"
            
            for line in lines:
                if "Type:" in line:
                    email_type = line.split("Type:")[1].strip()
                elif "Priority:" in line:
                    priority = line.split("Priority:")[1].strip()
            
            # Generate appropriate response template
            if email_type == "service_request":
                response = f"""
Szanowny/a {customer_name},

Dziękujemy za zgłoszenie serwisowe. Otrzymaliśmy Państwa wiadomość i traktujemy ją priorytetowo.

Nasz zespół techniczny skontaktuje się z Państwem w ciągu 24 godzin w celu umówienia wizyty serwisowej.

W przypadku pilnych awarii prosimy o kontakt telefoniczny pod numerem: +48 XXX XXX XXX

Z poważaniem,
Zespół Serwisowy {config.app.company_name}
"""
            elif email_type == "quote_inquiry":
                response = f"""
Szanowny/a {customer_name},

Dziękujemy za zainteresowanie naszymi usługami HVAC.

Przygotujemy dla Państwa szczegółową ofertę. W celu dokładnej wyceny proponujemy wizytę naszego specjalisty w dogodnym dla Państwa terminie.

Skontaktujemy się z Państwem w ciągu 2 dni roboczych.

Z poważaniem,
Dział Handlowy {config.app.company_name}
"""
            else:
                response = f"""
Szanowny/a {customer_name},

Dziękujemy za kontakt z {config.app.company_name}.

Otrzymaliśmy Państwa wiadomość i odpowiemy na nią w najkrótszym możliwym czasie.

Z poważaniem,
{config.app.company_name}
"""
            
            return response.strip()
            
        except Exception as e:
            return f"Error generating response: {str(e)}"


class TaskCreationTool(BaseTool):
    """Tool for creating tasks based on email analysis."""
    
    name: str = "task_creation_tool"
    description: str = "Create tasks and follow-up actions based on email analysis"
    
    def _run(self, email_analysis: str) -> str:
        """Create tasks based on email analysis."""
        try:
            tasks = []
            
            # Parse analysis to determine tasks
            if "service_request" in email_analysis:
                tasks.extend([
                    "Schedule service technician visit",
                    "Prepare service equipment and parts",
                    "Contact customer within 24 hours"
                ])
            
            if "quote_inquiry" in email_analysis:
                tasks.extend([
                    "Prepare detailed quote",
                    "Schedule site visit for assessment",
                    "Contact customer within 48 hours"
                ])
            
            if "complaint" in email_analysis:
                tasks.extend([
                    "Escalate to customer service manager",
                    "Schedule immediate follow-up call",
                    "Document complaint details"
                ])
            
            if "high" in email_analysis:
                tasks.append("Mark as high priority - immediate attention required")
            
            if "Follow-up Required: Yes" in email_analysis:
                tasks.append("Schedule follow-up contact")
            
            if not tasks:
                tasks.append("Review and file email")
            
            return "Created tasks:\n" + "\n".join(f"- {task}" for task in tasks)
            
        except Exception as e:
            return f"Error creating tasks: {str(e)}"


class HVACEmailCrew:
    """CrewAI crew for HVAC email processing."""
    
    def __init__(self, model_name: str = "gpt-4"):
        """Initialize the HVAC email processing crew."""
        self.model_name = model_name
        self.llm = self._create_llm()
        self.tools = self._create_tools()
        self.agents = self._create_agents()
        self.tasks = self._create_tasks()
        self.crew = self._create_crew()
    
    def _create_llm(self):
        """Create LLM instance."""
        if self.model_name.startswith("gpt"):
            return ChatOpenAI(
                model=self.model_name,
                api_key=config.ai.openai_api_key,
                temperature=config.ai.temperature
            )
        elif self.model_name.startswith("claude"):
            return ChatAnthropic(
                model=self.model_name,
                api_key=config.ai.anthropic_api_key,
                temperature=config.ai.temperature
            )
        else:
            raise ValueError(f"Unsupported model: {self.model_name}")
    
    def _create_tools(self):
        """Create tools for the agents."""
        return [
            EmailFetchTool(),
            EmailAnalysisTool(),
            ResponseGenerationTool(),
            TaskCreationTool()
        ]
    
    def _create_agents(self):
        """Create specialized agents for HVAC email processing."""
        
        email_monitor = Agent(
            role='Email Monitor Specialist',
            goal='Monitor and fetch new emails from HVAC business accounts',
            backstory="""You are an experienced email monitoring specialist for a HVAC company. 
            Your job is to continuously monitor email accounts for new customer inquiries, 
            service requests, and business communications. You understand the importance of 
            timely response in the HVAC industry.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.tools[0]],  # EmailFetchTool
            llm=self.llm
        )
        
        email_analyzer = Agent(
            role='HVAC Email Analysis Expert',
            goal='Analyze emails for HVAC-specific content and customer needs',
            backstory="""You are a senior HVAC business analyst with deep knowledge of 
            air conditioning, heating, and ventilation systems. You can quickly identify 
            customer needs, equipment types, service requirements, and urgency levels 
            from email communications. You understand Polish HVAC terminology and 
            customer communication patterns.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.tools[1]],  # EmailAnalysisTool
            llm=self.llm
        )
        
        customer_service = Agent(
            role='Customer Service Representative',
            goal='Generate professional and helpful responses to customer emails',
            backstory="""You are an experienced customer service representative for a 
            Polish HVAC company. You excel at creating professional, empathetic, and 
            helpful responses to customer inquiries. You understand HVAC services, 
            pricing, and can communicate complex technical information in simple terms. 
            You always maintain a professional tone while being warm and helpful.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.tools[2]],  # ResponseGenerationTool
            llm=self.llm
        )
        
        task_manager = Agent(
            role='HVAC Operations Task Manager',
            goal='Create and organize tasks based on customer emails and business needs',
            backstory="""You are an operations manager for a HVAC company with extensive 
            experience in project management and service coordination. You excel at 
            breaking down customer requests into actionable tasks, prioritizing work, 
            and ensuring nothing falls through the cracks. You understand the workflow 
            of HVAC installation, maintenance, and repair services.""",
            verbose=True,
            allow_delegation=False,
            tools=[self.tools[3]],  # TaskCreationTool
            llm=self.llm
        )
        
        return {
            'email_monitor': email_monitor,
            'email_analyzer': email_analyzer,
            'customer_service': customer_service,
            'task_manager': task_manager
        }
    
    def _create_tasks(self):
        """Create tasks for the crew."""
        
        monitor_task = Task(
            description="""Monitor the HVAC business email accounts and fetch any new emails. 
            Focus on emails from customers, suppliers, and business partners. 
            Provide a summary of new emails found.""",
            agent=self.agents['email_monitor'],
            expected_output="Summary of new emails fetched with count and brief preview"
        )
        
        analysis_task = Task(
            description="""Analyze the fetched emails for HVAC-specific content. 
            Identify customer needs, equipment types, service requirements, urgency levels, 
            and any other relevant business information. Classify each email by type and priority.""",
            agent=self.agents['email_analyzer'],
            expected_output="Detailed analysis of email content with classification and priority"
        )
        
        response_task = Task(
            description="""Generate professional responses to customer emails based on the analysis. 
            Ensure responses are appropriate for the email type, maintain professional tone, 
            and include relevant next steps or information.""",
            agent=self.agents['customer_service'],
            expected_output="Professional email response ready to send to customer"
        )
        
        task_creation = Task(
            description="""Create actionable tasks and follow-up items based on the email analysis. 
            Organize tasks by priority and assign appropriate timelines. Ensure all customer 
            needs are addressed with specific action items.""",
            agent=self.agents['task_manager'],
            expected_output="List of organized tasks with priorities and timelines"
        )
        
        return [monitor_task, analysis_task, response_task, task_creation]
    
    def _create_crew(self):
        """Create the crew with agents and tasks."""
        return Crew(
            agents=list(self.agents.values()),
            tasks=self.tasks,
            process=Process.sequential,
            verbose=2
        )
    
    def run_email_processing(self) -> str:
        """Run the complete email processing workflow."""
        try:
            logger.info("Starting CrewAI HVAC email processing")
            result = self.crew.kickoff()
            logger.info("CrewAI workflow completed successfully")
            return result
        except Exception as e:
            logger.error(f"CrewAI workflow error: {e}")
            return f"Error in email processing: {str(e)}"
    
    def run_continuous_processing(self, interval_minutes: int = 5):
        """Run continuous email processing."""
        import time
        
        logger.info(f"Starting continuous CrewAI processing (every {interval_minutes} minutes)")
        
        while True:
            try:
                result = self.run_email_processing()
                logger.info(f"Processing cycle completed: {result[:200]}...")
                
                # Wait for next cycle
                time.sleep(interval_minutes * 60)
                
            except KeyboardInterrupt:
                logger.info("Continuous processing stopped by user")
                break
            except Exception as e:
                logger.error(f"Processing cycle error: {e}")
                time.sleep(interval_minutes * 60)


# Convenience function for quick email processing
def process_hvac_emails_with_crew(model_name: str = "gpt-4") -> str:
    """Quick function to process HVAC emails with CrewAI."""
    crew = HVACEmailCrew(model_name=model_name)
    return crew.run_email_processing()


if __name__ == "__main__":
    # Example usage
    crew = HVACEmailCrew()
    result = crew.run_email_processing()
    print(f"CrewAI Result: {result}")
