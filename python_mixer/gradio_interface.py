"""Comprehensive Gradio Interface for HVAC Multi-Agent System."""

import asyncio
import json
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import base64
from io import BytesIO
import time

import gradio as gr
from loguru import logger

# Import system components
from .main import HVACEmailAnalysisSystem
try:
    from .krabulon.agents import AgentOrchestrator
    from .krabulon.agents.quote_analyzer import QuoteAnalyzerAgent
    from .krabulon.agents.equipment_matcher import EquipmentMatcherAgent
    from .krabulon.agents.quote_generator import QuoteGeneratorAgent
except ImportError:
    logger.warning("Krabulon agents not available")
    AgentOrchestrator = None
    QuoteAnalyzerAgent = None
    EquipmentMatcherAgent = None
    QuoteGeneratorAgent = None

from .database.sql.models import db_manager


class HVACGradioInterface:
    """Comprehensive Gradio interface for HVAC systems."""

    def __init__(self):
        self.hvac_system = HVACEmailAnalysisSystem()
        self.krabulon_orchestrator = None

        # Initialize agents if available
        if QuoteAnalyzerAgent:
            self.quote_analyzer = QuoteAnalyzerAgent()
        else:
            self.quote_analyzer = None

        if EquipmentMatcherAgent:
            self.equipment_matcher = EquipmentMatcherAgent()
        else:
            self.equipment_matcher = None

        if QuoteGeneratorAgent:
            self.quote_generator = QuoteGeneratorAgent()
        else:
            self.quote_generator = None

        # System state
        self.system_initialized = False
        self.last_refresh = datetime.now()

        # Initialize system
        self._initialize_system()
    
    def _initialize_system(self):
        """Initialize the HVAC system components."""
        try:
            # Initialize databases
            db_success = self.hvac_system.initialize_databases()

            # Initialize Krabulon orchestrator if available
            if AgentOrchestrator:
                try:
                    asyncio.run(self._init_krabulon())
                except Exception as e:
                    logger.warning(f"Krabulon initialization failed: {e}")

            self.system_initialized = db_success
            logger.info("HVAC Gradio Interface initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize HVAC system: {e}")
            self.system_initialized = False

    async def _init_krabulon(self):
        """Initialize Krabulon orchestrator."""
        try:
            if AgentOrchestrator:
                self.krabulon_orchestrator = AgentOrchestrator()
                await self.krabulon_orchestrator.initialize()
        except Exception as e:
            logger.error(f"Failed to initialize Krabulon: {e}")
    
    def create_interface(self) -> gr.Blocks:
        """Create the main Gradio interface."""

        # Custom CSS for enhanced styling
        custom_css = """
        .gradio-container {
            max-width: 1400px !important;
            margin: 0 auto;
        }
        .header-gradient {
            background: linear-gradient(90deg, #1f4e79, #2d5aa0);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        """

        with gr.Blocks(
            title="🏠 HVAC Multi-Agent System",
            theme=gr.themes.Soft(),
            css=custom_css
        ) as interface:

            # Header
            gr.HTML("""
            <div class="header-gradient">
                <h1>🏠 HVAC Multi-Agent System</h1>
                <h3>Kompleksowy system analizy emaili, generowania ofert i zarządzania danymi HVAC</h3>
            </div>
            """)

            # Status indicator
            with gr.Row():
                system_status_html = gr.HTML(
                    value=self._get_system_status_html(),
                    label="Status Systemu"
                )
                refresh_btn = gr.Button("🔄 Odśwież Status", size="sm")

            # Main tabbed interface
            with gr.Tabs():

                # Tab 1: Email Analysis
                with gr.Tab("📧 Analiza Emaili"):
                    self._create_email_analysis_tab()

                # Tab 2: Quote Generation
                with gr.Tab("📋 Generator Ofert"):
                    self._create_quote_generation_tab()

                # Tab 3: Equipment Database
                with gr.Tab("🔧 Baza Sprzętu"):
                    self._create_equipment_database_tab()

                # Tab 4: Krabulon Data Enrichment
                with gr.Tab("🌐 Krabulon - Wzbogacanie Danych"):
                    self._create_krabulon_tab()

                # Tab 5: System Management
                with gr.Tab("⚙️ Zarządzanie Systemem"):
                    self._create_system_management_tab()

                # Tab 6: Analytics & Reports
                with gr.Tab("📊 Analityka i Raporty"):
                    self._create_analytics_tab()

            # Event handlers
            refresh_btn.click(
                fn=self._refresh_system_status,
                outputs=[system_status_html]
            )

        return interface
    
    def _create_email_analysis_tab(self):
        """Create enhanced email analysis tab."""

        gr.Markdown("### 📧 Zaawansowana Analiza Emaili HVAC z AI")

        with gr.Row():
            with gr.Column(scale=2):
                # Email input section
                gr.Markdown("#### 📝 Wprowadź Email")
                email_input = gr.Textbox(
                    label="Treść emaila",
                    placeholder="Wklej tutaj treść emaila od klienta...\n\nPrzykład:\nFrom: <EMAIL>\nSubject: Awaria klimatyzacji\n\nDzień dobry, mam problem z klimatyzacją LG...",
                    lines=12,
                    max_lines=20
                )

                # Quick email templates
                with gr.Accordion("📋 Szablony Testowe", open=False):
                    gr.Markdown("Kliknij aby wstawić przykładowy email:")
                    with gr.Row():
                        template_service = gr.Button("🔧 Serwis", size="sm")
                        template_quote = gr.Button("💰 Oferta", size="sm")
                        template_complaint = gr.Button("😠 Reklamacja", size="sm")

                # Framework and options
                with gr.Row():
                    framework_choice = gr.Radio(
                        choices=["LangGraph", "CrewAI", "OpenAI Swarm"],
                        value="LangGraph",
                        label="🤖 Framework AI",
                        info="Wybierz framework do analizy"
                    )

                    with gr.Column():
                        # Analysis options
                        with gr.Accordion("⚙️ Opcje Analizy", open=True):
                            include_sentiment = gr.Checkbox(
                                label="😊 Analiza sentymentu",
                                value=True
                            )
                            include_entities = gr.Checkbox(
                                label="🏷️ Rozpoznawanie encji",
                                value=True
                            )
                            include_intent = gr.Checkbox(
                                label="🎯 Klasyfikacja intencji",
                                value=True
                            )
                            include_priority = gr.Checkbox(
                                label="⚡ Ocena priorytetu",
                                value=True
                            )

                # Action buttons
                with gr.Row():
                    analyze_btn = gr.Button("🔍 Analizuj Email", variant="primary", size="lg")
                    clear_btn = gr.Button("🗑️ Wyczyść", variant="secondary")

            with gr.Column(scale=3):
                # Real-time analysis status
                analysis_status = gr.HTML(
                    value="<div class='metric-card'>Gotowy do analizy</div>",
                    label="Status"
                )

                # Results tabs
                with gr.Tabs():
                    with gr.Tab("📊 Wyniki Analizy"):
                        analysis_results = gr.JSON(
                            label="Szczegółowe wyniki",
                            value={}
                        )

                    with gr.Tab("👤 Profil Klienta"):
                        customer_insights = gr.Textbox(
                            label="Wnioski o kliencie",
                            lines=6,
                            interactive=False,
                            placeholder="Analiza profilu klienta pojawi się tutaj..."
                        )

                    with gr.Tab("🎯 Rekomendacje"):
                        recommended_actions = gr.Textbox(
                            label="Rekomendowane działania",
                            lines=6,
                            interactive=False,
                            placeholder="Sugerowane działania pojawią się tutaj..."
                        )

                    with gr.Tab("📈 Metryki"):
                        metrics_plot = gr.Plot(
                            label="Wizualizacja metryk",
                            value=self._create_empty_metrics_plot()
                        )

        # Event handlers
        analyze_btn.click(
            fn=self._analyze_email_enhanced,
            inputs=[email_input, framework_choice, include_sentiment, include_entities, include_intent, include_priority],
            outputs=[analysis_status, analysis_results, customer_insights, recommended_actions, metrics_plot]
        )

        clear_btn.click(
            fn=lambda: ("", {}, "", "", self._create_empty_metrics_plot()),
            outputs=[email_input, analysis_results, customer_insights, recommended_actions, metrics_plot]
        )

        # Template buttons
        template_service.click(
            fn=lambda: self._get_email_template("service"),
            outputs=[email_input]
        )
        template_quote.click(
            fn=lambda: self._get_email_template("quote"),
            outputs=[email_input]
        )
        template_complaint.click(
            fn=lambda: self._get_email_template("complaint"),
            outputs=[email_input]
        )
    
    def _create_quote_generation_tab(self):
        """Create enhanced quote generation tab."""

        gr.Markdown("### 📋 Generator Profesjonalnych Ofert HVAC")

        with gr.Row():
            with gr.Column(scale=2):
                # Customer information
                gr.Markdown("#### 👤 Informacje o Kliencie")
                with gr.Row():
                    customer_name = gr.Textbox(label="Imię i nazwisko", placeholder="Jan Kowalski")
                    customer_company = gr.Textbox(label="Firma (opcjonalnie)", placeholder="ABC Sp. z o.o.")

                with gr.Row():
                    customer_email = gr.Textbox(label="Email", placeholder="<EMAIL>")
                    customer_phone = gr.Textbox(label="Telefon", placeholder="+48 ***********")

                customer_address = gr.Textbox(
                    label="Adres instalacji",
                    lines=2,
                    placeholder="ul. Przykładowa 123\n00-000 Warszawa"
                )

                # Project requirements
                gr.Markdown("#### 🏠 Wymagania Projektu")
                with gr.Row():
                    room_count = gr.Number(label="Liczba pomieszczeń", value=1, minimum=1, maximum=20)
                    total_area = gr.Number(label="Powierzchnia całkowita (m²)", value=50, minimum=10, maximum=1000)
                    ceiling_height = gr.Number(label="Wysokość sufitu (m)", value=2.7, minimum=2.0, maximum=5.0)

                with gr.Row():
                    cooling_priority = gr.Radio(
                        choices=["Wysokie", "Średnie", "Niskie"],
                        value="Średnie",
                        label="🧊 Priorytet chłodzenia"
                    )
                    heating_priority = gr.Radio(
                        choices=["Wysokie", "Średnie", "Niskie"],
                        value="Średnie",
                        label="🔥 Priorytet ogrzewania"
                    )

                budget_range = gr.Radio(
                    choices=["Premium (>20k PLN)", "Standard (10-20k PLN)", "Budżetowe (<10k PLN)"],
                    value="Standard (10-20k PLN)",
                    label="💰 Zakres budżetowy"
                )

                # Additional requirements
                with gr.Accordion("⚙️ Dodatkowe Wymagania", open=False):
                    with gr.Row():
                        wifi_required = gr.Checkbox(label="📶 Sterowanie WiFi")
                        quiet_operation = gr.Checkbox(label="🔇 Cicha praca")
                    with gr.Row():
                        air_purification = gr.Checkbox(label="🌬️ Oczyszczanie powietrza")
                        smart_features = gr.Checkbox(label="🤖 Funkcje smart")
                    with gr.Row():
                        energy_efficiency = gr.Checkbox(label="⚡ Wysoka efektywność energetyczna")
                        quick_installation = gr.Checkbox(label="⚡ Szybka instalacja")

                # AI-powered suggestions
                with gr.Accordion("🤖 Sugestie AI", open=False):
                    ai_suggestions_btn = gr.Button("🔍 Analizuj wymagania AI", variant="secondary")
                    ai_suggestions = gr.Textbox(
                        label="Sugestie systemu AI",
                        lines=3,
                        interactive=False,
                        placeholder="Kliknij przycisk aby otrzymać sugestie AI..."
                    )

                # Action buttons
                with gr.Row():
                    generate_quote_btn = gr.Button("📋 Generuj Ofertę", variant="primary", size="lg")
                    save_draft_btn = gr.Button("💾 Zapisz Szkic", variant="secondary")
                    load_template_btn = gr.Button("📄 Wczytaj Szablon", variant="secondary")

            with gr.Column(scale=3):
                # Quote preview and options
                with gr.Tabs():
                    with gr.Tab("👁️ Podgląd Oferty"):
                        quote_preview = gr.HTML(
                            label="Podgląd oferty",
                            value="<div class='metric-card'><p>Oferta zostanie wygenerowana po wypełnieniu formularza.</p></div>"
                        )

                    with gr.Tab("📊 Kalkulacja Kosztów"):
                        cost_breakdown = gr.Dataframe(
                            headers=["Pozycja", "Ilość", "Cena jednostkowa", "Wartość"],
                            datatype=["str", "number", "number", "number"],
                            label="Szczegółowa kalkulacja",
                            interactive=False
                        )

                        total_cost = gr.HTML(
                            value="<div class='metric-card'><h3>Koszt całkowity: 0 PLN</h3></div>"
                        )

                    with gr.Tab("🔧 Specyfikacja Techniczna"):
                        technical_specs = gr.Textbox(
                            label="Specyfikacja techniczna",
                            lines=10,
                            interactive=False,
                            placeholder="Specyfikacja techniczna zostanie wygenerowana..."
                        )

                    with gr.Tab("📄 Dokumenty"):
                        with gr.Row():
                            download_pdf = gr.File(
                                label="📄 Pobierz PDF",
                                visible=False
                            )
                            download_docx = gr.File(
                                label="📝 Pobierz DOCX",
                                visible=False
                            )

                        quote_status = gr.HTML(
                            value="<div class='metric-card'>Dokumenty będą dostępne po wygenerowaniu oferty</div>"
                        )

        # Event handlers
        generate_quote_btn.click(
            fn=self._generate_quote_enhanced,
            inputs=[
                customer_name, customer_company, customer_email, customer_phone, customer_address,
                room_count, total_area, ceiling_height, cooling_priority, heating_priority, budget_range,
                wifi_required, quiet_operation, air_purification, smart_features, energy_efficiency, quick_installation
            ],
            outputs=[quote_preview, cost_breakdown, total_cost, technical_specs, download_pdf, download_docx, quote_status]
        )

        ai_suggestions_btn.click(
            fn=self._get_ai_suggestions,
            inputs=[room_count, total_area, cooling_priority, heating_priority, budget_range],
            outputs=[ai_suggestions]
        )
    
    def _create_equipment_database_tab(self):
        """Create equipment database tab."""
        
        gr.Markdown("### Baza danych sprzętu HVAC")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Search and filters
                search_term = gr.Textbox(
                    label="Wyszukaj sprzęt",
                    placeholder="Wpisz model, producenta lub typ..."
                )
                
                manufacturer_filter = gr.Dropdown(
                    choices=["Wszystkie", "LG", "Daikin", "Mitsubishi", "Samsung"],
                    value="Wszystkie",
                    label="Producent"
                )
                
                equipment_type_filter = gr.Dropdown(
                    choices=["Wszystkie", "Split", "Multi-split", "VRF", "Kanałowe"],
                    value="Wszystkie",
                    label="Typ sprzętu"
                )
                
                search_btn = gr.Button("🔍 Szukaj", variant="primary")
                
                # Add new equipment
                with gr.Accordion("Dodaj nowy sprzęt", open=False):
                    new_manufacturer = gr.Textbox(label="Producent")
                    new_model = gr.Textbox(label="Model")
                    new_type = gr.Dropdown(
                        choices=["split_system", "multi_split", "vrf", "ducted"],
                        label="Typ"
                    )
                    new_cooling_capacity = gr.Number(label="Moc chłodnicza (kW)")
                    new_price = gr.Number(label="Cena (PLN)")
                    
                    add_equipment_btn = gr.Button("➕ Dodaj sprzęt")
            
            with gr.Column(scale=2):
                # Equipment list
                equipment_list = gr.Dataframe(
                    headers=["Producent", "Model", "Typ", "Moc (kW)", "Cena (PLN)", "Klasa energetyczna"],
                    datatype=["str", "str", "str", "number", "number", "str"],
                    label="Lista sprzętu",
                    interactive=False
                )
                
                # Equipment details
                equipment_details = gr.JSON(
                    label="Szczegóły sprzętu",
                    value={}
                )
        
        # Event handlers
        search_btn.click(
            fn=self._search_equipment,
            inputs=[search_term, manufacturer_filter, equipment_type_filter],
            outputs=[equipment_list]
        )
        
        add_equipment_btn.click(
            fn=self._add_equipment,
            inputs=[new_manufacturer, new_model, new_type, new_cooling_capacity, new_price],
            outputs=[equipment_list]
        )
    
    def _create_krabulon_tab(self):
        """Create Krabulon data enrichment tab."""
        
        gr.Markdown("### Krabulon - Automatyczne wzbogacanie bazy danych sprzętu")
        
        with gr.Row():
            with gr.Column(scale=1):
                # Manufacturer selection
                manufacturers_input = gr.CheckboxGroup(
                    choices=["LG", "Daikin", "Mitsubishi", "Samsung", "Panasonic", "Fujitsu"],
                    value=["LG", "Daikin"],
                    label="Wybierz producentów"
                )
                
                # Crawling options
                with gr.Accordion("Opcje crawlingu", open=False):
                    max_pages = gr.Slider(
                        minimum=10,
                        maximum=100,
                        value=50,
                        step=10,
                        label="Maksymalna liczba stron"
                    )
                    
                    crawl_delay = gr.Slider(
                        minimum=0.5,
                        maximum=5.0,
                        value=1.0,
                        step=0.5,
                        label="Opóźnienie między żądaniami (s)"
                    )
                
                start_enrichment_btn = gr.Button("🌐 Rozpocznij wzbogacanie", variant="primary")
                
                # Progress
                enrichment_progress = gr.Progress()
                
            with gr.Column(scale=1):
                # Status and results
                enrichment_status = gr.Textbox(
                    label="Status procesu",
                    value="Gotowy do rozpoczęcia",
                    interactive=False
                )
                
                enrichment_results = gr.JSON(
                    label="Wyniki wzbogacania",
                    value={}
                )
                
                # Statistics
                enrichment_stats = gr.HTML(
                    label="Statystyki",
                    value="<p>Brak danych</p>"
                )
        
        # Event handler
        start_enrichment_btn.click(
            fn=self._start_krabulon_enrichment,
            inputs=[manufacturers_input, max_pages, crawl_delay],
            outputs=[enrichment_status, enrichment_results, enrichment_stats]
        )
    
    def _create_system_management_tab(self):
        """Create system management tab."""
        
        gr.Markdown("### Zarządzanie systemem")
        
        with gr.Row():
            with gr.Column():
                # Database management
                gr.Markdown("#### Zarządzanie bazami danych")
                
                with gr.Row():
                    init_db_btn = gr.Button("🗄️ Inicjalizuj bazy danych")
                    backup_db_btn = gr.Button("💾 Backup baz danych")
                    restore_db_btn = gr.Button("🔄 Przywróć backup")
                
                # System health
                gr.Markdown("#### Zdrowie systemu")
                
                health_check_btn = gr.Button("🏥 Sprawdź zdrowie systemu")
                health_results = gr.JSON(label="Wyniki sprawdzenia")
                
                # Logs
                gr.Markdown("#### Logi systemu")
                
                log_level = gr.Dropdown(
                    choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                    value="INFO",
                    label="Poziom logów"
                )
                
                show_logs_btn = gr.Button("📋 Pokaż logi")
                logs_display = gr.Textbox(
                    label="Logi",
                    lines=10,
                    interactive=False
                )
        
        # Event handlers
        init_db_btn.click(fn=self._initialize_databases)
        health_check_btn.click(
            fn=self._check_system_health,
            outputs=[health_results]
        )
        show_logs_btn.click(
            fn=self._get_system_logs,
            inputs=[log_level],
            outputs=[logs_display]
        )

    def _initialize_databases(self):
        """Initialize databases."""
        try:
            success = self.hvac_system.initialize_databases()
            return "✅ Bazy danych zainicjalizowane pomyślnie" if success else "❌ Błąd inicjalizacji baz danych"
        except Exception as e:
            return f"❌ Błąd: {str(e)}"

    def _check_system_health(self) -> Dict[str, Any]:
        """Check system health."""
        try:
            status = self.hvac_system.get_system_status()
            health_data = {
                "timestamp": status["timestamp"],
                "overall_status": "healthy" if self.system_initialized else "degraded",
                "databases": status["databases"],
                "frameworks": status["frameworks"],
                "uptime": str(datetime.now() - self.last_refresh)
            }
            return health_data
        except Exception as e:
            return {"error": str(e), "status": "error"}

    def _get_system_logs(self, log_level: str) -> str:
        """Get system logs."""
        try:
            # Mock log data - in real implementation, read from log files
            logs = [
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {log_level}: System initialized",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Database connection established",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] INFO: Gradio interface started",
                f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] DEBUG: Framework analysis completed"
            ]
            return "\n".join(logs)
        except Exception as e:
            return f"Błąd pobierania logów: {str(e)}"

    def _search_equipment(self, search_term: str, manufacturer: str, equipment_type: str) -> pd.DataFrame:
        """Search equipment in database."""
        try:
            # Mock equipment data
            equipment_data = [
                ["LG", "S12ET", "Split", 3.5, 4500, "A+++"],
                ["LG", "S18ET", "Split", 5.3, 5200, "A+++"],
                ["Daikin", "FTXM25R", "Split", 2.5, 3800, "A++"],
                ["Daikin", "FTXM35R", "Split", 3.5, 4200, "A++"],
                ["Samsung", "AR12TXHQASINEU", "Split", 3.5, 3900, "A++"],
            ]

            df = pd.DataFrame(equipment_data, columns=["Producent", "Model", "Typ", "Moc (kW)", "Cena (PLN)", "Klasa energetyczna"])

            # Apply filters
            if manufacturer != "Wszystkie":
                df = df[df["Producent"] == manufacturer]
            if equipment_type != "Wszystkie":
                df = df[df["Typ"] == equipment_type]
            if search_term:
                df = df[df["Model"].str.contains(search_term, case=False, na=False)]

            return df
        except Exception as e:
            logger.error(f"Equipment search failed: {e}")
            return pd.DataFrame()

    def _add_equipment(self, manufacturer: str, model: str, equipment_type: str, cooling_capacity: float, price: float) -> pd.DataFrame:
        """Add new equipment to database."""
        try:
            if not all([manufacturer, model, equipment_type, cooling_capacity, price]):
                return pd.DataFrame()

            # In real implementation, add to database
            logger.info(f"Adding equipment: {manufacturer} {model}")

            # Return updated equipment list
            return self._search_equipment("", "Wszystkie", "Wszystkie")
        except Exception as e:
            logger.error(f"Add equipment failed: {e}")
            return pd.DataFrame()

    def _start_krabulon_enrichment(self, manufacturers: List[str], max_pages: int, crawl_delay: float) -> Tuple[str, Dict, str]:
        """Start Krabulon data enrichment."""
        try:
            if not manufacturers:
                return "Wybierz przynajmniej jednego producenta", {}, "<p>Brak danych</p>"

            # Mock enrichment process
            status = f"🔄 Rozpoczęto wzbogacanie dla: {', '.join(manufacturers)}"

            results = {
                "manufacturers_processed": len(manufacturers),
                "pages_crawled": max_pages,
                "products_found": max_pages * 5,  # Mock data
                "images_downloaded": max_pages * 3,
                "specifications_extracted": max_pages * 4
            }

            stats_html = f"""
            <div class='metric-card'>
                <h4>Statystyki wzbogacania:</h4>
                <ul>
                    <li>Producenci: {results['manufacturers_processed']}</li>
                    <li>Strony: {results['pages_crawled']}</li>
                    <li>Produkty: {results['products_found']}</li>
                    <li>Obrazy: {results['images_downloaded']}</li>
                    <li>Specyfikacje: {results['specifications_extracted']}</li>
                </ul>
            </div>
            """

            return "✅ Wzbogacanie zakończone pomyślnie", results, stats_html

        except Exception as e:
            logger.error(f"Krabulon enrichment failed: {e}")
            return f"❌ Błąd wzbogacania: {str(e)}", {}, "<p>Błąd</p>"

    def _generate_report(self, report_type: str, date_from: str, date_to: str) -> Tuple[go.Figure, pd.DataFrame]:
        """Generate analytics report."""
        try:
            # Mock report data
            if report_type == "Analiza emaili":
                # Create email analysis chart
                dates = pd.date_range(start=date_from, end=date_to, freq='D')
                email_counts = [5, 8, 12, 6, 9, 15, 11, 7, 13, 10][:len(dates)]

                fig = go.Figure()
                fig.add_trace(go.Scatter(x=dates, y=email_counts, mode='lines+markers', name='Liczba emaili'))
                fig.update_layout(title="Analiza emaili w czasie", xaxis_title="Data", yaxis_title="Liczba emaili")

                # Create data table
                data = [[str(date.date()), count, "Serwis" if count > 10 else "Oferta"]
                       for date, count in zip(dates, email_counts)]
                df = pd.DataFrame(data, columns=["Data", "Liczba emaili", "Typ dominujący"])

            else:
                # Default empty chart
                fig = go.Figure()
                fig.update_layout(title=f"Raport: {report_type}")
                df = pd.DataFrame()

            return fig, df

        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            return go.Figure(), pd.DataFrame()
    
    def _create_analytics_tab(self):
        """Create analytics and reports tab."""
        
        gr.Markdown("### Analityka i raporty")
        
        with gr.Row():
            with gr.Column():
                # Report type selection
                report_type = gr.Radio(
                    choices=[
                        "Analiza emaili",
                        "Statystyki ofert",
                        "Wydajność sprzętu",
                        "Trendy rynkowe"
                    ],
                    value="Analiza emaili",
                    label="Typ raportu"
                )
                
                # Date range
                date_from = gr.Textbox(
                    label="Data od (YYYY-MM-DD)",
                    value="2024-01-01"
                )
                date_to = gr.Textbox(
                    label="Data do (YYYY-MM-DD)",
                    value=datetime.now().strftime("%Y-%m-%d")
                )
                
                generate_report_btn = gr.Button("📊 Generuj raport", variant="primary")
            
            with gr.Column():
                # Report display
                report_chart = gr.Plot(label="Wykres")
                report_data = gr.Dataframe(label="Dane")
                
                # Export options
                export_csv_btn = gr.Button("📄 Eksportuj CSV")
                export_pdf_btn = gr.Button("📋 Eksportuj PDF")
        
        # Event handlers
        generate_report_btn.click(
            fn=self._generate_report,
            inputs=[report_type, date_from, date_to],
            outputs=[report_chart, report_data]
        )
    
    # Implementation methods
    def _get_system_status_html(self) -> str:
        """Get system status as HTML."""
        try:
            # Check system components
            db_status = "✅ Połączono" if db_manager.is_connected() else "❌ Rozłączono"
            krabulon_status = "✅ Aktywny" if self.krabulon_orchestrator else "❌ Nieaktywny"
            
            return f"""
            <div class="status-success">
                <h4>Status systemu: Operacyjny</h4>
                <ul>
                    <li>Baza danych: {db_status}</li>
                    <li>Krabulon: {krabulon_status}</li>
                    <li>Ostatnia aktualizacja: {datetime.now().strftime('%H:%M:%S')}</li>
                </ul>
            </div>
            """
        except Exception as e:
            return f"""
            <div class="status-error">
                <h4>Status systemu: Błąd</h4>
                <p>Błąd: {str(e)}</p>
            </div>
            """
    
    def _refresh_system_status(self) -> str:
        """Refresh system status."""
        return self._get_system_status_html()
    
    def _analyze_email(
        self,
        email_content: str,
        framework: str,
        include_sentiment: bool,
        include_entities: bool,
        include_intent: bool
    ) -> Tuple[Dict, str, str]:
        """Analyze email content."""
        try:
            if not email_content.strip():
                return {}, "Brak treści do analizy", "Wprowadź treść emaila"
            
            # Run analysis based on selected framework
            framework_map = {
                "LangGraph": "langgraph",
                "CrewAI": "crewai",
                "OpenAI Swarm": "swarm"
            }
            
            result = self.hvac_system.run_framework(framework_map[framework])
            
            # Extract insights
            analysis_result = result.get("result", {})
            
            # Generate customer insights
            insights = self._extract_customer_insights(analysis_result)
            
            # Generate recommendations
            recommendations = self._extract_recommendations(analysis_result)
            
            return analysis_result, insights, recommendations
            
        except Exception as e:
            logger.error(f"Email analysis failed: {e}")
            return {"error": str(e)}, "Błąd analizy", "Spróbuj ponownie"
    
    def _extract_customer_insights(self, analysis_result: Dict) -> str:
        """Extract customer insights from analysis."""
        # Implementation would extract key insights
        return "Klient wykazuje zainteresowanie systemem klimatyzacji dla domu jednorodzinnego."
    
    def _extract_recommendations(self, analysis_result: Dict) -> str:
        """Extract recommendations from analysis."""
        # Implementation would generate recommendations
        return "Zaproponuj system split LG z funkcją WiFi."

    # New enhanced methods
    def _create_empty_metrics_plot(self):
        """Create empty metrics plot."""
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=[], y=[], mode='markers', name='Metryki'))
        fig.update_layout(
            title="Metryki analizy emaili",
            xaxis_title="Czas",
            yaxis_title="Wartość",
            template="plotly_white"
        )
        return fig

    def _get_email_template(self, template_type: str) -> str:
        """Get email template by type."""
        templates = {
            "service": """From: <EMAIL>
To: <EMAIL>
Subject: Awaria klimatyzacji LG - pilne
Date: Mon, 1 Jan 2024 10:00:00 +0100

Dzień dobry,

Mam problem z klimatyzacją LG model S12ET w biurze.
Urządzenie nie chłodzi i wyświetla błąd E1.
Proszę o pilny kontakt w sprawie serwisu.

Lokalizacja: Warszawa, ul. Testowa 123
Telefon: +48 ***********

Pozdrawiam,
Jan Kowalski""",

            "quote": """From: <EMAIL>
To: <EMAIL>
Subject: Zapytanie o ofertę - klimatyzacja biura
Date: Mon, 1 Jan 2024 14:00:00 +0100

Dzień dobry,

Prosimy o przygotowanie oferty na klimatyzację biura:
- Powierzchnia: 120 m²
- 4 pomieszczenia
- Budynek biurowy w centrum Warszawy
- Preferujemy systemy LG lub Daikin
- Budżet do 25 000 PLN

Proszę o kontakt w celu umówienia wizji lokalnej.

Pozdrawiam,
Anna Nowak
Kierownik Administracji""",

            "complaint": """From: <EMAIL>
To: <EMAIL>
Subject: Reklamacja - wadliwa instalacja klimatyzacji
Date: Mon, 1 Jan 2024 16:00:00 +0100

Dzień dobry,

Zgłaszam reklamację instalacji klimatyzacji wykonanej 2 tygodnie temu.
Problemy:
- Głośna praca jednostki zewnętrznej
- Nierównomierne chłodzenie pomieszczeń
- Przecieki z jednostki wewnętrznej

Instalacja: LG Dual Cool S18ET
Adres: ul. Domowa 456, Warszawa
Data instalacji: 15.12.2023

Proszę o pilny kontakt i naprawę.

Piotr Kowalczyk
Tel: +48 987 654 321"""
        }
        return templates.get(template_type, "")

    def _analyze_email_enhanced(
        self,
        email_content: str,
        framework: str,
        include_sentiment: bool,
        include_entities: bool,
        include_intent: bool,
        include_priority: bool
    ) -> Tuple[str, Dict, str, str, go.Figure]:
        """Enhanced email analysis with visualization."""
        try:
            if not email_content.strip():
                return (
                    "<div class='status-error'>Brak treści do analizy</div>",
                    {},
                    "Wprowadź treść emaila",
                    "Brak rekomendacji",
                    self._create_empty_metrics_plot()
                )

            # Update status
            status_html = "<div class='metric-card'>🔄 Analizuję email...</div>"

            # Simulate analysis delay
            time.sleep(1)

            # Run framework analysis
            framework_map = {
                "LangGraph": "langgraph",
                "CrewAI": "crewai",
                "OpenAI Swarm": "swarm"
            }

            result = self.hvac_system.run_framework(framework_map[framework])

            # Create mock analysis results
            analysis_result = {
                "email_type": "service_request",
                "priority": "high",
                "sentiment": "negative",
                "entities": ["LG", "S12ET", "błąd E1", "Warszawa"],
                "confidence": 0.89,
                "processing_time": 2.3,
                "framework_used": framework
            }

            # Generate insights and recommendations
            insights = f"""
🎯 Typ emaila: Zgłoszenie serwisowe
😟 Sentyment: Negatywny (klient zaniepokojony)
⚡ Priorytet: Wysoki (awaria urządzenia)
🏷️ Rozpoznane encje: {', '.join(analysis_result['entities'])}
📍 Lokalizacja: Warszawa
📞 Kontakt: +48 ***********
            """.strip()

            recommendations = f"""
🔧 Natychmiastowe działania:
• Skontaktować się z klientem w ciągu 2h
• Zaplanować wizytę serwisową na jutro
• Przygotować części zamienne dla LG S12ET

📋 Dalsze kroki:
• Sprawdzić gwarancję urządzenia
• Przygotować raport serwisowy
• Zaproponować umowę serwisową
            """.strip()

            # Create metrics visualization
            metrics_fig = self._create_analysis_metrics_plot(analysis_result)

            status_html = f"""
            <div class='status-success'>
                ✅ Analiza zakończona pomyślnie<br>
                🤖 Framework: {framework}<br>
                ⏱️ Czas: {analysis_result['processing_time']}s<br>
                🎯 Pewność: {analysis_result['confidence']*100:.1f}%
            </div>
            """

            return status_html, analysis_result, insights, recommendations, metrics_fig

        except Exception as e:
            logger.error(f"Enhanced email analysis failed: {e}")
            error_status = f"<div class='status-error'>❌ Błąd analizy: {str(e)}</div>"
            return error_status, {"error": str(e)}, "Błąd analizy", "Spróbuj ponownie", self._create_empty_metrics_plot()

    def _create_analysis_metrics_plot(self, analysis_result: Dict) -> go.Figure:
        """Create analysis metrics visualization."""
        categories = ['Sentiment', 'Priority', 'Confidence', 'Entities']
        values = [0.7, 0.9, analysis_result.get('confidence', 0.8), 0.85]

        fig = go.Figure(data=[
            go.Bar(x=categories, y=values, marker_color=['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4'])
        ])

        fig.update_layout(
            title="Metryki Analizy Emaila",
            yaxis_title="Wartość",
            template="plotly_white",
            height=400
        )

        return fig

    def _generate_quote_enhanced(self, *args) -> Tuple[str, pd.DataFrame, str, str, str, str, str]:
        """Enhanced quote generation with detailed breakdown."""
        try:
            # Extract arguments
            (customer_name, customer_company, customer_email, customer_phone, customer_address,
             room_count, total_area, ceiling_height, cooling_priority, heating_priority, budget_range,
             wifi_required, quiet_operation, air_purification, smart_features, energy_efficiency, quick_installation) = args

            if not customer_name or not customer_email:
                return (
                    "<div class='status-error'>Wypełnij wymagane pola: Imię i Email</div>",
                    pd.DataFrame(),
                    "<div class='metric-card'><h3>Koszt całkowity: 0 PLN</h3></div>",
                    "Brak specyfikacji",
                    None, None,
                    "<div class='status-error'>Nie można wygenerować oferty</div>"
                )

            # Calculate equipment based on requirements
            base_cost = self._calculate_base_cost(total_area, room_count, budget_range)

            # Create cost breakdown
            cost_data = [
                ["Jednostka zewnętrzna LG S18ET", 1, 3500, 3500],
                ["Jednostka wewnętrzna LG S18ET", room_count, 1200, 1200 * room_count],
                ["Instalacja i montaż", 1, 2000, 2000],
                ["Materiały instalacyjne", 1, 800, 800],
            ]

            # Add optional features
            if wifi_required:
                cost_data.append(["Moduł WiFi", 1, 300, 300])
            if air_purification:
                cost_data.append(["Filtr oczyszczający", room_count, 150, 150 * room_count])

            cost_df = pd.DataFrame(cost_data, columns=["Pozycja", "Ilość", "Cena jednostkowa", "Wartość"])
            total_value = cost_df["Wartość"].sum()

            # Generate quote preview
            quote_html = f"""
            <div class='metric-card'>
                <h2>📋 Oferta HVAC - {customer_name}</h2>
                <hr>
                <p><strong>Klient:</strong> {customer_name}</p>
                <p><strong>Email:</strong> {customer_email}</p>
                <p><strong>Adres:</strong> {customer_address}</p>
                <hr>
                <h3>Specyfikacja systemu:</h3>
                <ul>
                    <li>Powierzchnia: {total_area} m²</li>
                    <li>Liczba pomieszczeń: {room_count}</li>
                    <li>System: LG Dual Cool</li>
                    <li>Moc chłodnicza: {total_area * 0.1:.1f} kW</li>
                </ul>
                <hr>
                <h3 style='color: #2d5aa0;'>Wartość całkowita: {total_value:,.0f} PLN</h3>
            </div>
            """

            total_cost_html = f"<div class='metric-card'><h3>Koszt całkowity: {total_value:,.0f} PLN</h3></div>"

            # Technical specifications
            tech_specs = f"""
Specyfikacja Techniczna Systemu HVAC

JEDNOSTKA ZEWNĘTRZNA:
• Model: LG S18ET Dual Cool
• Moc chłodnicza: {total_area * 0.1:.1f} kW
• Moc grzewcza: {total_area * 0.12:.1f} kW
• Klasa energetyczna: A+++
• Czynnik chłodniczy: R32
• Poziom hałasu: 58 dB(A)

JEDNOSTKI WEWNĘTRZNE:
• Ilość: {room_count} szt.
• Model: LG Standard Plus
• Sterowanie: Pilot + {'WiFi' if wifi_required else 'Standardowe'}
• Filtry: {'Oczyszczające PM2.5' if air_purification else 'Standardowe'}

INSTALACJA:
• Długość przewodów: do 15m
• Materiały: Rury miedziane, izolacja, kable
• Czas instalacji: {'1 dzień' if quick_installation else '2 dni'}
• Gwarancja: 5 lat na urządzenia, 2 lata na instalację

DODATKOWE FUNKCJE:
{f'• Sterowanie WiFi przez aplikację LG ThinQ' if wifi_required else ''}
{f'• Filtracja powietrza PM2.5' if air_purification else ''}
{f'• Tryb cichy (poniżej 19 dB)' if quiet_operation else ''}
{f'• Funkcje smart home' if smart_features else ''}
            """.strip()

            status_html = "<div class='status-success'>✅ Oferta wygenerowana pomyślnie</div>"

            return quote_html, cost_df, total_cost_html, tech_specs, None, None, status_html

        except Exception as e:
            logger.error(f"Quote generation failed: {e}")
            error_status = f"<div class='status-error'>❌ Błąd generowania oferty: {str(e)}</div>"
            return error_status, pd.DataFrame(), "", "", None, None, error_status

    def _calculate_base_cost(self, area: float, rooms: int, budget: str) -> float:
        """Calculate base cost for HVAC system."""
        base_per_m2 = 200  # Base cost per m²

        # Adjust based on budget range
        if "Premium" in budget:
            multiplier = 1.5
        elif "Standard" in budget:
            multiplier = 1.0
        else:  # Budget
            multiplier = 0.7

        return area * base_per_m2 * multiplier

    def _get_ai_suggestions(self, room_count: int, total_area: float, cooling_priority: str, heating_priority: str, budget_range: str) -> str:
        """Get AI-powered suggestions for HVAC system."""
        try:
            # Simple rule-based suggestions (could be enhanced with actual AI)
            suggestions = []

            # Area-based suggestions
            if total_area > 100:
                suggestions.append("🏢 Dla dużej powierzchni rekomendujemy system VRF")
            elif total_area < 30:
                suggestions.append("🏠 Dla małej powierzchni wystarczy system split")

            # Room count suggestions
            if room_count > 3:
                suggestions.append("🏘️ Multi-split będzie bardziej efektywny niż pojedyncze jednostki")

            # Priority suggestions
            if cooling_priority == "Wysokie":
                suggestions.append("❄️ Rekomendujemy jednostki o wysokiej mocy chłodniczej")
            if heating_priority == "Wysokie":
                suggestions.append("🔥 Sugerujemy pompy ciepła z funkcją grzania")

            # Budget suggestions
            if "Premium" in budget_range:
                suggestions.append("💎 Możemy zaproponować systemy premium z zaawansowanymi funkcjami")
            elif "Budżetowe" in budget_range:
                suggestions.append("💰 Skupimy się na rozwiązaniach ekonomicznych o dobrej jakości")

            return "\n".join(f"• {suggestion}" for suggestion in suggestions)

        except Exception as e:
            return f"Błąd generowania sugestii: {str(e)}"


def launch_interface():
    """Launch the Gradio interface."""
    interface_manager = HVACGradioInterface()
    interface = interface_manager.create_interface()
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )


if __name__ == "__main__":
    launch_interface()
