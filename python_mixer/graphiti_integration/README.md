# 🔧 HVAC Graphiti Knowledge Graph Integration

## Overview

This integration brings the power of **Zep AI's Graphiti** temporal knowledge graph to your HVAC CRM system, creating an intelligent memory layer that continuously learns from customer interactions, equipment data, and service patterns.

### 🌟 Key Features

- **Temporal Knowledge Tracking**: Maintains time-aware relationships between customers, equipment, and service events
- **AI-Powered Insights**: Uses Pydantic AI agents to provide intelligent recommendations
- **Email Analysis Integration**: Automatically extracts knowledge from customer emails
- **Real-time Updates**: Continuously updates the knowledge graph with new information
- **Multi-modal Search**: Combines semantic, keyword, and graph-based search capabilities

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gradio UI     │    │   FastAPI       │    │   Neo4j         │
│   Port 7860     │◄──►│   Port 8001     │◄──►│   Port 7687     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └─────────────►│  Graphiti Core  │◄─────────────┘
                        │  Knowledge      │
                        │  Graph Engine   │
                        └─────────────────┘
                                 │
                        ┌─────────────────┐
                        │  Email Analysis │
                        │  Pipeline       │
                        └─────────────────┘
```

## 🚀 Quick Start

### 1. Environment Setup

Create a `.env` file with your configuration:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key
MODEL_CHOICE=gpt-4o-mini

# Neo4j Configuration
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti_password

# Remote Database Configuration
POSTGRES_HOST=**************
POSTGRES_PASSWORD=your_postgres_password
MONGODB_HOST=**************
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1
```

### 2. Start the System

```bash
# Option 1: Use the orchestrator (recommended)
python start_graphiti_system.py

# Option 2: Manual startup
docker-compose -f docker-compose.graphiti.yml up -d
python -m uvicorn graphiti_integration.api:app --host 0.0.0.0 --port 8001
python graphiti_integration/gradio_graphiti_interface.py
```

### 3. Access the Interfaces

- **Gradio Interface**: http://localhost:7860
- **API Documentation**: http://localhost:8001/docs
- **Neo4j Browser**: http://localhost:7474

## 📊 Components

### 1. HVAC Graphiti Agent (`hvac_graphiti_agent.py`)

The core AI agent that provides intelligent responses based on the knowledge graph:

```python
from graphiti_integration.hvac_graphiti_agent import hvac_agent, initialize_graphiti

# Initialize the knowledge graph
graphiti = await initialize_graphiti()

# Query the agent
result = await hvac_agent.run("What equipment needs maintenance?", deps=deps)
```

**Capabilities:**
- Equipment health analysis
- Customer service history tracking
- Maintenance pattern recognition
- Predictive recommendations

### 2. Email-to-Knowledge Pipeline (`email_to_graphiti.py`)

Automatically processes email analysis results and populates the knowledge graph:

```python
from graphiti_integration.email_to_graphiti import HVACKnowledgeExtractor

extractor = HVACKnowledgeExtractor(graphiti_client)
episodes = await extractor.process_email_analysis(email_result)
```

**Features:**
- Customer information extraction
- Equipment mention detection
- Service request classification
- Maintenance discussion analysis

### 3. FastAPI Server (`api.py`)

RESTful API for programmatic access to the knowledge graph:

```bash
# Query the knowledge graph
curl -X POST "http://localhost:8001/query" \
  -H "Content-Type: application/json" \
  -d '{"query": "LG S12ET maintenance history", "limit": 10}'

# Process emails
curl -X POST "http://localhost:8001/emails/process" \
  -H "Content-Type: application/json" \
  -d '{"emails": [...]}'
```

### 4. Gradio Interface (`gradio_graphiti_interface.py`)

User-friendly web interface with multiple tabs:

- **Agent Chat**: Interactive conversation with the HVAC agent
- **Knowledge Search**: Direct search of the knowledge graph
- **Email Processing**: Manual email analysis and knowledge extraction
- **Analytics**: Visualizations and insights

## 🔍 Usage Examples

### Customer Service Scenario

```python
# Agent query about customer equipment
query = "Show me all equipment <NAME_EMAIL> and their service history"
result = await hvac_agent.run(query, deps=deps)
```

### Maintenance Planning

```python
# Find equipment needing maintenance
query = "Which LG units are due for maintenance based on installation dates?"
result = await hvac_agent.run(query, deps=deps)
```

### Email Processing

```python
# Process customer email
email_result = EmailAnalysisResult(
    email_id="cust_001",
    sender="<EMAIL>",
    subject="AC not cooling properly",
    content="My LG S12ET installed last year is not cooling...",
    intent="service_request",
    priority="high"
)

episodes = await extractor.process_email_analysis(email_result)
```

## 🛠️ Development

### Adding New Knowledge Types

1. **Define Data Models** in `hvac_graphiti_agent.py`:
```python
class NewEquipmentType(BaseModel):
    equipment_id: str
    special_field: str
```

2. **Create Search Tools**:
```python
@hvac_agent.tool
async def search_new_equipment_type(ctx, query: str):
    # Implementation
    pass
```

3. **Update Email Extractor** in `email_to_graphiti.py`:
```python
async def _extract_new_info(self, email_result):
    # Extract new information type
    pass
```

### Custom Visualizations

Add new plots to `gradio_graphiti_interface.py`:

```python
def create_custom_dashboard():
    # Create custom Plotly visualization
    fig = px.scatter(data, x="date", y="value")
    return fig
```

## 🔧 Configuration

### Neo4j Optimization

The Docker Compose configuration includes optimized Neo4j settings:

```yaml
environment:
  - NEO4J_dbms_memory_heap_max_size=2G
  - NEO4J_dbms_memory_pagecache_size=1G
  - NEO4J_PLUGINS=["apoc", "graph-data-science"]
```

### Graphiti Settings

Configure Graphiti behavior in your environment:

```bash
# Model selection
MODEL_CHOICE=gpt-4o-mini  # or gpt-4, claude-3-sonnet

# Search parameters
GRAPHITI_SEARCH_LIMIT=20
GRAPHITI_RERANK_ENABLED=true
```

## 📈 Monitoring

### Health Checks

```bash
# Check API health
curl http://localhost:8001/health

# Check Neo4j connection
curl http://localhost:7474/db/data/
```

### Logs

Monitor system logs:

```bash
# API logs
docker logs hvac-python-mixer-graphiti

# Neo4j logs
docker logs hvac-neo4j-graphiti
```

## 🚨 Troubleshooting

### Common Issues

1. **Neo4j Connection Failed**
   ```bash
   # Check if Neo4j is running
   docker ps | grep neo4j
   
   # Check logs
   docker logs hvac-neo4j-graphiti
   ```

2. **Graphiti Initialization Error**
   ```bash
   # Verify environment variables
   echo $NEO4J_URI $NEO4J_PASSWORD
   
   # Test connection manually
   python -c "from graphiti_integration.hvac_graphiti_agent import initialize_graphiti; import asyncio; asyncio.run(initialize_graphiti())"
   ```

3. **Memory Issues**
   ```bash
   # Increase Neo4j memory limits in docker-compose.graphiti.yml
   NEO4J_dbms_memory_heap_max_size=4G
   ```

## 🤝 Integration with Existing System

### Email Pipeline Integration

Connect with your existing email processing:

```python
from email_processing.email_parser import EmailParser
from graphiti_integration.email_to_graphiti import HVACKnowledgeExtractor

# Process emails and update knowledge graph
parser = EmailParser()
extractor = HVACKnowledgeExtractor(graphiti_client)

for email in emails:
    analysis = parser.analyze_email(email)
    await extractor.process_email_analysis(analysis)
```

### Database Integration

Sync with PostgreSQL and MongoDB:

```python
# Sync customer data
customer_data = fetch_from_postgres()
await add_hvac_episode(graphiti, customer_data, "postgres_sync")

# Sync equipment data
equipment_data = fetch_from_mongodb()
await add_hvac_episode(graphiti, equipment_data, "mongodb_sync")
```

## 📚 Resources

- [Graphiti Documentation](https://help.getzep.com/graphiti/)
- [Pydantic AI Documentation](https://ai.pydantic.dev/)
- [Neo4j Documentation](https://neo4j.com/docs/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)

## 🎯 Next Steps

1. **Enhanced NLP**: Integrate advanced NLP models for better entity extraction
2. **Predictive Analytics**: Build ML models using temporal patterns
3. **Mobile Interface**: Create mobile-friendly interface
4. **Voice Integration**: Add voice query capabilities
5. **Advanced Visualizations**: Create 3D knowledge graph visualizations

---

**🔧 HVAC Graphiti Integration** | Powered by Zep AI Graphiti & Pydantic AI
