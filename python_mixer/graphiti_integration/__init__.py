"""
HVAC Graphiti Knowledge Graph Integration Package

This package provides temporal knowledge graph capabilities for the HVAC CRM system
using Zep AI's Graphiti framework with Neo4j backend.

Components:
- hvac_graphiti_agent: AI agent for HVAC knowledge queries
- email_to_graphiti: Email analysis to knowledge graph pipeline
- api: FastAPI server for REST access
- gradio_graphiti_interface: Web UI for knowledge graph interaction
"""

__version__ = "1.0.0"
__author__ = "HVAC CRM Team"
__description__ = "Temporal Knowledge Graph for HVAC CRM System"

# Import main components for easy access
from .hvac_graphiti_agent import (
    hvac_agent,
    initialize_graphiti,
    HVACGraphitiDependencies,
    add_hvac_episode
)

from .email_to_graphiti import (
    EmailAnalysisResult,
    HVACKnowledgeExtractor,
    process_email_batch,
    initialize_hvac_knowledge_graph
)

__all__ = [
    "hvac_agent",
    "initialize_graphiti", 
    "HVACGraphitiDependencies",
    "add_hvac_episode",
    "EmailAnalysisResult",
    "HVACKnowledgeExtractor", 
    "process_email_batch",
    "initialize_hvac_knowledge_graph"
]
