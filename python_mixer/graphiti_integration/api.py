"""
FastAPI application for HVAC Graphiti Agent
Provides REST API endpoints for the temporal knowledge graph.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import asyncio
import json
import logging
from datetime import datetime, timezone
import os

from .hvac_graphiti_agent import (
    initialize_graphiti, 
    hvac_agent, 
    HVACGraphitiDependencies,
    add_hvac_episode
)
from .email_to_graphiti import (
    EmailAnalysisResult,
    HVACKnowledgeExtractor,
    process_email_batch,
    initialize_hvac_knowledge_graph
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(
    title="HVAC Graphiti Knowledge Graph API",
    description="Temporal knowledge graph for HVAC CRM system",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
graphiti_client = None
knowledge_extractor = None

# ========== Request/Response Models ==========

class QueryRequest(BaseModel):
    """Request model for knowledge graph queries."""
    query: str = Field(description="Natural language query")
    limit: Optional[int] = Field(10, description="Maximum number of results")
    include_context: Optional[bool] = Field(True, description="Include contextual information")

class QueryResponse(BaseModel):
    """Response model for knowledge graph queries."""
    query: str
    results: List[Dict[str, Any]]
    total_results: int
    processing_time: float

class EpisodeRequest(BaseModel):
    """Request model for adding episodes."""
    name: str = Field(description="Episode name")
    episode_body: str = Field(description="Episode content")
    source: str = Field(description="Source identifier")
    source_description: Optional[str] = Field(None, description="Source description")

class EmailBatchRequest(BaseModel):
    """Request model for processing email batches."""
    emails: List[EmailAnalysisResult]

class HealthResponse(BaseModel):
    """Health check response."""
    status: str
    timestamp: datetime
    neo4j_connected: bool
    graphiti_initialized: bool

# ========== Startup/Shutdown Events ==========

@app.on_event("startup")
async def startup_event():
    """Initialize the application."""
    global graphiti_client, knowledge_extractor
    
    try:
        logger.info("Initializing HVAC Graphiti API...")
        
        # Initialize Graphiti client
        graphiti_client = await initialize_graphiti()
        logger.info("✅ Graphiti client initialized")
        
        # Initialize knowledge extractor
        knowledge_extractor = HVACKnowledgeExtractor(graphiti_client)
        logger.info("✅ Knowledge extractor initialized")
        
        # Initialize knowledge graph structure
        await initialize_hvac_knowledge_graph(graphiti_client)
        logger.info("✅ HVAC knowledge graph structure initialized")
        
        logger.info("🚀 HVAC Graphiti API ready!")
        
    except Exception as e:
        logger.error(f"❌ Failed to initialize API: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down HVAC Graphiti API...")

# ========== Dependency Injection ==========

async def get_graphiti_deps():
    """Get Graphiti dependencies."""
    if not graphiti_client:
        raise HTTPException(status_code=503, detail="Graphiti client not initialized")
    
    from rich.console import Console
    return HVACGraphitiDependencies(
        graphiti_client=graphiti_client,
        console=Console()
    )

# ========== API Endpoints ==========

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    neo4j_connected = False
    graphiti_initialized = False
    
    try:
        if graphiti_client:
            # Test Neo4j connection
            await graphiti_client.search("test", limit=1)
            neo4j_connected = True
            graphiti_initialized = True
    except Exception as e:
        logger.warning(f"Health check failed: {e}")
    
    return HealthResponse(
        status="healthy" if neo4j_connected else "unhealthy",
        timestamp=datetime.now(timezone.utc),
        neo4j_connected=neo4j_connected,
        graphiti_initialized=graphiti_initialized
    )

@app.post("/query", response_model=QueryResponse)
async def query_knowledge_graph(
    request: QueryRequest,
    deps: HVACGraphitiDependencies = Depends(get_graphiti_deps)
):
    """Query the knowledge graph."""
    start_time = datetime.now()
    
    try:
        # Run the HVAC agent
        result = await hvac_agent.run(request.query, deps=deps)
        
        # Search the knowledge graph directly for additional context
        search_results = await graphiti_client.search(
            query=request.query,
            limit=request.limit
        )
        
        # Format results
        formatted_results = []
        for result_item in search_results:
            formatted_results.append({
                "fact": result_item.fact,
                "valid_at": result_item.valid_at,
                "invalid_at": result_item.invalid_at,
                "uuid": result_item.uuid,
                "score": getattr(result_item, 'score', 0.0)
            })
        
        processing_time = (datetime.now() - start_time).total_seconds()
        
        return QueryResponse(
            query=request.query,
            results=formatted_results,
            total_results=len(formatted_results),
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Error querying knowledge graph: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/agent/chat")
async def chat_with_agent(
    request: QueryRequest,
    deps: HVACGraphitiDependencies = Depends(get_graphiti_deps)
):
    """Chat with the HVAC agent."""
    try:
        result = await hvac_agent.run(request.query, deps=deps)
        
        return {
            "query": request.query,
            "response": result.data,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/episodes")
async def add_episode(request: EpisodeRequest):
    """Add an episode to the knowledge graph."""
    try:
        await graphiti_client.add_episode(
            name=request.name,
            episode_body=request.episode_body,
            source=request.source,
            source_description=request.source_description
        )
        
        return {
            "message": "Episode added successfully",
            "episode_name": request.name,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error adding episode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/emails/process")
async def process_emails(
    request: EmailBatchRequest,
    background_tasks: BackgroundTasks
):
    """Process a batch of email analysis results."""
    try:
        if not knowledge_extractor:
            raise HTTPException(status_code=503, detail="Knowledge extractor not initialized")
        
        # Process emails in background
        background_tasks.add_task(
            process_email_batch,
            graphiti_client,
            request.emails
        )
        
        return {
            "message": f"Processing {len(request.emails)} emails",
            "email_count": len(request.emails),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error processing emails: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats")
async def get_knowledge_graph_stats():
    """Get knowledge graph statistics."""
    try:
        # This would require custom Cypher queries to Neo4j
        # For now, return basic stats
        return {
            "status": "active",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "message": "Knowledge graph statistics endpoint - implementation pending"
        }
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/search/{query}")
async def search_knowledge_graph(
    query: str,
    limit: int = 10
):
    """Simple search endpoint."""
    try:
        results = await graphiti_client.search(query=query, limit=limit)
        
        formatted_results = []
        for result in results:
            formatted_results.append({
                "fact": result.fact,
                "valid_at": result.valid_at,
                "invalid_at": result.invalid_at,
                "uuid": result.uuid
            })
        
        return {
            "query": query,
            "results": formatted_results,
            "count": len(formatted_results)
        }
        
    except Exception as e:
        logger.error(f"Error searching: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ========== WebSocket for Real-time Updates ==========

@app.websocket("/ws/agent")
async def websocket_agent_chat(websocket):
    """WebSocket endpoint for real-time agent chat."""
    await websocket.accept()
    
    try:
        deps = await get_graphiti_deps()
        
        while True:
            # Receive message
            data = await websocket.receive_text()
            query_data = json.loads(data)
            
            # Process with agent
            result = await hvac_agent.run(query_data["query"], deps=deps)
            
            # Send response
            response = {
                "type": "agent_response",
                "query": query_data["query"],
                "response": result.data,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            await websocket.send_text(json.dumps(response))
            
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
