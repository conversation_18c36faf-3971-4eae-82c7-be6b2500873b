"""
Email to Graphiti Integration Module
Processes email analysis results and populates the temporal knowledge graph.
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

from graphiti_core import Graphiti
from pydantic import BaseModel, Field

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# ========== Data Models ==========

class EmailAnalysisResult(BaseModel):
    """Model for email analysis results."""
    email_id: str = Field(description="Unique email identifier")
    sender: str = Field(description="Email sender")
    recipient: str = Field(description="Email recipient")
    subject: str = Field(description="Email subject")
    timestamp: datetime = Field(description="Email timestamp")
    content: str = Field(description="Email content")
    analysis: Dict[str, Any] = Field(description="AI analysis results")
    entities: List[Dict[str, str]] = Field(default_factory=list, description="Extracted entities")
    intent: Optional[str] = Field(None, description="Detected intent")
    sentiment: Optional[str] = Field(None, description="Sentiment analysis")
    priority: Optional[str] = Field(None, description="Priority level")

class HVACKnowledgeExtractor:
    """Extracts HVAC-specific knowledge from email analysis results."""
    
    def __init__(self, graphiti_client: Graphiti):
        self.graphiti = graphiti_client
    
    async def process_email_analysis(self, email_result: EmailAnalysisResult) -> List[Dict[str, Any]]:
        """Process email analysis and extract knowledge episodes."""
        episodes = []
        
        try:
            # Extract customer information
            customer_episode = await self._extract_customer_info(email_result)
            if customer_episode:
                episodes.append(customer_episode)
            
            # Extract equipment information
            equipment_episodes = await self._extract_equipment_info(email_result)
            episodes.extend(equipment_episodes)
            
            # Extract service requests
            service_episode = await self._extract_service_info(email_result)
            if service_episode:
                episodes.append(service_episode)
            
            # Extract maintenance information
            maintenance_episode = await self._extract_maintenance_info(email_result)
            if maintenance_episode:
                episodes.append(maintenance_episode)
            
            # Add all episodes to knowledge graph
            for episode in episodes:
                await self._add_episode_to_graph(episode)
            
            logger.info(f"Processed email {email_result.email_id}: {len(episodes)} episodes added")
            return episodes
            
        except Exception as e:
            logger.error(f"Error processing email {email_result.email_id}: {e}")
            return []
    
    async def _extract_customer_info(self, email_result: EmailAnalysisResult) -> Optional[Dict[str, Any]]:
        """Extract customer information from email."""
        try:
            # Look for customer entities in the analysis
            customer_entities = [
                entity for entity in email_result.entities 
                if entity.get('type') in ['PERSON', 'CUSTOMER', 'CLIENT']
            ]
            
            if not customer_entities:
                return None
            
            customer_data = {
                "type": "customer_contact",
                "timestamp": email_result.timestamp.isoformat(),
                "customer_email": email_result.sender,
                "contact_method": "email",
                "subject": email_result.subject,
                "entities": customer_entities,
                "sentiment": email_result.sentiment,
                "priority": email_result.priority
            }
            
            # Extract phone numbers if present
            phone_entities = [
                entity for entity in email_result.entities 
                if entity.get('type') == 'PHONE'
            ]
            if phone_entities:
                customer_data["phone_numbers"] = [entity.get('value') for entity in phone_entities]
            
            # Extract addresses if present
            address_entities = [
                entity for entity in email_result.entities 
                if entity.get('type') in ['ADDRESS', 'LOCATION']
            ]
            if address_entities:
                customer_data["addresses"] = [entity.get('value') for entity in address_entities]
            
            return {
                "name": f"customer_contact_{email_result.email_id}",
                "episode_body": json.dumps(customer_data),
                "source": "email_analysis",
                "source_description": f"Customer contact via email from {email_result.sender}"
            }
            
        except Exception as e:
            logger.error(f"Error extracting customer info: {e}")
            return None
    
    async def _extract_equipment_info(self, email_result: EmailAnalysisResult) -> List[Dict[str, Any]]:
        """Extract equipment information from email."""
        episodes = []
        
        try:
            # Look for equipment-related entities
            equipment_entities = [
                entity for entity in email_result.entities 
                if entity.get('type') in ['EQUIPMENT', 'MODEL', 'HVAC_UNIT']
            ]
            
            # Also search content for common HVAC models
            hvac_models = self._find_hvac_models_in_content(email_result.content)
            
            for model in hvac_models:
                equipment_data = {
                    "type": "equipment_mention",
                    "timestamp": email_result.timestamp.isoformat(),
                    "model": model,
                    "context": "email_discussion",
                    "customer_email": email_result.sender,
                    "email_subject": email_result.subject,
                    "intent": email_result.intent
                }
                
                episodes.append({
                    "name": f"equipment_mention_{model}_{email_result.email_id}",
                    "episode_body": json.dumps(equipment_data),
                    "source": "email_analysis",
                    "source_description": f"Equipment {model} mentioned in email from {email_result.sender}"
                })
            
            return episodes
            
        except Exception as e:
            logger.error(f"Error extracting equipment info: {e}")
            return []
    
    async def _extract_service_info(self, email_result: EmailAnalysisResult) -> Optional[Dict[str, Any]]:
        """Extract service request information from email."""
        try:
            # Check if this is a service-related email
            service_keywords = ['repair', 'service', 'maintenance', 'problem', 'issue', 'broken', 'not working']
            content_lower = email_result.content.lower()
            
            if not any(keyword in content_lower for keyword in service_keywords):
                return None
            
            service_data = {
                "type": "service_request",
                "timestamp": email_result.timestamp.isoformat(),
                "customer_email": email_result.sender,
                "subject": email_result.subject,
                "description": email_result.content[:500],  # First 500 chars
                "intent": email_result.intent,
                "sentiment": email_result.sentiment,
                "priority": email_result.priority,
                "entities": email_result.entities
            }
            
            # Extract urgency indicators
            urgency_keywords = ['urgent', 'emergency', 'asap', 'immediately', 'critical']
            if any(keyword in content_lower for keyword in urgency_keywords):
                service_data["urgency"] = "high"
            
            return {
                "name": f"service_request_{email_result.email_id}",
                "episode_body": json.dumps(service_data),
                "source": "email_analysis",
                "source_description": f"Service request from {email_result.sender}"
            }
            
        except Exception as e:
            logger.error(f"Error extracting service info: {e}")
            return None
    
    async def _extract_maintenance_info(self, email_result: EmailAnalysisResult) -> Optional[Dict[str, Any]]:
        """Extract maintenance information from email."""
        try:
            # Check for maintenance-related content
            maintenance_keywords = ['maintenance', 'inspection', 'cleaning', 'filter', 'check-up', 'preventive']
            content_lower = email_result.content.lower()
            
            if not any(keyword in content_lower for keyword in maintenance_keywords):
                return None
            
            maintenance_data = {
                "type": "maintenance_discussion",
                "timestamp": email_result.timestamp.isoformat(),
                "customer_email": email_result.sender,
                "subject": email_result.subject,
                "content_summary": email_result.content[:300],
                "entities": email_result.entities,
                "intent": email_result.intent
            }
            
            return {
                "name": f"maintenance_discussion_{email_result.email_id}",
                "episode_body": json.dumps(maintenance_data),
                "source": "email_analysis",
                "source_description": f"Maintenance discussion with {email_result.sender}"
            }
            
        except Exception as e:
            logger.error(f"Error extracting maintenance info: {e}")
            return None
    
    def _find_hvac_models_in_content(self, content: str) -> List[str]:
        """Find HVAC model numbers in email content."""
        import re
        
        # Common HVAC model patterns
        patterns = [
            r'\bS\d{2}ET\b',  # LG S12ET, S18ET, etc.
            r'\bDual\s+Cool\b',  # Samsung Dual Cool
            r'\bFTXS\d{2}[A-Z]+\b',  # Daikin FTXS series
            r'\bMSZ-[A-Z0-9]+\b',  # Mitsubishi MSZ series
            r'\b[A-Z]{2,4}-\d{2,4}[A-Z]*\b'  # Generic model pattern
        ]
        
        models = []
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            models.extend(matches)
        
        return list(set(models))  # Remove duplicates
    
    async def _add_episode_to_graph(self, episode_data: Dict[str, Any]):
        """Add an episode to the Graphiti knowledge graph."""
        try:
            await self.graphiti.add_episode(
                name=episode_data["name"],
                episode_body=episode_data["episode_body"],
                source=episode_data["source"],
                source_description=episode_data["source_description"]
            )
            
            logger.info(f"Added episode to graph: {episode_data['name']}")
            
        except Exception as e:
            logger.error(f"Error adding episode to graph: {e}")

# ========== Integration Functions ==========

async def process_email_batch(
    graphiti_client: Graphiti, 
    email_results: List[EmailAnalysisResult]
) -> Dict[str, Any]:
    """Process a batch of email analysis results."""
    extractor = HVACKnowledgeExtractor(graphiti_client)
    
    total_episodes = 0
    processed_emails = 0
    errors = []
    
    for email_result in email_results:
        try:
            episodes = await extractor.process_email_analysis(email_result)
            total_episodes += len(episodes)
            processed_emails += 1
            
        except Exception as e:
            errors.append(f"Email {email_result.email_id}: {str(e)}")
            logger.error(f"Error processing email {email_result.email_id}: {e}")
    
    return {
        "processed_emails": processed_emails,
        "total_episodes": total_episodes,
        "errors": errors,
        "success_rate": processed_emails / len(email_results) if email_results else 0
    }

async def initialize_hvac_knowledge_graph(graphiti_client: Graphiti):
    """Initialize the knowledge graph with HVAC-specific structure."""
    try:
        # Add initial HVAC knowledge structure
        initial_episodes = [
            {
                "name": "hvac_system_initialization",
                "episode_body": json.dumps({
                    "type": "system_initialization",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "description": "HVAC CRM Knowledge Graph initialized",
                    "capabilities": [
                        "customer_tracking",
                        "equipment_monitoring",
                        "service_history",
                        "maintenance_scheduling",
                        "email_analysis_integration"
                    ]
                }),
                "source": "hvac_system",
                "source_description": "HVAC CRM System Initialization"
            }
        ]
        
        for episode in initial_episodes:
            await graphiti_client.add_episode(
                name=episode["name"],
                episode_body=episode["episode_body"],
                source=episode["source"],
                source_description=episode["source_description"]
            )
        
        logger.info("HVAC Knowledge Graph initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing HVAC knowledge graph: {e}")

if __name__ == "__main__":
    # Example usage
    async def main():
        from hvac_graphiti_agent import initialize_graphiti
        
        graphiti = await initialize_graphiti()
        await initialize_hvac_knowledge_graph(graphiti)
        
        # Example email analysis result
        example_email = EmailAnalysisResult(
            email_id="test_001",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="LG S12ET not cooling properly",
            timestamp=datetime.now(timezone.utc),
            content="Hello, my LG S12ET air conditioner is not cooling properly. It was installed last year and needs urgent repair.",
            analysis={"confidence": 0.95},
            entities=[
                {"type": "EQUIPMENT", "value": "LG S12ET"},
                {"type": "PERSON", "value": "customer"}
            ],
            intent="service_request",
            sentiment="concerned",
            priority="high"
        )
        
        extractor = HVACKnowledgeExtractor(graphiti)
        episodes = await extractor.process_email_analysis(example_email)
        
        print(f"Processed example email: {len(episodes)} episodes created")
    
    asyncio.run(main())
