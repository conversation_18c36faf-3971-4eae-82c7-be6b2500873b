"""
Enhanced Gradio Interface for HVAC Graphiti Knowledge Graph
Provides an intuitive web interface for interacting with the temporal knowledge graph.
"""

import gradio as gr
import asyncio
import json
import pandas as pd
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional, Tuple
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots

from .hvac_graphiti_agent import initialize_graphiti, hvac_agent, HVACGraphitiDependencies
from .email_to_graphiti import EmailAnalysisResult, HVACKnowledgeExtractor
from rich.console import Console

# Global variables
graphiti_client = None
knowledge_extractor = None
console = Console()

# ========== Initialization ==========

async def initialize_interface():
    """Initialize the Graphiti interface."""
    global graphiti_client, knowledge_extractor
    
    try:
        graphiti_client = await initialize_graphiti()
        knowledge_extractor = HVACKnowledgeExtractor(graphiti_client)
        return "✅ Graphiti Knowledge Graph connected successfully!"
    except Exception as e:
        return f"❌ Failed to connect: {str(e)}"

# ========== Agent Chat Functions ==========

async def chat_with_hvac_agent(message: str, history: List[List[str]]) -> Tuple[str, List[List[str]]]:
    """Chat with the HVAC Graphiti agent."""
    if not graphiti_client:
        return "❌ Graphiti not initialized. Please initialize first.", history
    
    try:
        deps = HVACGraphitiDependencies(
            graphiti_client=graphiti_client,
            console=console
        )
        
        result = await hvac_agent.run(message, deps=deps)
        response = result.data
        
        # Add to history
        history.append([message, response])
        
        return "", history
        
    except Exception as e:
        error_msg = f"❌ Error: {str(e)}"
        history.append([message, error_msg])
        return "", history

# ========== Knowledge Graph Search ==========

async def search_knowledge_graph(query: str, limit: int = 10) -> Tuple[str, pd.DataFrame]:
    """Search the knowledge graph and return results."""
    if not graphiti_client:
        return "❌ Graphiti not initialized", pd.DataFrame()
    
    try:
        results = await graphiti_client.search(query=query, limit=limit)
        
        # Convert to DataFrame
        data = []
        for result in results:
            data.append({
                "Fact": result.fact,
                "Valid From": result.valid_at or "Unknown",
                "Valid Until": result.invalid_at or "Current",
                "UUID": result.uuid[:8] + "..." if result.uuid else "N/A",
                "Score": getattr(result, 'score', 0.0)
            })
        
        df = pd.DataFrame(data)
        
        if df.empty:
            return f"No results found for query: '{query}'", df
        
        return f"Found {len(df)} results for query: '{query}'", df
        
    except Exception as e:
        return f"❌ Search error: {str(e)}", pd.DataFrame()

# ========== Email Processing ==========

async def process_sample_email(
    sender: str,
    subject: str,
    content: str,
    intent: str = "service_request",
    priority: str = "medium"
) -> str:
    """Process a sample email and add to knowledge graph."""
    if not knowledge_extractor:
        return "❌ Knowledge extractor not initialized"
    
    try:
        # Create sample email analysis result
        email_result = EmailAnalysisResult(
            email_id=f"sample_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            sender=sender,
            recipient="<EMAIL>",
            subject=subject,
            timestamp=datetime.now(timezone.utc),
            content=content,
            analysis={"confidence": 0.95},
            entities=[],  # Would be populated by real NLP
            intent=intent,
            sentiment="neutral",
            priority=priority
        )
        
        # Process the email
        episodes = await knowledge_extractor.process_email_analysis(email_result)
        
        return f"✅ Processed email successfully! Created {len(episodes)} knowledge episodes."
        
    except Exception as e:
        return f"❌ Error processing email: {str(e)}"

# ========== Visualization Functions ==========

def create_knowledge_timeline():
    """Create a timeline visualization of knowledge events."""
    # This would require querying the Neo4j database for temporal data
    # For now, return a placeholder
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=[datetime.now()],
        y=["Sample Event"],
        mode='markers+text',
        text=["Knowledge Graph Initialized"],
        textposition="top center"
    ))
    
    fig.update_layout(
        title="HVAC Knowledge Timeline",
        xaxis_title="Time",
        yaxis_title="Events",
        height=400
    )
    
    return fig

def create_equipment_health_dashboard():
    """Create equipment health dashboard."""
    # Sample data - would be populated from knowledge graph
    equipment_data = {
        "Equipment": ["LG S12ET #001", "LG S18ET #002", "Daikin FTXS25 #003"],
        "Health Score": [85, 92, 78],
        "Last Service": ["2024-01-15", "2024-02-20", "2024-01-30"],
        "Status": ["Good", "Excellent", "Needs Attention"]
    }
    
    df = pd.DataFrame(equipment_data)
    
    fig = px.bar(
        df, 
        x="Equipment", 
        y="Health Score",
        color="Status",
        title="Equipment Health Scores",
        color_discrete_map={
            "Excellent": "green",
            "Good": "yellow", 
            "Needs Attention": "red"
        }
    )
    
    return fig

# ========== Gradio Interface ==========

def create_gradio_interface():
    """Create the main Gradio interface."""
    
    with gr.Blocks(
        title="HVAC Graphiti Knowledge Graph",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .tab-nav {
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        """
    ) as interface:
        
        gr.Markdown("""
        # 🔧 HVAC Graphiti Knowledge Graph Interface
        
        **Temporal Knowledge Management for HVAC CRM System**
        
        This interface provides access to the Graphiti-powered knowledge graph that tracks:
        - Customer interactions and history
        - Equipment lifecycle and maintenance
        - Service patterns and predictions
        - Email analysis and insights
        """)
        
        # Initialization section
        with gr.Row():
            init_btn = gr.Button("🚀 Initialize Graphiti Connection", variant="primary")
            init_status = gr.Textbox(label="Connection Status", interactive=False)
        
        init_btn.click(
            fn=lambda: asyncio.run(initialize_interface()),
            outputs=init_status
        )
        
        # Main tabs
        with gr.Tabs():
            
            # Agent Chat Tab
            with gr.Tab("🤖 HVAC Agent Chat"):
                gr.Markdown("### Chat with the HVAC Knowledge Agent")
                
                chatbot = gr.Chatbot(
                    label="HVAC Agent",
                    height=400,
                    bubble_full_width=False
                )
                
                with gr.Row():
                    msg_input = gr.Textbox(
                        label="Your Message",
                        placeholder="Ask about equipment, customers, maintenance patterns...",
                        scale=4
                    )
                    send_btn = gr.Button("Send", variant="primary", scale=1)
                
                # Example queries
                gr.Examples(
                    examples=[
                        "What equipment needs maintenance this month?",
                        "Show me the service history <NAME_EMAIL>",
                        "What are common issues with LG S12ET units?",
                        "Which customers have the most service requests?",
                        "Predict maintenance needs for next quarter"
                    ],
                    inputs=msg_input
                )
                
                # Chat functionality
                send_btn.click(
                    fn=lambda msg, hist: asyncio.run(chat_with_hvac_agent(msg, hist)),
                    inputs=[msg_input, chatbot],
                    outputs=[msg_input, chatbot]
                )
                
                msg_input.submit(
                    fn=lambda msg, hist: asyncio.run(chat_with_hvac_agent(msg, hist)),
                    inputs=[msg_input, chatbot],
                    outputs=[msg_input, chatbot]
                )
            
            # Knowledge Search Tab
            with gr.Tab("🔍 Knowledge Search"):
                gr.Markdown("### Search the Temporal Knowledge Graph")
                
                with gr.Row():
                    search_input = gr.Textbox(
                        label="Search Query",
                        placeholder="Enter search terms...",
                        scale=3
                    )
                    search_limit = gr.Slider(
                        label="Max Results",
                        minimum=5,
                        maximum=50,
                        value=10,
                        step=5,
                        scale=1
                    )
                    search_btn = gr.Button("Search", variant="primary", scale=1)
                
                search_status = gr.Textbox(label="Search Status", interactive=False)
                search_results = gr.Dataframe(
                    label="Search Results",
                    headers=["Fact", "Valid From", "Valid Until", "UUID", "Score"],
                    interactive=False
                )
                
                search_btn.click(
                    fn=lambda q, l: asyncio.run(search_knowledge_graph(q, l)),
                    inputs=[search_input, search_limit],
                    outputs=[search_status, search_results]
                )
            
            # Email Processing Tab
            with gr.Tab("📧 Email Processing"):
                gr.Markdown("### Process Email for Knowledge Extraction")
                
                with gr.Column():
                    email_sender = gr.Textbox(label="Sender Email", placeholder="<EMAIL>")
                    email_subject = gr.Textbox(label="Subject", placeholder="LG S12ET not cooling properly")
                    email_content = gr.Textbox(
                        label="Email Content",
                        placeholder="Describe the issue or request...",
                        lines=5
                    )
                    
                    with gr.Row():
                        email_intent = gr.Dropdown(
                            label="Intent",
                            choices=["service_request", "maintenance_inquiry", "general_question", "complaint"],
                            value="service_request"
                        )
                        email_priority = gr.Dropdown(
                            label="Priority",
                            choices=["low", "medium", "high", "urgent"],
                            value="medium"
                        )
                    
                    process_email_btn = gr.Button("Process Email", variant="primary")
                    email_status = gr.Textbox(label="Processing Status", interactive=False)
                
                process_email_btn.click(
                    fn=lambda s, sub, c, i, p: asyncio.run(process_sample_email(s, sub, c, i, p)),
                    inputs=[email_sender, email_subject, email_content, email_intent, email_priority],
                    outputs=email_status
                )
            
            # Analytics Tab
            with gr.Tab("📊 Analytics & Insights"):
                gr.Markdown("### Knowledge Graph Analytics")
                
                with gr.Row():
                    refresh_analytics_btn = gr.Button("🔄 Refresh Analytics", variant="secondary")
                
                with gr.Row():
                    timeline_plot = gr.Plot(label="Knowledge Timeline")
                    equipment_plot = gr.Plot(label="Equipment Health Dashboard")
                
                refresh_analytics_btn.click(
                    fn=lambda: (create_knowledge_timeline(), create_equipment_health_dashboard()),
                    outputs=[timeline_plot, equipment_plot]
                )
                
                # Load initial plots
                interface.load(
                    fn=lambda: (create_knowledge_timeline(), create_equipment_health_dashboard()),
                    outputs=[timeline_plot, equipment_plot]
                )
        
        # Footer
        gr.Markdown("""
        ---
        **HVAC Graphiti Knowledge Graph** | Powered by Zep Graphiti & Pydantic AI | 
        Integrated with HVAC CRM System
        """)
    
    return interface

# ========== Main Function ==========

def launch_interface():
    """Launch the Gradio interface."""
    interface = create_gradio_interface()
    
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True
    )

if __name__ == "__main__":
    launch_interface()
