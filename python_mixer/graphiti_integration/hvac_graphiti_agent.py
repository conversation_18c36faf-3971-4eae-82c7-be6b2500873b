"""
HVAC-Specific Graphiti Agent for Temporal Knowledge Management
Integrates with the existing HVAC CRM system for enhanced customer and equipment tracking.
"""

from __future__ import annotations
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timezone
import asyncio
import os
import json

from pydantic import BaseModel, Field
from pydantic_ai import Agent, RunContext
from pydantic_ai.providers.openai import OpenAIProvider
from pydantic_ai.models.openai import OpenAIModel
from graphiti_core import Graphiti
from dotenv import load_dotenv
from rich.console import Console
from rich.markdown import Markdown

# Load environment variables
load_dotenv()

console = Console()

# ========== HVAC-Specific Models ==========

class HVACEquipmentInfo(BaseModel):
    """Model for HVAC equipment information."""
    equipment_id: str = Field(description="Unique equipment identifier")
    model: str = Field(description="Equipment model (e.g., LG S12ET)")
    type: str = Field(description="Equipment type (split, ducted, etc.)")
    capacity: Optional[str] = Field(None, description="Cooling/heating capacity")
    installation_date: Optional[str] = Field(None, description="Installation date")
    warranty_expiry: Optional[str] = Field(None, description="Warranty expiration date")
    last_service: Optional[str] = Field(None, description="Last service date")
    health_score: Optional[float] = Field(None, description="AI-calculated health score")

class CustomerProfile(BaseModel):
    """Model for customer profile information."""
    customer_id: str = Field(description="Unique customer identifier")
    name: str = Field(description="Customer name")
    email: Optional[str] = Field(None, description="Customer email")
    phone: Optional[str] = Field(None, description="Customer phone")
    address: Optional[str] = Field(None, description="Customer address")
    equipment_count: Optional[int] = Field(None, description="Number of equipment units")
    total_value: Optional[float] = Field(None, description="Total equipment value")
    service_history_count: Optional[int] = Field(None, description="Number of service calls")

class ServiceTicket(BaseModel):
    """Model for service ticket information."""
    ticket_id: str = Field(description="Unique ticket identifier")
    customer_id: str = Field(description="Associated customer ID")
    equipment_id: Optional[str] = Field(None, description="Associated equipment ID")
    issue_type: str = Field(description="Type of issue")
    priority: str = Field(description="Ticket priority")
    status: str = Field(description="Current ticket status")
    created_date: str = Field(description="Ticket creation date")
    resolved_date: Optional[str] = Field(None, description="Resolution date")
    technician: Optional[str] = Field(None, description="Assigned technician")

# ========== Dependencies ==========

@dataclass
class HVACGraphitiDependencies:
    """Dependencies for the HVAC Graphiti agent."""
    graphiti_client: Graphiti
    console: Console

# ========== Helper Functions ==========

def get_model():
    """Configure and return the LLM model to use."""
    model_choice = os.getenv('MODEL_CHOICE', 'gpt-4o-mini')
    api_key = os.getenv('OPENAI_API_KEY', 'no-api-key-provided')
    return OpenAIModel(model_choice, provider=OpenAIProvider(api_key=api_key))

async def initialize_graphiti() -> Graphiti:
    """Initialize Graphiti client with Neo4j connection."""
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
    
    graphiti = Graphiti(
        neo4j_uri=neo4j_uri,
        neo4j_user=neo4j_user,
        neo4j_password=neo4j_password
    )
    
    # Initialize the client
    await graphiti.build_indices_and_constraints()
    return graphiti

# ========== HVAC Graphiti Agent ==========

hvac_agent = Agent(
    get_model(),
    system_prompt="""You are an expert HVAC technician and customer service agent with access to a comprehensive 
    temporal knowledge graph containing information about HVAC equipment, customers, service history, and maintenance records.
    
    Your capabilities include:
    - Analyzing equipment health and predicting maintenance needs
    - Tracking customer service history and preferences
    - Providing technical recommendations based on historical data
    - Identifying patterns in equipment failures and service requests
    - Generating insights for preventive maintenance scheduling
    
    When answering questions:
    1. Search the knowledge graph for relevant information
    2. Consider temporal aspects (when events occurred, equipment age, service intervals)
    3. Provide actionable recommendations based on historical patterns
    4. Be specific about equipment models, service dates, and technical details
    5. Suggest preventive measures when appropriate
    
    Always be professional, technically accurate, and customer-focused in your responses.""",
    deps_type=HVACGraphitiDependencies
)

# ========== Search Tools ==========

@hvac_agent.tool
async def search_equipment_history(
    ctx: RunContext[HVACGraphitiDependencies], 
    equipment_id: str,
    query: str = ""
) -> List[Dict[str, Any]]:
    """Search for equipment history and maintenance records."""
    try:
        search_query = f"equipment {equipment_id} {query}".strip()
        results = await ctx.deps.graphiti_client.search(
            query=search_query,
            limit=10
        )
        
        equipment_facts = []
        for result in results:
            equipment_facts.append({
                "fact": result.fact,
                "valid_at": result.valid_at,
                "invalid_at": result.invalid_at,
                "uuid": result.uuid
            })
        
        ctx.deps.console.print(f"[green]Found {len(equipment_facts)} equipment records[/green]")
        return equipment_facts
        
    except Exception as e:
        ctx.deps.console.print(f"[red]Error searching equipment history: {e}[/red]")
        return []

@hvac_agent.tool
async def search_customer_profile(
    ctx: RunContext[HVACGraphitiDependencies], 
    customer_id: str,
    include_equipment: bool = True
) -> Dict[str, Any]:
    """Search for comprehensive customer profile including equipment and service history."""
    try:
        search_query = f"customer {customer_id}"
        if include_equipment:
            search_query += " equipment service maintenance"
            
        results = await ctx.deps.graphiti_client.search(
            query=search_query,
            limit=20
        )
        
        customer_data = {
            "customer_info": [],
            "equipment": [],
            "service_history": [],
            "maintenance_records": []
        }
        
        for result in results:
            fact = result.fact.lower()
            if "customer" in fact and "contact" in fact:
                customer_data["customer_info"].append({
                    "fact": result.fact,
                    "valid_at": result.valid_at,
                    "invalid_at": result.invalid_at
                })
            elif "equipment" in fact or "model" in fact:
                customer_data["equipment"].append({
                    "fact": result.fact,
                    "valid_at": result.valid_at,
                    "invalid_at": result.invalid_at
                })
            elif "service" in fact or "repair" in fact:
                customer_data["service_history"].append({
                    "fact": result.fact,
                    "valid_at": result.valid_at,
                    "invalid_at": result.invalid_at
                })
            elif "maintenance" in fact or "inspection" in fact:
                customer_data["maintenance_records"].append({
                    "fact": result.fact,
                    "valid_at": result.valid_at,
                    "invalid_at": result.invalid_at
                })
        
        ctx.deps.console.print(f"[green]Found customer profile with {len(results)} total records[/green]")
        return customer_data
        
    except Exception as e:
        ctx.deps.console.print(f"[red]Error searching customer profile: {e}[/red]")
        return {}

@hvac_agent.tool
async def search_maintenance_patterns(
    ctx: RunContext[HVACGraphitiDependencies], 
    equipment_type: str,
    time_period: str = "last_year"
) -> List[Dict[str, Any]]:
    """Search for maintenance patterns and failure trends for specific equipment types."""
    try:
        search_query = f"{equipment_type} maintenance failure pattern {time_period}"
        results = await ctx.deps.graphiti_client.search(
            query=search_query,
            limit=15
        )
        
        patterns = []
        for result in results:
            patterns.append({
                "pattern": result.fact,
                "valid_at": result.valid_at,
                "invalid_at": result.invalid_at,
                "confidence": getattr(result, 'score', 0.0)
            })
        
        ctx.deps.console.print(f"[green]Found {len(patterns)} maintenance patterns[/green]")
        return patterns
        
    except Exception as e:
        ctx.deps.console.print(f"[red]Error searching maintenance patterns: {e}[/red]")
        return []

# ========== Knowledge Graph Population ==========

async def add_hvac_episode(graphiti: Graphiti, episode_data: Dict[str, Any], source: str = "hvac_system"):
    """Add an episode to the HVAC knowledge graph."""
    try:
        episode_body = json.dumps(episode_data) if isinstance(episode_data, dict) else str(episode_data)
        
        await graphiti.add_episode(
            name=f"hvac_episode_{datetime.now(timezone.utc).isoformat()}",
            episode_body=episode_body,
            source=source,
            source_description=f"HVAC CRM System - {source}"
        )
        
        console.print(f"[green]✅ Added HVAC episode to knowledge graph[/green]")
        
    except Exception as e:
        console.print(f"[red]❌ Error adding episode: {e}[/red]")

# ========== Main Interface ==========

async def run_hvac_agent():
    """Run the interactive HVAC Graphiti agent."""
    console.print("[bold blue]🔧 HVAC Graphiti Agent Starting...[/bold blue]")
    
    try:
        # Initialize Graphiti
        graphiti = await initialize_graphiti()
        console.print("[green]✅ Connected to Neo4j knowledge graph[/green]")
        
        # Create dependencies
        deps = HVACGraphitiDependencies(
            graphiti_client=graphiti,
            console=console
        )
        
        console.print("\n[bold green]🚀 HVAC Agent Ready![/bold green]")
        console.print("Ask me about equipment, customers, maintenance patterns, or service history.")
        console.print("Type 'quit' to exit.\n")
        
        while True:
            try:
                user_input = input("🔧 HVAC Query: ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not user_input:
                    continue
                
                console.print(f"\n[yellow]Processing: {user_input}[/yellow]")
                
                # Run the agent
                result = await hvac_agent.run(user_input, deps=deps)
                
                # Display the response
                console.print("\n[bold cyan]🤖 HVAC Agent Response:[/bold cyan]")
                console.print(Markdown(result.data))
                console.print("\n" + "="*60 + "\n")
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
        
        console.print("[yellow]👋 HVAC Agent shutting down...[/yellow]")
        
    except Exception as e:
        console.print(f"[red]❌ Failed to initialize HVAC Agent: {e}[/red]")

if __name__ == "__main__":
    asyncio.run(run_hvac_agent())
