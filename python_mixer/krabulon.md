# Integracja agentów AI z crawlerami do wzbogacania bazy danych HVAC

## Wprowadzenie

Niniejszy raport przedstawia strategię integracji systemów multi-agentowych z bibliotekami do crawlowania w celu automatycznego wzbogacania bazy danych o urządzenia HVAC, dane techniczne i inne informacje branżowe. Skupiamy się na rozwiązaniach hostowanych lokalnie, zaimplementowanych w Pythonie, które umożliwiają efektywne zbieranie, przetwarzanie i przechowywanie danych technicznych.

## Porównanie bibliotek do crawlowania

### 1. Crawl4AI

**GitHub**: [unclecode/crawl4ai](https://github.com/unclecode/crawl4ai)

Crawl4AI to nowoczesna biblioteka do crawlowania stron internetowych, zaprojektowana specjalnie z myślą o integracji z modelami językowymi (LLM) i systemami agentowymi.

**Kluczowe funkcje**:
- Generowanie czystego Markdown zoptymalizowanego dla LLM
- Ekstrakcja strukturalna danych za pomocą CSS, XPath lub LLM
- Zaawansowana kontrola przeglądarki (hooki, proxy, tryby ukrywania)
- Wysoka wydajność dzięki równoległemu crawlowaniu
- Pełna obsługa JavaScript i dynamicznych stron
- Automatyczne wykrywanie i obsługa tabel (konwersja do DataFrame)
- Integracja z protokołem MCP dla narzędzi AI

**Zalety dla HVAC**:
- Doskonała obsługa stron z danymi technicznymi i specyfikacjami
- Możliwość ekstrakcji tabel z parametrami urządzeń
- Łatwa integracja z agentami AI

**Hostowanie lokalne**:
- Pełne wsparcie dla instalacji lokalnej przez pip
- Możliwość uruchomienia w kontenerze Docker
- Nie wymaga zewnętrznych API ani kluczy

### 2. Crawlee

**GitHub**: [apify/crawlee-python](https://github.com/apify/crawlee-python)

Crawlee to biblioteka do web scrapingu i automatyzacji przeglądarki, zaprojektowana do budowania niezawodnych crawlerów.

**Kluczowe funkcje**:
- Ujednolicony interfejs dla crawlowania HTTP i przeglądarki headless
- Automatyczne crawlowanie równoległe
- Automatyczne ponawianie prób w przypadku błędów lub blokad
- Zintegrowana rotacja proxy i zarządzanie sesjami
- Konfigurowalne routowanie żądań

**Zalety dla HVAC**:
- Wysoka niezawodność, co jest kluczowe przy zbieraniu danych technicznych
- Dobra obsługa stron z katalogami produktów

**Hostowanie lokalne**:
- Łatwa instalacja przez pip
- Możliwość uruchomienia lokalnie bez zewnętrznych zależności

### 3. Scrapy

**GitHub**: [scrapy/scrapy](https://github.com/scrapy/scrapy)

Scrapy to dojrzały framework do ekstrakcji danych, który umożliwia efektywne crawlowanie stron internetowych.

**Kluczowe funkcje**:
- Rozbudowany ekosystem rozszerzeń
- Wysoka wydajność
- Zaawansowane selektory CSS i XPath
- Obsługa potoków przetwarzania danych
- Wsparcie dla różnych formatów eksportu

**Zalety dla HVAC**:
- Sprawdzona technologia z dużą społecznością
- Dobre wsparcie dla ekstrakcji danych strukturalnych

**Hostowanie lokalne**:
- Standardowa instalacja przez pip
- Pełne wsparcie dla uruchamiania lokalnego

## Architektura integracji agentów z crawlerami

### Ogólna architektura systemu

Proponowana architektura składa się z następujących komponentów:

1. **System multi-agentowy** (LangGraph/CrewAI) - orkiestracja całego procesu
2. **Moduł crawlowania** (Crawl4AI/Crawlee/Scrapy) - zbieranie danych z internetu
3. **Moduł przetwarzania danych** - ekstrakcja, transformacja i normalizacja danych
4. **Baza danych** - przechowywanie i indeksowanie danych
5. **Interfejs użytkownika** - zarządzanie systemem i przeglądanie danych

### Przepływ pracy między agentami a crawlerami

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Agent Planista │────▶│  Agent Crawler  │────▶│ Agent Ekstrakcji│
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
        │                       │                       │
        │                       │                       │
        ▼                       ▼                       ▼
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Baza Wiedzy    │     │  Moduł Crawlera │     │ Moduł Ekstrakcji│
│  HVAC           │     │  (Crawl4AI)     │     │  Danych         │
└─────────────────┘     └─────────────────┘     └─────────────────┘
                                                        │
                                                        │
┌─────────────────┐     ┌─────────────────┐             │
│                 │     │                 │             │
│ Agent Walidacji │◀────│ Agent Integracji│◀────────────┘
│                 │     │                 │
└─────────────────┘     └─────────────────┘
        │                       │
        │                       │
        ▼                       ▼
┌─────────────────┐     ┌─────────────────┐
│  Moduł          │     │                 │
│  Walidacji      │     │  Baza Danych    │
└─────────────────┘     └─────────────────┘
```

### Szczegółowy opis agentów i ich zadań

#### 1. Agent Planista

**Zadania**:
- Identyfikacja źródeł danych (strony producentów, katalogi, dokumentacje techniczne)
- Planowanie strategii crawlowania (częstotliwość, głębokość, priorytety)
- Koordynacja pracy innych agentów
- Monitorowanie postępu i rozwiązywanie konfliktów

**Integracja z crawlerem**:
```python
import asyncio
from crawl4ai import AsyncWebCrawler
from langroid import ChatAgent, Task

async def plan_crawling_strategy(sources, priorities):
    # Konfiguracja agenta planisty
    agent_cfg = lr.ChatAgentConfig(llm=llm_cfg)
    planner_agent = lr.ChatAgent(agent_cfg)
    planner_task = lr.Task(
        planner_agent, 
        name="Planner", 
        system_message="Zaplanuj strategię crawlowania dla źródeł danych HVAC."
    )
    
    # Generowanie planu crawlowania
    crawl_plan = await planner_task.llm_response(
        f"Zaplanuj strategię crawlowania dla następujących źródeł: {sources} "
        f"z priorytetami: {priorities}"
    )
    
    return crawl_plan
```

#### 2. Agent Crawler

**Zadania**:
- Wykonywanie operacji crawlowania zgodnie z planem
- Obsługa różnych typów stron (katalogi produktów, specyfikacje techniczne)
- Zarządzanie sesjami i uwierzytelnianiem
- Obsługa błędów i ponawianie prób

**Integracja z crawlerem**:
```python
async def execute_crawling(urls, crawler_config):
    # Konfiguracja agenta crawlera
    agent_cfg = lr.ChatAgentConfig(llm=llm_cfg)
    crawler_agent = lr.ChatAgent(agent_cfg)
    crawler_task = lr.Task(
        crawler_agent, 
        name="Crawler", 
        system_message="Wykonaj operacje crawlowania dla podanych URL-i."
    )
    
    # Inicjalizacja crawlera
    async with AsyncWebCrawler() as crawler:
        results = []
        for url in urls:
            # Agent decyduje o parametrach crawlowania dla konkretnego URL
            crawl_params = await crawler_task.llm_response(
                f"Określ parametry crawlowania dla URL: {url}"
            )
            
            # Wykonanie crawlowania
            result = await crawler.arun(
                url=url,
                **eval(crawl_params)  # Konwersja odpowiedzi agenta na parametry
            )
            results.append(result)
    
    return results
```

#### 3. Agent Ekstrakcji

**Zadania**:
- Analiza zawartości stron
- Identyfikacja i ekstrakcja kluczowych danych technicznych
- Normalizacja i strukturyzacja danych
- Przygotowanie danych do zapisania w bazie

**Integracja z crawlerem**:
```python
async def extract_hvac_data(crawl_results):
    # Konfiguracja agenta ekstrakcji
    agent_cfg = lr.ChatAgentConfig(llm=llm_cfg)
    extraction_agent = lr.ChatAgent(agent_cfg)
    extraction_task = lr.Task(
        extraction_agent, 
        name="Extractor", 
        system_message="Ekstrahuj dane techniczne HVAC z zawartości stron."
    )
    
    extracted_data = []
    for result in crawl_results:
        # Agent analizuje zawartość i ekstrahuje dane
        data = await extraction_task.llm_response(
            f"Ekstrahuj dane techniczne urządzeń HVAC z następującej zawartości: "
            f"{result.markdown[:4000]}..."  # Limit długości dla LLM
        )
        
        # Strukturyzacja danych
        structured_data = parse_extraction_response(data)
        extracted_data.append(structured_data)
    
    return extracted_data
```

#### 4. Agent Integracji

**Zadania**:
- Mapowanie wyekstrahowanych danych do schematu bazy danych
- Rozwiązywanie konfliktów i duplikatów
- Wzbogacanie istniejących rekordów nowymi danymi
- Zarządzanie relacjami między danymi

**Integracja z bazą danych**:
```python
async def integrate_data(extracted_data, db_connection):
    # Konfiguracja agenta integracji
    agent_cfg = lr.ChatAgentConfig(llm=llm_cfg)
    integration_agent = lr.ChatAgent(agent_cfg)
    integration_task = lr.Task(
        integration_agent, 
        name="Integrator", 
        system_message="Integruj dane techniczne HVAC z bazą danych."
    )
    
    for data_item in extracted_data:
        # Sprawdzenie, czy dane już istnieją w bazie
        existing_data = query_database(db_connection, data_item["model"])
        
        if existing_data:
            # Agent decyduje, jak zintegrować nowe dane z istniejącymi
            integration_plan = await integration_task.llm_response(
                f"Zaplanuj integrację nowych danych: {data_item} "
                f"z istniejącymi danymi: {existing_data}"
            )
            
            # Wykonanie integracji
            execute_integration_plan(db_connection, integration_plan, data_item, existing_data)
        else:
            # Dodanie nowego rekordu
            insert_new_record(db_connection, data_item)
```

#### 5. Agent Walidacji

**Zadania**:
- Weryfikacja poprawności i kompletności danych
- Wykrywanie anomalii i niespójności
- Generowanie raportów jakości danych
- Sugerowanie działań naprawczych

**Implementacja**:
```python
async def validate_data(db_connection):
    # Konfiguracja agenta walidacji
    agent_cfg = lr.ChatAgentConfig(llm=llm_cfg)
    validation_agent = lr.ChatAgent(agent_cfg)
    validation_task = lr.Task(
        validation_agent, 
        name="Validator", 
        system_message="Waliduj dane techniczne HVAC w bazie danych."
    )
    
    # Pobieranie próbki danych do walidacji
    data_sample = get_data_sample(db_connection)
    
    # Agent przeprowadza walidację
    validation_report = await validation_task.llm_response(
        f"Przeprowadź walidację następujących danych HVAC: {data_sample}"
    )
    
    # Analiza raportu i podejmowanie działań
    issues = parse_validation_report(validation_report)
    for issue in issues:
        fix_data_issue(db_connection, issue)
```

### Struktura danych dla informacji technicznych HVAC

Poniżej przedstawiono przykładową strukturę danych dla urządzeń HVAC, która może być używana do przechowywania wyekstrahowanych informacji:

```python
hvac_equipment = {
    "basic_info": {
        "manufacturer": str,  # Producent
        "model": str,         # Model
        "type": str,          # Typ urządzenia (np. klimatyzator, pompa ciepła)
        "series": str,        # Seria produktu
        "release_year": int,  # Rok wprowadzenia na rynek
    },
    "technical_specs": {
        "cooling_capacity": {
            "value": float,   # Wartość
            "unit": str,      # Jednostka (np. kW, BTU/h)
        },
        "heating_capacity": {
            "value": float,
            "unit": str,
        },
        "energy_efficiency": {
            "cooling_eer": float,  # EER dla chłodzenia
            "heating_cop": float,  # COP dla ogrzewania
            "seer": float,         # SEER
            "scop": float,         # SCOP
            "energy_class": str,   # Klasa energetyczna
        },
        "dimensions": {
            "width": float,
            "height": float,
            "depth": float,
            "unit": str,      # Jednostka (np. mm, cm)
        },
        "weight": {
            "value": float,
            "unit": str,
        },
        "refrigerant": {
            "type": str,      # Typ czynnika chłodniczego (np. R32, R410A)
            "charge": float,  # Ilość czynnika
            "unit": str,      # Jednostka (np. kg)
        },
        "electrical": {
            "power_supply": str,  # Zasilanie (np. 230V, 50Hz)
            "power_consumption": {
                "cooling": float,  # Pobór mocy przy chłodzeniu
                "heating": float,  # Pobór mocy przy ogrzewaniu
                "unit": str,       # Jednostka (np. W, kW)
            },
        },
        "airflow": {
            "value": float,   # Przepływ powietrza
            "unit": str,      # Jednostka (np. m³/h)
        },
        "noise_level": {
            "indoor": float,  # Poziom hałasu jednostki wewnętrznej
            "outdoor": float, # Poziom hałasu jednostki zewnętrznej
            "unit": str,      # Jednostka (np. dB(A))
        },
        "operating_range": {
            "cooling": {
                "min": float, # Minimalna temperatura pracy w trybie chłodzenia
                "max": float, # Maksymalna temperatura pracy w trybie chłodzenia
            },
            "heating": {
                "min": float, # Minimalna temperatura pracy w trybie ogrzewania
                "max": float, # Maksymalna temperatura pracy w trybie ogrzewania
            },
            "unit": str,      # Jednostka (np. °C)
        },
    },
    "features": [str],        # Lista funkcji (np. WiFi, jonizator)
    "certifications": [str],  # Lista certyfikatów
    "documentation": {
        "manuals": [str],     # Lista URL-i do instrukcji
        "datasheets": [str],  # Lista URL-i do kart katalogowych
        "installation_guides": [str], # Lista URL-i do instrukcji montażu
    },
    "metadata": {
        "source_url": str,    # URL źródłowy
        "extraction_date": str, # Data ekstrakcji
        "last_updated": str,  # Data ostatniej aktualizacji
        "confidence_score": float, # Ocena pewności danych (0-1)
    }
}
```

### Integracja z bazami danych

#### SQL (PostgreSQL)

```python
import psycopg2
import json

def save_to_postgresql(data, connection_string):
    conn = psycopg2.connect(connection_string)
    cursor = conn.cursor()
    
    # Podstawowe informacje do tabeli głównej
    cursor.execute("""
        INSERT INTO hvac_equipment 
        (manufacturer, model, type, series, release_year, source_url, extraction_date, last_updated, confidence_score)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        RETURNING id
    """, (
        data["basic_info"]["manufacturer"],
        data["basic_info"]["model"],
        data["basic_info"]["type"],
        data["basic_info"]["series"],
        data["basic_info"]["release_year"],
        data["metadata"]["source_url"],
        data["metadata"]["extraction_date"],
        data["metadata"]["last_updated"],
        data["metadata"]["confidence_score"]
    ))
    
    equipment_id = cursor.fetchone()[0]
    
    # Specyfikacje techniczne do tabeli specs
    tech_specs = data["technical_specs"]
    cursor.execute("""
        INSERT INTO technical_specs
        (equipment_id, cooling_capacity, cooling_unit, heating_capacity, heating_unit, 
         cooling_eer, heating_cop, seer, scop, energy_class)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
    """, (
        equipment_id,
        tech_specs["cooling_capacity"]["value"],
        tech_specs["cooling_capacity"]["unit"],
        tech_specs["heating_capacity"]["value"],
        tech_specs["heating_capacity"]["unit"],
        tech_specs["energy_efficiency"]["cooling_eer"],
        tech_specs["energy_efficiency"]["heating_cop"],
        tech_specs["energy_efficiency"]["seer"],
        tech_specs["energy_efficiency"]["scop"],
        tech_specs["energy_efficiency"]["energy_class"]
    ))
    
    # Dodawanie funkcji do tabeli features
    for feature in data["features"]:
        cursor.execute("""
            INSERT INTO features (equipment_id, feature_name)
            VALUES (%s, %s)
        """, (equipment_id, feature))
    
    conn.commit()
    cursor.close()
    conn.close()
```

#### NoSQL (MongoDB)

```python
from pymongo import MongoClient

def save_to_mongodb(data, connection_string):
    client = MongoClient(connection_string)
    db = client.hvac_database
    collection = db.equipment
    
    # MongoDB może przechowywać całą strukturę danych bezpośrednio
    result = collection.insert_one(data)
    
    return result.inserted_id
```

#### Graph (Neo4j)

```python
from neo4j import GraphDatabase

def save_to_neo4j(data, uri, user, password):
    driver = GraphDatabase.driver(uri, auth=(user, password))
    
    with driver.session() as session:
        # Tworzenie węzła dla urządzenia
        equipment_id = session.write_transaction(create_equipment_node, data)
        
        # Tworzenie węzłów dla producenta i relacji
        session.write_transaction(
            create_manufacturer_relation, 
            equipment_id, 
            data["basic_info"]["manufacturer"]
        )
        
        # Tworzenie węzłów dla funkcji i relacji
        for feature in data["features"]:
            session.write_transaction(create_feature_relation, equipment_id, feature)
    
    driver.close()

def create_equipment_node(tx, data):
    result = tx.run("""
        CREATE (e:Equipment {
            model: $model,
            type: $type,
            series: $series,
            release_year: $release_year
        })
        RETURN id(e) as id
    """, {
        "model": data["basic_info"]["model"],
        "type": data["basic_info"]["type"],
        "series": data["basic_info"]["series"],
        "release_year": data["basic_info"]["release_year"]
    })
    
    return result.single()["id"]

def create_manufacturer_relation(tx, equipment_id, manufacturer):
    tx.run("""
        MATCH (e:Equipment) WHERE id(e) = $equipment_id
        MERGE (m:Manufacturer {name: $manufacturer})
        CREATE (m)-[:PRODUCES]->(e)
    """, {
        "equipment_id": equipment_id,
        "manufacturer": manufacturer
    })

def create_feature_relation(tx, equipment_id, feature):
    tx.run("""
        MATCH (e:Equipment) WHERE id(e) = $equipment_id
        MERGE (f:Feature {name: $feature})
        CREATE (e)-[:HAS_FEATURE]->(f)
    """, {
        "equipment_id": equipment_id,
        "feature": feature
    })
```

#### GraphQL

```python
import requests
import json

def save_to_graphql(data, endpoint, headers):
    # Definicja mutacji GraphQL
    mutation = """
    mutation CreateEquipment($input: EquipmentInput!) {
        createEquipment(input: $input) {
            id
            manufacturer {
                id
                name
            }
            model
            type
            features {
                id
                name
            }
        }
    }
    """
    
    # Przygotowanie danych wejściowych
    variables = {
        "input": {
            "manufacturer": data["basic_info"]["manufacturer"],
            "model": data["basic_info"]["model"],
            "type": data["basic_info"]["type"],
            "series": data["basic_info"]["series"],
            "releaseYear": data["basic_info"]["release_year"],
            "technicalSpecs": {
                "coolingCapacity": data["technical_specs"]["cooling_capacity"]["value"],
                "coolingUnit": data["technical_specs"]["cooling_capacity"]["unit"],
                "heatingCapacity": data["technical_specs"]["heating_capacity"]["value"],
                "heatingUnit": data["technical_specs"]["heating_capacity"]["unit"],
                # Pozostałe pola...
            },
            "features": data["features"]
        }
    }
    
    # Wykonanie zapytania
    response = requests.post(
        endpoint,
        json={"query": mutation, "variables": variables},
        headers=headers
    )
    
    return response.json()
```

## Przykładowy kod integracji agentów z crawlerami

Poniżej przedstawiono przykładowy kod integrujący system multi-agentowy (CrewAI) z biblioteką Crawl4AI do automatycznego wzbogacania bazy danych HVAC:

```python
import asyncio
import json
from datetime import datetime
from crewai import Agent, Task, Crew, Process
from crawl4ai import AsyncWebCrawler

# Definicja agentów
researcher_agent = Agent(
    role="Researcher",
    goal="Find the most reliable sources of HVAC equipment technical data",
    backstory="You are an expert in HVAC industry research with deep knowledge of manufacturers and their documentation.",
    verbose=True
)

crawler_agent = Agent(
    role="Web Crawler",
    goal="Extract comprehensive technical data from HVAC equipment websites",
    backstory="You are a specialist in web crawling and data extraction, with expertise in navigating complex technical documentation.",
    verbose=True
)

data_engineer_agent = Agent(
    role="Data Engineer",
    goal="Structure and normalize extracted HVAC data for database storage",
    backstory="You are an experienced data engineer specializing in HVAC systems and database design.",
    verbose=True
)

quality_agent = Agent(
    role="Quality Assurance",
    goal="Ensure accuracy and completeness of extracted HVAC technical data",
    backstory="You are meticulous about data quality and have extensive knowledge of HVAC specifications.",
    verbose=True
)

# Definicja zadań
research_task = Task(
    description="Identify the top 5 HVAC manufacturers and their product documentation websites",
    agent=researcher_agent,
    expected_output="A JSON list of manufacturer names and their documentation URLs"
)

crawl_task = Task(
    description="Crawl the identified websites and extract technical specifications of HVAC equipment",
    agent=crawler_agent,
    expected_output="Raw extracted data in JSON format",
    context=[research_task]
)

structure_task = Task(
    description="Process and structure the raw data into a normalized format suitable for database storage",
    agent=data_engineer_agent,
    expected_output="Structured HVAC data in standardized JSON format",
    context=[crawl_task]
)

validate_task = Task(
    description="Validate the structured data for accuracy, completeness, and consistency",
    agent=quality_agent,
    expected_output="Validation report and final verified data",
    context=[structure_task]
)

# Utworzenie i uruchomienie crew
hvac_crew = Crew(
    agents=[researcher_agent, crawler_agent, data_engineer_agent, quality_agent],
    tasks=[research_task, crawl_task, structure_task, validate_task],
    process=Process.sequential
)

# Funkcja do wykonania crawlowania za pomocą Crawl4AI
async def crawl_manufacturer_site(url):
    async with AsyncWebCrawler() as crawler:
        result = await crawler.arun(
            url=url,
            deep_crawl="bfs",  # Przeszukiwanie wszerz
            max_pages=20,      # Limit stron
            content_selector="main, .product-specs, .technical-data",  # Selektory dla danych technicznych
            fit_markdown=True  # Optymalizacja dla LLM
        )
        return result.markdown

# Funkcja integrująca CrewAI z Crawl4AI
def integrate_crew_with_crawler(manufacturer_urls):
    results = {}
    
    for manufacturer, url in manufacturer_urls.items():
        print(f"Crawling {manufacturer} at {url}...")
        
        # Wykonanie crawlowania asynchronicznie
        markdown_data = asyncio.run(crawl_manufacturer_site(url))
        
        # Zapisanie wyników do pliku dla agenta
        with open(f"{manufacturer}_data.md", "w") as f:
            f.write(markdown_data)
        
        results[manufacturer] = f"{manufacturer}_data.md"
    
    return results

# Główna funkcja
def main():
    # Uruchomienie crew do identyfikacji producentów
    result = hvac_crew.kickoff()
    
    # Parsowanie wyników
    manufacturers = json.loads(result)
    
    # Integracja z crawlerem
    crawled_data_files = integrate_crew_with_crawler(manufacturers)
    
    # Aktualizacja kontekstu dla kolejnych zadań
    crawl_task.context = [research_task, {"crawled_files": crawled_data_files}]
    
    # Kontynuacja pracy crew
    final_result = hvac_crew.continue_from(crawl_task)
    
    # Zapisanie końcowych danych
    with open("hvac_database_update.json", "w") as f:
        json.dump(final_result, f, indent=2)
    
    print("HVAC database enrichment completed successfully!")

if __name__ == "__main__":
    main()
```

## Rekomendowana architektura dla firmy HVAC

Na podstawie przeprowadzonej analizy, rekomendujemy następującą architekturę do automatycznego wzbogacania bazy danych HVAC:

### 1. Biblioteka do crawlowania: Crawl4AI

Crawl4AI oferuje najlepsze wsparcie dla integracji z agentami AI, ekstrakcji danych strukturalnych i obsługi dynamicznych stron internetowych, co jest kluczowe dla stron producentów HVAC.

### 2. System multi-agentowy: CrewAI

CrewAI zapewnia intuicyjny interfejs do definiowania ról agentów i zadań, co ułatwia implementację złożonych przepływów pracy związanych z wzbogacaniem bazy danych.

### 3. Baza danych: Hybrydowe podejście

- **PostgreSQL** - dla podstawowych danych transakcyjnych i relacyjnych
- **MongoDB** - dla przechowywania niestrukturalnych danych, takich jak pełne specyfikacje techniczne
- **Neo4j** - dla modelowania relacji między urządzeniami, producentami i funkcjami
- **GraphQL** - jako jednolity interfejs dostępu do danych

### 4. Przepływ pracy

1. **Identyfikacja źródeł** - agenci identyfikują wiarygodne źródła danych HVAC
2. **Crawlowanie** - Crawl4AI zbiera dane z zidentyfikowanych źródeł
3. **Ekstrakcja** - agenci ekstrahują dane techniczne z zebranych treści
4. **Normalizacja** - dane są normalizowane do standardowego formatu
5. **Walidacja** - agenci weryfikują poprawność i kompletność danych
6. **Integracja** - dane są zapisywane w odpowiednich bazach danych
7. **Raportowanie** - generowanie raportów o nowych lub zaktualizowanych danych

### 5. Korzyści z hostowania lokalnego

- **Bezpieczeństwo danych** - wszystkie dane pozostają w infrastrukturze firmy
- **Kontrola nad procesem** - pełna kontrola nad częstotliwością i zakresem crawlowania
- **Niższe koszty** - brak opłat za API lub usługi zewnętrzne
- **Elastyczność** - możliwość dostosowania do specyficznych potrzeb firmy HVAC
- **Integracja z istniejącymi systemami** - łatwiejsza integracja z wewnętrznymi systemami firmy

## Wnioski

Integracja systemów multi-agentowych z bibliotekami do crawlowania oferuje potężne narzędzie do automatycznego wzbogacania bazy danych o urządzenia HVAC i dane techniczne. Dzięki lokalnie hostowanym rozwiązaniom w Pythonie, firma HVAC może efektywnie zbierać, przetwarzać i przechowywać dane techniczne, co przekłada się na lepszą obsługę klientów, efektywniejsze zarządzanie zasobami i przewagę konkurencyjną.

Rekomendowana architektura oparta na Crawl4AI i CrewAI zapewnia elastyczność, skalowalność i wydajność, jednocześnie minimalizując koszty i maksymalizując kontrolę nad danymi. Hybrydowe podejście do baz danych pozwala na optymalne przechowywanie różnych typów danych i efektywne zarządzanie relacjami między nimi.
