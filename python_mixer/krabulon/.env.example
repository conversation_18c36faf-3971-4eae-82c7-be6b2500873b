# Krabulon Environment Configuration
# Copy this file to .env and update with your values

# Required - OpenAI API Configuration
OPENAI_API_KEY=your-openai-api-key-here

# Required - Security
SECRET_KEY=your-secret-key-change-in-production

# Database Configuration - PostgreSQL (GoBackend-Kratos Production)
DATABASE_HOST=**************
DATABASE_PORT=5432
DATABASE_NAME=hvacdb
DATABASE_USER=hvacdb
DATABASE_PASSWORD=blaeritipol

# Database Configuration - MongoDB
MONGODB_HOST=localhost
MONGODB_PORT=27017
MONGODB_DATABASE=hvac_equipment
MONGODB_USERNAME=
MONGODB_PASSWORD=

# Database Configuration - Neo4j
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=password

# Database Configuration - Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DATABASE=0
REDIS_PASSWORD=

# MinIO Object Storage (GoBackend-Kratos Production)
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1
MINIO_USE_SSL=false
MINIO_BUCKET=dolores-attachments

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=false

# GoBackend-Kratos Integration
GOBACKEND_HOST=**************
GOBACKEND_PORT=8080
GOBACKEND_API_URL=http://**************:8080
GOBACKEND_GRPC_PORT=9090
GOBACKEND_HEALTH_CHECK_URL=http://**************:8080/health

# AI Configuration (GoBackend-Kratos AI Services)
BIELIK_V3_URL=http://**************:8877
GEMMA3_URL=http://**************:8878
GEMMA4_URL=http://**************:8879
AI_TEMPERATURE=0.1
AI_MAX_TOKENS=4096

# Email Processing Configuration
IMAP_HOST=imap.gmail.com
IMAP_PORT=993
IMAP_USERNAME=<EMAIL>
IMAP_PASSWORD=your-email-password
IMAP_USE_SSL=true

# Transcription Email Configuration
TRANSCRIPTION_EMAIL=<EMAIL>
CUSTOMER_EMAIL=<EMAIL>

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/krabulon.log

# Crawling Configuration
CRAWLING_MAX_PAGES=50
CRAWLING_DELAY=1.0
CRAWLING_TIMEOUT=30
CRAWLING_USER_AGENT=Krabulon HVAC Bot 1.0

# Monitoring Configuration
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090

# Development Configuration
RELOAD=false
WORKERS=1
