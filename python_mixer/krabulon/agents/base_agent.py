"""Base agent class for all HVAC data processing agents."""

import asyncio
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from datetime import datetime
from loguru import logger

from crewai import Agent, Task
from langchain.llms import OpenAI
from langchain.chat_models import Chat<PERSON>penAI

from ..config import settings
from ..models import HVACEquipment


class BaseAgent(ABC):
    """Base class for all HVAC data processing agents."""
    
    def __init__(
        self,
        name: str,
        role: str,
        goal: str,
        backstory: str,
        verbose: bool = True,
        memory: bool = True,
        **kwargs
    ):
        """Initialize base agent.
        
        Args:
            name: Agent name
            role: Agent role description
            goal: Agent goal
            backstory: Agent backstory
            verbose: Enable verbose logging
            memory: Enable memory
            **kwargs: Additional agent parameters
        """
        self.name = name
        self.role = role
        self.goal = goal
        self.backstory = backstory
        self.verbose = verbose
        self.memory = memory
        
        # Initialize LLM
        self.llm = self._initialize_llm()
        
        # Initialize CrewAI agent
        self.agent = Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            verbose=verbose,
            memory=memory,
            llm=self.llm,
            **kwargs
        )
        
        # Agent state
        self.state = {
            "initialized": True,
            "last_execution": None,
            "execution_count": 0,
            "errors": []
        }
        
        logger.info(f"Initialized {self.name} agent")
    
    def _initialize_llm(self):
        """Initialize the language model."""
        try:
            return ChatOpenAI(
                model=settings.ai.openai_model,
                temperature=settings.ai.openai_temperature,
                max_tokens=settings.ai.openai_max_tokens,
                openai_api_key=settings.ai.openai_api_key
            )
        except Exception as e:
            logger.error(f"Failed to initialize LLM: {e}")
            # Fallback to basic OpenAI
            return OpenAI(
                model_name="gpt-3.5-turbo-instruct",
                openai_api_key=settings.ai.openai_api_key
            )
    
    @abstractmethod
    async def execute(self, input_data: Any, **kwargs) -> Any:
        """Execute the agent's main task.
        
        Args:
            input_data: Input data for processing
            **kwargs: Additional execution parameters
            
        Returns:
            Processed output data
        """
        pass
    
    async def run_task(self, task_description: str, context: Optional[Dict] = None) -> Any:
        """Run a specific task using the agent.
        
        Args:
            task_description: Description of the task to execute
            context: Optional context data
            
        Returns:
            Task execution result
        """
        try:
            self.state["execution_count"] += 1
            self.state["last_execution"] = datetime.now()
            
            # Create task
            task = Task(
                description=task_description,
                agent=self.agent,
                context=context or {}
            )
            
            # Execute task
            logger.info(f"Executing task for {self.name}: {task_description[:100]}...")
            result = await asyncio.to_thread(task.execute)
            
            logger.info(f"Task completed successfully for {self.name}")
            return result
            
        except Exception as e:
            error_msg = f"Task execution failed for {self.name}: {str(e)}"
            logger.error(error_msg)
            self.state["errors"].append({
                "timestamp": datetime.now(),
                "error": str(e),
                "task": task_description[:100]
            })
            raise
    
    def get_state(self) -> Dict[str, Any]:
        """Get current agent state."""
        return self.state.copy()
    
    def reset_state(self) -> None:
        """Reset agent state."""
        self.state = {
            "initialized": True,
            "last_execution": None,
            "execution_count": 0,
            "errors": []
        }
        logger.info(f"Reset state for {self.name}")
    
    def validate_input(self, input_data: Any, required_fields: List[str]) -> bool:
        """Validate input data has required fields.
        
        Args:
            input_data: Data to validate
            required_fields: List of required field names
            
        Returns:
            True if valid, False otherwise
        """
        if not isinstance(input_data, dict):
            return False
        
        for field in required_fields:
            if field not in input_data or input_data[field] is None:
                logger.warning(f"Missing required field '{field}' in input data")
                return False
        
        return True
    
    def format_output(self, data: Any, output_type: str = "dict") -> Any:
        """Format output data according to specified type.
        
        Args:
            data: Data to format
            output_type: Output format type
            
        Returns:
            Formatted data
        """
        if output_type == "dict" and hasattr(data, "dict"):
            return data.dict()
        elif output_type == "json" and hasattr(data, "json"):
            return data.json()
        elif output_type == "list" and not isinstance(data, list):
            return [data]
        
        return data
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform agent health check.
        
        Returns:
            Health status information
        """
        try:
            # Test LLM connection
            test_response = await asyncio.to_thread(
                self.llm.predict,
                "Test connection. Respond with 'OK'."
            )
            
            llm_healthy = "OK" in test_response or "ok" in test_response.lower()
            
            return {
                "agent": self.name,
                "healthy": llm_healthy,
                "state": self.get_state(),
                "llm_model": settings.ai.openai_model,
                "last_check": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Health check failed for {self.name}: {e}")
            return {
                "agent": self.name,
                "healthy": False,
                "error": str(e),
                "state": self.get_state(),
                "last_check": datetime.now().isoformat()
            }
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.__class__.__name__}(name='{self.name}', role='{self.role}')"
    
    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.name}', "
            f"role='{self.role}', "
            f"executions={self.state['execution_count']}, "
            f"errors={len(self.state['errors'])}"
            f")"
        )
