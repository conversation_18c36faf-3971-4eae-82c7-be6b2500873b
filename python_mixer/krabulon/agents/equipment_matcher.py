"""Equipment Matcher Agent for matching customer requirements to HVAC equipment."""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent


class EquipmentMatcherAgent(BaseAgent):
    """Agent responsible for matching customer requirements to appropriate HVAC equipment."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="EquipmentMatcher",
            role="HVAC Equipment Matching Specialist",
            goal="Match customer requirements to optimal HVAC equipment configurations",
            backstory=(
                "You are an expert HVAC equipment specialist with comprehensive knowledge "
                "of LG, Daikin, and Mitsubishi product lines. You understand technical "
                "specifications, installation requirements, and can match customer needs "
                "to the perfect equipment combination. Your recommendations consider "
                "performance, efficiency, cost, and long-term value."
            ),
            **kwargs
        )
        
        # LG Equipment Database (focused on popular models)
        self.lg_equipment = {
            "split_systems": [
                {
                    "model": "S12ET",
                    "series": "Dual Cool",
                    "type": "split_system",
                    "cooling_capacity_kw": 3.5,
                    "heating_capacity_kw": 4.0,
                    "energy_class": "A++",
                    "seer": 16.0,
                    "scop": 4.1,
                    "refrigerant": "R32",
                    "features": ["WiFi", "Dual Cool Technology", "Inverter", "Gold Fin"],
                    "price_pln": 2800,
                    "room_size_m2": "25-35",
                    "noise_level_db": 19
                },
                {
                    "model": "S18ET",
                    "series": "Dual Cool",
                    "type": "split_system", 
                    "cooling_capacity_kw": 5.3,
                    "heating_capacity_kw": 5.8,
                    "energy_class": "A++",
                    "seer": 15.8,
                    "scop": 4.0,
                    "refrigerant": "R32",
                    "features": ["WiFi", "Dual Cool Technology", "Inverter", "Gold Fin"],
                    "price_pln": 3200,
                    "room_size_m2": "35-50",
                    "noise_level_db": 21
                },
                {
                    "model": "G12WL",
                    "series": "Gallery",
                    "type": "split_system",
                    "cooling_capacity_kw": 3.5,
                    "heating_capacity_kw": 4.0,
                    "energy_class": "A+++",
                    "seer": 18.5,
                    "scop": 4.6,
                    "refrigerant": "R32",
                    "features": ["WiFi", "Gallery Design", "Inverter", "Air Purification", "Smart Diagnosis"],
                    "price_pln": 4200,
                    "room_size_m2": "25-35",
                    "noise_level_db": 17
                }
            ],
            "multi_split": [
                {
                    "model": "MU2M15",
                    "series": "Multi Split",
                    "type": "multi_split",
                    "cooling_capacity_kw": 4.2,
                    "heating_capacity_kw": 4.6,
                    "energy_class": "A++",
                    "refrigerant": "R32",
                    "max_indoor_units": 2,
                    "features": ["Inverter", "Individual Control", "WiFi Ready"],
                    "price_pln": 5500,
                    "suitable_rooms": 2
                },
                {
                    "model": "MU3M21",
                    "series": "Multi Split",
                    "type": "multi_split",
                    "cooling_capacity_kw": 6.1,
                    "heating_capacity_kw": 6.8,
                    "energy_class": "A++",
                    "refrigerant": "R32",
                    "max_indoor_units": 3,
                    "features": ["Inverter", "Individual Control", "WiFi Ready"],
                    "price_pln": 7200,
                    "suitable_rooms": 3
                }
            ]
        }
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute equipment matching based on customer requirements.
        
        Args:
            input_data: Customer requirements and matching parameters
            **kwargs: Additional matching parameters
            
        Returns:
            Equipment matching results with recommendations
        """
        try:
            logger.info("Starting equipment matching process")
            
            # Validate input
            if not self.validate_input(input_data, ["equipment_requirements", "preferences_analysis"]):
                raise ValueError("Invalid input data for equipment matching")
            
            equipment_requirements = input_data["equipment_requirements"]
            preferences_analysis = input_data["preferences_analysis"]
            business_analysis = input_data.get("business_analysis", {})
            matching_config = input_data.get("config", {})
            
            # Generate equipment recommendations
            equipment_recommendations = await self._generate_equipment_recommendations(
                equipment_requirements,
                preferences_analysis,
                business_analysis,
                matching_config
            )
            
            # Calculate installation requirements
            installation_analysis = await self._analyze_installation_requirements(
                equipment_recommendations,
                equipment_requirements
            )
            
            # Generate pricing analysis
            pricing_analysis = await self._generate_pricing_analysis(
                equipment_recommendations,
                business_analysis,
                preferences_analysis
            )
            
            # Compile matching results
            matching_results = {
                "matching_summary": {
                    "recommendations_count": len(equipment_recommendations),
                    "matching_confidence": self._calculate_matching_confidence(equipment_recommendations),
                    "total_estimated_cost": pricing_analysis.get("total_cost_range", {}),
                    "matching_date": datetime.now().isoformat(),
                    "matcher_version": "1.0.0"
                },
                "equipment_recommendations": equipment_recommendations,
                "installation_analysis": installation_analysis,
                "pricing_analysis": pricing_analysis,
                "alternative_options": await self._generate_alternative_options(
                    equipment_recommendations, equipment_requirements
                )
            }
            
            logger.info(f"Equipment matching completed: {len(equipment_recommendations)} recommendations")
            return matching_results
            
        except Exception as e:
            logger.error(f"Equipment matching failed: {e}")
            raise
    
    async def _generate_equipment_recommendations(
        self,
        requirements: Dict[str, Any],
        preferences: Dict[str, Any],
        business: Dict[str, Any],
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate equipment recommendations based on requirements."""
        
        # Extract key requirements
        system_type = requirements.get("system_type", "split")
        capacity_reqs = requirements.get("capacity_requirements", {})
        performance_reqs = requirements.get("performance_requirements", {})
        feature_reqs = requirements.get("feature_requirements", {})
        budget_constraints = requirements.get("budget_constraints", {})
        
        # Extract preferences
        brand_prefs = preferences.get("brand_preferences", {})
        feature_priorities = preferences.get("feature_priorities", {})
        
        # Generate recommendations using AI
        recommendation_prompt = f"""
        Generate 3 HVAC equipment recommendations based on customer requirements and preferences.
        
        Requirements:
        - System Type: {system_type}
        - Cooling Capacity: {capacity_reqs.get("cooling_capacity_kw", "not specified")} kW
        - Room Count: {capacity_reqs.get("estimated_room_count", "not specified")}
        - Total Area: {capacity_reqs.get("total_area_m2", "not specified")} m²
        - Energy Efficiency: {performance_reqs.get("energy_efficiency_class", "any")}
        - Budget Range: {budget_constraints.get("budget_range", "unspecified")}
        
        Preferences:
        - Preferred Brands: {brand_prefs.get("preferred_brands", ["any"])}
        - Energy Efficiency Priority: {feature_priorities.get("energy_efficiency", "important")}
        - Quiet Operation Priority: {feature_priorities.get("quiet_operation", "important")}
        - Smart Features Priority: {feature_priorities.get("smart_features", "nice_to_have")}
        
        Available LG Equipment Database:
        {json.dumps(self.lg_equipment, indent=2)}
        
        Generate 3 recommendations in JSON format:
        [
            {{
                "recommendation_rank": 1,
                "equipment": {{
                    "manufacturer": "LG",
                    "model": "specific model from database",
                    "series": "product series",
                    "type": "split_system|multi_split|vrf",
                    "cooling_capacity_kw": number,
                    "heating_capacity_kw": number,
                    "energy_class": "A+++|A++|A+|A",
                    "seer": number,
                    "scop": number,
                    "refrigerant": "R32|R410A",
                    "features": ["list", "of", "features"],
                    "noise_level_db": number,
                    "estimated_price_pln": number
                }},
                "suitability_analysis": {{
                    "capacity_match": "perfect|good|adequate|insufficient",
                    "efficiency_match": "excellent|good|adequate|poor",
                    "feature_match": "excellent|good|adequate|poor",
                    "budget_match": "within|slightly_over|over|well_over",
                    "overall_score": 0.0-1.0
                }},
                "installation_requirements": {{
                    "indoor_units_count": number,
                    "outdoor_units_count": number,
                    "piping_length_estimate": "standard|extended|complex",
                    "electrical_requirements": "standard|upgraded",
                    "installation_complexity": "simple|moderate|complex",
                    "estimated_installation_hours": number
                }},
                "value_proposition": {{
                    "primary_benefits": ["main", "selling", "points"],
                    "energy_savings_annual": "estimated annual savings in PLN",
                    "warranty_coverage": "standard|extended",
                    "maintenance_requirements": "low|medium|high"
                }},
                "recommendation_reason": "Why this equipment is recommended for this customer"
            }}
        ]
        
        Focus on LG equipment from the database. Ensure recommendations match customer requirements and preferences.
        Rank by overall suitability considering performance, features, and value.
        """
        
        try:
            response = await self.run_task(recommendation_prompt)
            recommendations = json.loads(response)
            
            # Validate and enhance recommendations
            enhanced_recommendations = []
            for rec in recommendations:
                enhanced_rec = await self._enhance_recommendation(rec, requirements, preferences)
                enhanced_recommendations.append(enhanced_rec)
            
            return enhanced_recommendations
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to generate equipment recommendations: {e}")
            
            # Fallback to basic recommendations
            return await self._generate_fallback_recommendations(requirements, preferences)
    
    async def _enhance_recommendation(
        self,
        recommendation: Dict[str, Any],
        requirements: Dict[str, Any],
        preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Enhance recommendation with additional analysis."""
        
        # Add compatibility analysis
        recommendation["compatibility_analysis"] = await self._analyze_compatibility(
            recommendation, requirements
        )
        
        # Add competitive analysis
        recommendation["competitive_analysis"] = await self._analyze_competitive_position(
            recommendation
        )
        
        # Add lifecycle analysis
        recommendation["lifecycle_analysis"] = await self._analyze_equipment_lifecycle(
            recommendation
        )
        
        # Add customization options
        recommendation["customization_options"] = await self._generate_customization_options(
            recommendation, preferences
        )
        
        return recommendation
    
    async def _analyze_compatibility(
        self,
        recommendation: Dict[str, Any],
        requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze equipment compatibility with requirements."""
        
        equipment = recommendation.get("equipment", {})
        capacity_reqs = requirements.get("capacity_requirements", {})
        performance_reqs = requirements.get("performance_requirements", {})
        
        compatibility = {
            "capacity_compatibility": "excellent",
            "performance_compatibility": "excellent",
            "feature_compatibility": "good",
            "installation_compatibility": "good",
            "compatibility_score": 0.9,
            "compatibility_notes": []
        }
        
        # Check capacity compatibility
        required_cooling = capacity_reqs.get("cooling_capacity_kw")
        equipment_cooling = equipment.get("cooling_capacity_kw")
        
        if required_cooling and equipment_cooling:
            if equipment_cooling >= required_cooling * 0.9:
                compatibility["capacity_compatibility"] = "excellent"
            elif equipment_cooling >= required_cooling * 0.8:
                compatibility["capacity_compatibility"] = "good"
            else:
                compatibility["capacity_compatibility"] = "insufficient"
                compatibility["compatibility_notes"].append("Cooling capacity may be insufficient")
        
        return compatibility
    
    async def _analyze_competitive_position(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze competitive position of recommended equipment."""
        
        return {
            "market_position": "premium",
            "competitive_advantages": [
                "Advanced inverter technology",
                "Superior energy efficiency",
                "Comprehensive warranty",
                "Smart connectivity features"
            ],
            "competitive_disadvantages": [
                "Higher initial cost",
                "Premium positioning"
            ],
            "market_alternatives": [
                {"brand": "Daikin", "model": "FTXM35R", "price_difference": "+15%"},
                {"brand": "Mitsubishi", "model": "MSZ-LN35VG", "price_difference": "+25%"}
            ]
        }
    
    async def _analyze_equipment_lifecycle(self, recommendation: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze equipment lifecycle and long-term value."""
        
        equipment = recommendation.get("equipment", {})
        
        return {
            "expected_lifespan_years": 15,
            "maintenance_schedule": {
                "annual_service": "recommended",
                "filter_replacement": "every 3 months",
                "refrigerant_check": "every 2 years"
            },
            "energy_efficiency_degradation": "minimal over 10 years",
            "upgrade_path": "compatible with future smart home systems",
            "resale_value": "high due to premium brand",
            "total_cost_of_ownership": {
                "initial_cost": equipment.get("estimated_price_pln", 0),
                "annual_energy_cost": 800,
                "annual_maintenance_cost": 300,
                "15_year_total": equipment.get("estimated_price_pln", 0) + (15 * 1100)
            }
        }
    
    async def _generate_customization_options(
        self,
        recommendation: Dict[str, Any],
        preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate customization options for the recommendation."""
        
        return {
            "indoor_unit_options": [
                {"type": "wall_mounted", "design": "standard", "price_difference": 0},
                {"type": "wall_mounted", "design": "premium", "price_difference": 500},
                {"type": "ceiling_cassette", "design": "4_way", "price_difference": 800}
            ],
            "control_options": [
                {"type": "basic_remote", "features": ["temperature", "mode"], "price_difference": 0},
                {"type": "wifi_module", "features": ["smartphone_control", "scheduling"], "price_difference": 300},
                {"type": "smart_thermostat", "features": ["learning", "geofencing"], "price_difference": 800}
            ],
            "installation_options": [
                {"type": "standard", "description": "Basic installation", "price_difference": 0},
                {"type": "premium", "description": "Includes piping cover and extended warranty", "price_difference": 500},
                {"type": "smart_home", "description": "Integration with home automation", "price_difference": 1000}
            ],
            "service_packages": [
                {"type": "basic", "description": "1 year warranty", "annual_cost": 0},
                {"type": "extended", "description": "3 year warranty + annual service", "annual_cost": 400},
                {"type": "premium", "description": "5 year warranty + bi-annual service", "annual_cost": 600}
            ]
        }
    
    async def _analyze_installation_requirements(
        self,
        recommendations: List[Dict[str, Any]],
        requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze installation requirements for recommendations."""
        
        installation_reqs = requirements.get("installation_requirements", {})
        
        return {
            "site_survey_required": True,
            "common_requirements": {
                "electrical_upgrade": "may be required for multi-split systems",
                "drainage": "condensate drain required for all indoor units",
                "ventilation": "adequate outdoor unit ventilation space needed",
                "structural": "wall mounting points must support unit weight"
            },
            "installation_timeline": {
                "site_survey": "1-2 hours",
                "equipment_delivery": "3-7 days after order",
                "installation": "4-8 hours depending on system complexity",
                "commissioning": "1-2 hours for testing and setup"
            },
            "permits_required": {
                "building_permit": "not typically required for residential",
                "electrical_permit": "may be required for electrical upgrades",
                "refrigerant_handling": "certified technician required"
            },
            "preparation_checklist": [
                "Clear access to installation areas",
                "Ensure electrical panel accessibility",
                "Remove furniture from installation zones",
                "Confirm outdoor unit placement location"
            ]
        }
    
    async def _generate_pricing_analysis(
        self,
        recommendations: List[Dict[str, Any]],
        business_analysis: Dict[str, Any],
        preferences: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive pricing analysis."""
        
        # Calculate price ranges
        equipment_prices = [
            rec.get("equipment", {}).get("estimated_price_pln", 0)
            for rec in recommendations
        ]
        
        min_price = min(equipment_prices) if equipment_prices else 0
        max_price = max(equipment_prices) if equipment_prices else 0
        
        # Estimate installation costs
        installation_cost_base = 1500  # Base installation cost
        installation_cost_range = {
            "min": installation_cost_base,
            "max": installation_cost_base * 1.5
        }
        
        return {
            "equipment_cost_range": {
                "min_pln": min_price,
                "max_pln": max_price,
                "currency": "PLN"
            },
            "installation_cost_range": installation_cost_range,
            "total_cost_range": {
                "min_pln": min_price + installation_cost_range["min"],
                "max_pln": max_price + installation_cost_range["max"],
                "currency": "PLN"
            },
            "financing_options": {
                "cash_discount": "3% discount for full payment",
                "installment_12_months": "0% interest for 12 months",
                "installment_24_months": "2.9% APR for 24 months",
                "lease_option": "available for commercial customers"
            },
            "value_analysis": {
                "cost_per_kw_cooling": min_price / max(1, min([
                    rec.get("equipment", {}).get("cooling_capacity_kw", 1)
                    for rec in recommendations
                ])),
                "energy_payback_period": "3-5 years through energy savings",
                "warranty_value": "comprehensive coverage included"
            },
            "competitive_pricing": {
                "market_position": "competitive with premium brands",
                "value_proposition": "superior efficiency and features justify premium",
                "price_sensitivity_analysis": business_analysis.get("risk_factors", {}).get("price_sensitivity_risk", "medium")
            }
        }
    
    async def _generate_alternative_options(
        self,
        primary_recommendations: List[Dict[str, Any]],
        requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate alternative equipment options."""
        
        return {
            "budget_alternatives": [
                {
                    "description": "Entry-level LG split system",
                    "model": "S09ET Standard",
                    "price_reduction": "20-30% lower cost",
                    "trade_offs": ["Basic features", "Lower efficiency rating"]
                }
            ],
            "premium_upgrades": [
                {
                    "description": "LG Gallery series with advanced features",
                    "model": "G12WL Gallery",
                    "price_increase": "30-40% higher cost",
                    "additional_benefits": ["Premium design", "Air purification", "Ultra-quiet operation"]
                }
            ],
            "system_alternatives": [
                {
                    "description": "Multi-split system for multiple rooms",
                    "benefits": ["Individual room control", "Single outdoor unit"],
                    "considerations": ["Higher initial cost", "More complex installation"]
                }
            ],
            "future_upgrade_path": {
                "smart_home_integration": "WiFi modules can be added later",
                "additional_zones": "System can be expanded with multi-split outdoor units",
                "air_quality_upgrades": "Air purification modules available"
            }
        }
    
    def _calculate_matching_confidence(self, recommendations: List[Dict[str, Any]]) -> float:
        """Calculate overall confidence in equipment matching."""
        
        if not recommendations:
            return 0.0
        
        # Average the overall scores from recommendations
        scores = [
            rec.get("suitability_analysis", {}).get("overall_score", 0.5)
            for rec in recommendations
        ]
        
        return sum(scores) / len(scores) if scores else 0.5
    
    async def _generate_fallback_recommendations(
        self,
        requirements: Dict[str, Any],
        preferences: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate fallback recommendations when AI fails."""
        
        # Simple rule-based recommendations
        capacity_needed = requirements.get("capacity_requirements", {}).get("cooling_capacity_kw", 3.5)
        
        # Select appropriate model based on capacity
        if capacity_needed <= 3.5:
            selected_model = self.lg_equipment["split_systems"][0]  # S12ET
        elif capacity_needed <= 5.0:
            selected_model = self.lg_equipment["split_systems"][1]  # S18ET
        else:
            selected_model = self.lg_equipment["split_systems"][2]  # G12WL
        
        return [
            {
                "recommendation_rank": 1,
                "equipment": selected_model,
                "suitability_analysis": {
                    "capacity_match": "good",
                    "efficiency_match": "good",
                    "feature_match": "good",
                    "budget_match": "within",
                    "overall_score": 0.8
                },
                "installation_requirements": {
                    "indoor_units_count": 1,
                    "outdoor_units_count": 1,
                    "installation_complexity": "simple",
                    "estimated_installation_hours": 4
                },
                "value_proposition": {
                    "primary_benefits": ["Energy efficient", "Reliable", "Quiet operation"],
                    "warranty_coverage": "standard"
                },
                "recommendation_reason": "Fallback recommendation based on capacity requirements"
            }
        ]
