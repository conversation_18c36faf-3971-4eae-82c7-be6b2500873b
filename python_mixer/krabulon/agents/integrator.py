"""Integrator Agent for saving validated HVAC data to databases."""

from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

from .base_agent import BaseAgent
from ..database import DatabaseManager


class IntegratorAgent(BaseAgent):
    """Agent responsible for integrating validated HVAC data into databases."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Integrator",
            role="Database Integration Specialist",
            goal="Efficiently integrate validated HVAC equipment data into multiple database systems",
            backstory=(
                "You are an expert in database integration and data management. "
                "You understand how to efficiently store HVAC equipment data across "
                "different database systems while maintaining data integrity, handling "
                "duplicates, and optimizing for both storage and retrieval performance."
            ),
            **kwargs
        )
        
        # Initialize database manager
        self.database_manager = DatabaseManager()
        self._initialized = False
    
    async def initialize_databases(self):
        """Initialize database connections."""
        if not self._initialized:
            await self.database_manager.initialize()
            self._initialized = True
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute database integration for validated HVAC data.
        
        Args:
            input_data: Validated equipment data and integration configuration
            **kwargs: Additional integration parameters
            
        Returns:
            Integration results and database operation summary
        """
        try:
            logger.info("Starting database integration")
            
            # Validate input
            if not self.validate_input(input_data, ["validated_equipment"]):
                raise ValueError("Invalid input data for integration")
            
            # Initialize databases if needed
            await self.initialize_databases()
            
            validated_equipment = input_data["validated_equipment"]
            integration_config = input_data.get("integration_config", {})
            
            # Process equipment data for integration
            processed_equipment = await self._process_equipment_for_integration(
                validated_equipment,
                integration_config
            )
            
            # Execute database operations
            database_results = await self._execute_database_operations(
                processed_equipment,
                integration_config
            )
            
            # Generate integration report
            integration_report = await self._generate_integration_report(
                processed_equipment,
                database_results,
                integration_config
            )
            
            # Compile final results
            integration_results = {
                "integration_summary": {
                    "total_equipment_processed": len(validated_equipment),
                    "equipment_integrated": len(processed_equipment),
                    "integration_rate": len(processed_equipment) / max(1, len(validated_equipment)),
                    "timestamp": datetime.now().isoformat(),
                    "integrator_version": "1.0.0"
                },
                "database_results": database_results,
                "integration_report": integration_report,
                "processed_equipment": processed_equipment
            }
            
            logger.info(f"Database integration completed: {len(processed_equipment)} equipment items")
            return integration_results
            
        except Exception as e:
            logger.error(f"Database integration failed: {e}")
            raise
    
    async def _process_equipment_for_integration(
        self,
        validated_equipment: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Process validated equipment data for database integration."""
        
        processed_equipment = []
        duplicate_handling = config.get("duplicate_handling", "merge")
        update_existing = config.get("update_existing", True)
        
        for equipment in validated_equipment:
            try:
                # Generate unique identifier if not present
                if not equipment.get("id"):
                    equipment["id"] = self._generate_equipment_id(equipment)
                
                # Check for duplicates if enabled
                if duplicate_handling != "ignore":
                    existing_equipment = await self._find_existing_equipment(equipment)
                    
                    if existing_equipment:
                        if duplicate_handling == "merge":
                            equipment = await self._merge_equipment_data(existing_equipment, equipment)
                        elif duplicate_handling == "skip":
                            logger.info(f"Skipping duplicate equipment: {equipment.get('id')}")
                            continue
                        elif duplicate_handling == "replace" and update_existing:
                            # Mark for replacement
                            equipment["_operation"] = "replace"
                        else:
                            logger.info(f"Duplicate found but not updating: {equipment.get('id')}")
                            continue
                
                # Add integration metadata
                equipment["integration_metadata"] = {
                    "integrated_at": datetime.now().isoformat(),
                    "integrator": "IntegratorAgent",
                    "operation": equipment.get("_operation", "insert"),
                    "duplicate_handling": duplicate_handling
                }
                
                processed_equipment.append(equipment)
                
            except Exception as e:
                logger.error(f"Failed to process equipment for integration: {e}")
        
        return processed_equipment
    
    async def _execute_database_operations(
        self,
        processed_equipment: List[Dict[str, Any]],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute database operations for processed equipment."""
        
        database_results = {
            "postgresql": {"saved": 0, "updated": 0, "errors": 0, "error_details": []},
            "mongodb": {"saved": 0, "updated": 0, "errors": 0, "error_details": []},
            "neo4j": {"saved": 0, "updated": 0, "errors": 0, "error_details": []},
            "total_operations": len(processed_equipment)
        }
        
        # Batch processing
        batch_size = config.get("batch_size", 50)
        dry_run = config.get("dry_run", False)
        
        if dry_run:
            logger.info("DRY RUN MODE - No data will be saved to databases")
            return database_results
        
        # Process in batches
        for i in range(0, len(processed_equipment), batch_size):
            batch = processed_equipment[i:i + batch_size]
            
            try:
                # Save to PostgreSQL
                if self.database_manager.postgresql:
                    pg_result = await self.database_manager.postgresql.save_equipment(batch)
                    database_results["postgresql"]["saved"] += pg_result.get("saved", 0)
                    database_results["postgresql"]["updated"] += pg_result.get("updated", 0)
                    database_results["postgresql"]["errors"] += pg_result.get("errors", 0)
                    database_results["postgresql"]["error_details"].extend(
                        pg_result.get("error_details", [])
                    )
                
                # Save to MongoDB
                if self.database_manager.mongodb:
                    mongo_result = await self.database_manager.mongodb.save_equipment(batch)
                    database_results["mongodb"]["saved"] += mongo_result.get("saved", 0)
                    database_results["mongodb"]["updated"] += mongo_result.get("updated", 0)
                    database_results["mongodb"]["errors"] += mongo_result.get("errors", 0)
                    database_results["mongodb"]["error_details"].extend(
                        mongo_result.get("error_details", [])
                    )
                
                # Save to Neo4j
                if self.database_manager.neo4j:
                    neo4j_result = await self.database_manager.neo4j.save_equipment(batch)
                    database_results["neo4j"]["saved"] += neo4j_result.get("saved", 0)
                    database_results["neo4j"]["updated"] += neo4j_result.get("updated", 0)
                    database_results["neo4j"]["errors"] += neo4j_result.get("errors", 0)
                    database_results["neo4j"]["error_details"].extend(
                        neo4j_result.get("error_details", [])
                    )
                
                logger.info(f"Processed batch {i//batch_size + 1}/{(len(processed_equipment)-1)//batch_size + 1}")
                
            except Exception as e:
                logger.error(f"Failed to process batch {i//batch_size + 1}: {e}")
                # Add error to all database results
                for db_name in ["postgresql", "mongodb", "neo4j"]:
                    database_results[db_name]["errors"] += len(batch)
                    database_results[db_name]["error_details"].append(f"Batch error: {str(e)}")
        
        return database_results
    
    async def _find_existing_equipment(self, equipment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Find existing equipment in database."""
        
        try:
            if not self.database_manager.postgresql:
                return None
            
            basic_info = equipment.get("basic_info", {})
            manufacturer = basic_info.get("manufacturer")
            model = basic_info.get("model")
            equipment_type = basic_info.get("type")
            
            if not all([manufacturer, model, equipment_type]):
                return None
            
            # Search for existing equipment
            existing_list = await self.database_manager.postgresql.get_equipment(
                manufacturer=manufacturer,
                equipment_type=equipment_type,
                limit=10
            )
            
            # Find exact match
            for existing in existing_list:
                if (existing.get("manufacturer", "").lower() == manufacturer.lower() and
                    existing.get("model", "").lower() == model.lower() and
                    existing.get("type", "").lower() == equipment_type.lower()):
                    return existing
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to find existing equipment: {e}")
            return None
    
    async def _merge_equipment_data(
        self,
        existing: Dict[str, Any],
        new: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Merge new equipment data with existing data."""
        
        merge_prompt = f"""
        Merge the following HVAC equipment data, prioritizing more complete and recent information.
        
        Existing equipment data:
        {existing}
        
        New equipment data:
        {new}
        
        Merge rules:
        1. Keep the most complete technical specifications
        2. Prefer higher confidence scores
        3. Combine features and certifications (remove duplicates)
        4. Use the most recent extraction date
        5. Preserve the original ID from existing data
        6. Update confidence score based on data completeness
        
        Return the merged equipment data in the same JSON structure as the input.
        Focus on maximizing data completeness and accuracy.
        """
        
        try:
            merged_response = await self.run_task(merge_prompt)
            
            # Parse the merged data
            import json
            merged_data = json.loads(merged_response)
            
            # Ensure we keep the existing ID
            merged_data["id"] = existing.get("id")
            merged_data["_operation"] = "update"
            
            return merged_data
            
        except Exception as e:
            logger.error(f"Failed to merge equipment data using AI: {e}")
            
            # Fallback to simple merge
            merged = new.copy()
            merged["id"] = existing.get("id")
            merged["_operation"] = "update"
            
            # Merge features and certifications
            existing_features = set(existing.get("features", []))
            new_features = set(new.get("features", []))
            merged["features"] = list(existing_features.union(new_features))
            
            existing_certs = set(existing.get("certifications", []))
            new_certs = set(new.get("certifications", []))
            merged["certifications"] = list(existing_certs.union(new_certs))
            
            return merged
    
    def _generate_equipment_id(self, equipment: Dict[str, Any]) -> str:
        """Generate unique identifier for equipment."""
        
        basic_info = equipment.get("basic_info", {})
        manufacturer = basic_info.get("manufacturer", "unknown")
        model = basic_info.get("model", "unknown")
        equipment_type = basic_info.get("type", "unknown")
        
        # Create hash-like ID
        import hashlib
        id_string = f"{manufacturer}_{model}_{equipment_type}".lower()
        return hashlib.md5(id_string.encode()).hexdigest()[:12]
    
    async def _generate_integration_report(
        self,
        processed_equipment: List[Dict[str, Any]],
        database_results: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive integration report."""
        
        report = {
            "summary": {
                "total_processed": len(processed_equipment),
                "total_operations": database_results.get("total_operations", 0),
                "successful_operations": 0,
                "failed_operations": 0,
                "integration_rate": 0.0
            },
            "database_breakdown": {},
            "operation_types": {
                "insert": 0,
                "update": 0,
                "replace": 0,
                "skip": 0
            },
            "quality_metrics": {
                "average_confidence": 0.0,
                "high_confidence_items": 0,
                "complete_items": 0
            },
            "errors": [],
            "recommendations": []
        }
        
        # Calculate database breakdown
        for db_name in ["postgresql", "mongodb", "neo4j"]:
            if db_name in database_results:
                db_result = database_results[db_name]
                report["database_breakdown"][db_name] = {
                    "saved": db_result.get("saved", 0),
                    "updated": db_result.get("updated", 0),
                    "errors": db_result.get("errors", 0),
                    "success_rate": (
                        (db_result.get("saved", 0) + db_result.get("updated", 0)) /
                        max(1, database_results.get("total_operations", 1))
                    )
                }
                
                report["summary"]["successful_operations"] += (
                    db_result.get("saved", 0) + db_result.get("updated", 0)
                )
                report["summary"]["failed_operations"] += db_result.get("errors", 0)
                
                # Collect errors
                report["errors"].extend(db_result.get("error_details", []))
        
        # Calculate operation types
        for equipment in processed_equipment:
            operation = equipment.get("_operation", "insert")
            if operation in report["operation_types"]:
                report["operation_types"][operation] += 1
        
        # Calculate quality metrics
        if processed_equipment:
            confidences = [eq.get("confidence_score", 0) for eq in processed_equipment]
            report["quality_metrics"]["average_confidence"] = sum(confidences) / len(confidences)
            report["quality_metrics"]["high_confidence_items"] = len([
                c for c in confidences if c > 0.7
            ])
            
            # Count complete items
            complete_count = 0
            for equipment in processed_equipment:
                basic_info = equipment.get("basic_info", {})
                tech_specs = equipment.get("technical_specs", {})
                
                if (basic_info.get("manufacturer") and basic_info.get("model") and
                    tech_specs and len(tech_specs) > 2):
                    complete_count += 1
            
            report["quality_metrics"]["complete_items"] = complete_count
        
        # Calculate overall integration rate
        if database_results.get("total_operations", 0) > 0:
            report["summary"]["integration_rate"] = (
                report["summary"]["successful_operations"] /
                database_results["total_operations"]
            )
        
        # Generate recommendations
        report["recommendations"] = await self._generate_recommendations(report, config)
        
        return report
    
    async def _generate_recommendations(
        self,
        report: Dict[str, Any],
        config: Dict[str, Any]
    ) -> List[str]:
        """Generate recommendations based on integration results."""
        
        recommendations = []
        
        # Success rate recommendations
        integration_rate = report["summary"]["integration_rate"]
        if integration_rate < 0.8:
            recommendations.append(
                f"Low integration success rate ({integration_rate:.1%}). "
                "Consider reviewing data validation rules and database connectivity."
            )
        
        # Quality recommendations
        avg_confidence = report["quality_metrics"]["average_confidence"]
        if avg_confidence < 0.6:
            recommendations.append(
                f"Low average confidence score ({avg_confidence:.3f}). "
                "Consider improving extraction algorithms or data sources."
            )
        
        # Error recommendations
        total_errors = report["summary"]["failed_operations"]
        if total_errors > 0:
            recommendations.append(
                f"{total_errors} integration errors occurred. "
                "Review error details and consider data preprocessing improvements."
            )
        
        # Database-specific recommendations
        for db_name, db_stats in report["database_breakdown"].items():
            if db_stats["success_rate"] < 0.9:
                recommendations.append(
                    f"{db_name} has low success rate ({db_stats['success_rate']:.1%}). "
                    "Check database connectivity and schema compatibility."
                )
        
        return recommendations
