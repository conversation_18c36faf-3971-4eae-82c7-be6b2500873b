"""Planner Agent for HVAC data crawling strategy."""

import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from loguru import logger

from .base_agent import BaseAgent
from ..config import settings


class PlannerAgent(BaseAgent):
    """Agent responsible for planning HVAC data crawling strategies."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="Planner",
            role="HVAC Data Crawling Strategist",
            goal="Plan and optimize crawling strategies for HVAC equipment data collection",
            backstory=(
                "You are an expert strategist specializing in HVAC industry data collection. "
                "You have deep knowledge of manufacturer websites, product catalogs, and "
                "technical documentation structures. Your expertise helps identify the most "
                "efficient crawling paths and prioritize data sources based on quality and relevance."
            ),
            **kwargs
        )
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute planning strategy for HVAC data crawling.
        
        Args:
            input_data: Planning requirements and constraints
            **kwargs: Additional planning parameters
            
        Returns:
            Comprehensive crawling plan
        """
        try:
            logger.info("Starting crawling strategy planning")
            
            # Validate input
            if not self.validate_input(input_data, ["manufacturers", "priorities"]):
                raise ValueError("Invalid input data for planning")
            
            # Generate crawling plan
            plan = await self._generate_crawling_plan(
                input_data["manufacturers"],
                input_data.get("priorities", {}),
                **kwargs
            )
            
            # Optimize plan
            optimized_plan = await self._optimize_plan(plan)
            
            # Add execution schedule
            scheduled_plan = await self._add_schedule(optimized_plan)
            
            logger.info("Crawling strategy planning completed successfully")
            return scheduled_plan
            
        except Exception as e:
            logger.error(f"Planning execution failed: {e}")
            raise
    
    async def _generate_crawling_plan(
        self,
        manufacturers: List[str],
        priorities: Dict[str, int],
        **kwargs
    ) -> Dict[str, Any]:
        """Generate initial crawling plan."""
        
        # Get manufacturer configurations
        manufacturer_configs = self._get_manufacturer_configs(manufacturers)
        
        # Create planning prompt
        planning_prompt = self._create_planning_prompt(
            manufacturer_configs,
            priorities,
            **kwargs
        )
        
        # Get AI planning recommendations
        plan_response = await self.run_task(planning_prompt)
        
        # Parse and structure the plan
        try:
            plan_data = json.loads(plan_response)
        except json.JSONDecodeError:
            # Fallback to structured parsing
            plan_data = self._parse_plan_response(plan_response)
        
        return plan_data
    
    def _get_manufacturer_configs(self, manufacturers: List[str]) -> List[Dict[str, Any]]:
        """Get configuration for specified manufacturers."""
        configs = []
        
        for manufacturer in manufacturers:
            # Find manufacturer in settings
            manufacturer_config = None
            for config in settings.manufacturers:
                if config["name"].lower() == manufacturer.lower():
                    manufacturer_config = config
                    break
            
            if manufacturer_config:
                configs.append(manufacturer_config)
            else:
                # Create default config
                configs.append({
                    "name": manufacturer,
                    "base_url": f"https://www.{manufacturer.lower()}.com",
                    "product_pages": ["/products", "/hvac"],
                    "selectors": {
                        "product_list": ".product-grid .product",
                        "product_specs": ".specifications"
                    }
                })
        
        return configs
    
    def _create_planning_prompt(
        self,
        manufacturer_configs: List[Dict[str, Any]],
        priorities: Dict[str, int],
        **kwargs
    ) -> str:
        """Create planning prompt for AI agent."""
        
        prompt = f"""
        Plan an efficient HVAC equipment data crawling strategy for the following manufacturers:
        
        Manufacturers and their configurations:
        {json.dumps(manufacturer_configs, indent=2)}
        
        Priorities (1=highest, 5=lowest):
        {json.dumps(priorities, indent=2)}
        
        Additional constraints:
        - Maximum pages per manufacturer: {settings.crawling.max_pages}
        - Crawling delay: {settings.crawling.delay} seconds
        - Timeout: {settings.crawling.timeout} seconds
        - Respect robots.txt: {settings.crawling.respect_robots_txt}
        
        Please provide a detailed crawling plan in JSON format with the following structure:
        {{
            "strategy": "description of overall strategy",
            "manufacturers": [
                {{
                    "name": "manufacturer name",
                    "priority": 1-5,
                    "estimated_pages": number,
                    "crawl_order": number,
                    "target_urls": ["list of specific URLs to crawl"],
                    "selectors": {{
                        "product_list": "CSS selector",
                        "product_specs": "CSS selector"
                    }},
                    "expected_data_types": ["list of data types"],
                    "estimated_duration": "time estimate"
                }}
            ],
            "total_estimated_duration": "total time estimate",
            "risk_factors": ["list of potential issues"],
            "success_metrics": ["list of success criteria"]
        }}
        
        Focus on:
        1. Maximizing data quality and coverage
        2. Minimizing crawling time and resources
        3. Avoiding rate limiting and blocking
        4. Prioritizing high-value manufacturers
        5. Ensuring comprehensive technical specifications
        """
        
        return prompt
    
    def _parse_plan_response(self, response: str) -> Dict[str, Any]:
        """Parse plan response when JSON parsing fails."""
        # Basic fallback parsing
        plan = {
            "strategy": "Sequential crawling with priority-based ordering",
            "manufacturers": [],
            "total_estimated_duration": "2-4 hours",
            "risk_factors": ["Rate limiting", "Dynamic content"],
            "success_metrics": ["Data completeness", "Extraction accuracy"]
        }
        
        # Extract manufacturer information from response
        lines = response.split('\n')
        current_manufacturer = None
        
        for line in lines:
            line = line.strip()
            if "manufacturer:" in line.lower() or "name:" in line.lower():
                if current_manufacturer:
                    plan["manufacturers"].append(current_manufacturer)
                current_manufacturer = {
                    "name": line.split(":")[-1].strip(),
                    "priority": 3,
                    "estimated_pages": 20,
                    "crawl_order": len(plan["manufacturers"]) + 1,
                    "target_urls": [],
                    "selectors": {
                        "product_list": ".product",
                        "product_specs": ".specs"
                    },
                    "expected_data_types": ["technical_specs", "product_info"],
                    "estimated_duration": "30-60 minutes"
                }
        
        if current_manufacturer:
            plan["manufacturers"].append(current_manufacturer)
        
        return plan
    
    async def _optimize_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize the crawling plan for efficiency."""
        
        optimization_prompt = f"""
        Optimize the following HVAC crawling plan for maximum efficiency:
        
        Current plan:
        {json.dumps(plan, indent=2)}
        
        Consider:
        1. Parallel vs sequential crawling opportunities
        2. Resource allocation optimization
        3. Risk mitigation strategies
        4. Data quality vs speed trade-offs
        
        Provide optimized plan with the same JSON structure, including:
        - Improved crawling order
        - Parallel processing opportunities
        - Resource allocation recommendations
        - Risk mitigation measures
        """
        
        optimized_response = await self.run_task(optimization_prompt)
        
        try:
            optimized_plan = json.loads(optimized_response)
            return optimized_plan
        except json.JSONDecodeError:
            logger.warning("Failed to parse optimization response, using original plan")
            return plan
    
    async def _add_schedule(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Add execution schedule to the plan."""
        
        now = datetime.now()
        schedule = {
            "created_at": now.isoformat(),
            "execution_start": (now + timedelta(minutes=5)).isoformat(),
            "phases": []
        }
        
        current_time = now + timedelta(minutes=5)
        
        for i, manufacturer in enumerate(plan.get("manufacturers", [])):
            phase_duration = self._estimate_phase_duration(manufacturer)
            
            phase = {
                "phase": i + 1,
                "manufacturer": manufacturer["name"],
                "start_time": current_time.isoformat(),
                "end_time": (current_time + phase_duration).isoformat(),
                "estimated_duration_minutes": phase_duration.total_seconds() / 60,
                "dependencies": [],
                "parallel_execution": False
            }
            
            schedule["phases"].append(phase)
            current_time += phase_duration + timedelta(minutes=2)  # Buffer time
        
        schedule["total_duration_minutes"] = (current_time - now).total_seconds() / 60
        schedule["estimated_completion"] = current_time.isoformat()
        
        plan["schedule"] = schedule
        return plan
    
    def _estimate_phase_duration(self, manufacturer: Dict[str, Any]) -> timedelta:
        """Estimate duration for a manufacturer crawling phase."""
        
        estimated_pages = manufacturer.get("estimated_pages", 20)
        crawl_delay = settings.crawling.delay
        
        # Base time calculation
        base_minutes = (estimated_pages * crawl_delay) / 60
        
        # Add processing overhead
        processing_overhead = base_minutes * 0.3
        
        # Add complexity factor based on priority
        priority = manufacturer.get("priority", 3)
        complexity_factor = 1.0 + (5 - priority) * 0.2
        
        total_minutes = (base_minutes + processing_overhead) * complexity_factor
        
        return timedelta(minutes=max(10, int(total_minutes)))
    
    async def validate_plan(self, plan: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the generated crawling plan."""
        
        validation_results = {
            "valid": True,
            "warnings": [],
            "errors": [],
            "recommendations": []
        }
        
        # Check required fields
        required_fields = ["strategy", "manufacturers", "schedule"]
        for field in required_fields:
            if field not in plan:
                validation_results["errors"].append(f"Missing required field: {field}")
                validation_results["valid"] = False
        
        # Validate manufacturers
        if "manufacturers" in plan:
            for i, manufacturer in enumerate(plan["manufacturers"]):
                if not manufacturer.get("name"):
                    validation_results["errors"].append(f"Manufacturer {i} missing name")
                    validation_results["valid"] = False
                
                if not manufacturer.get("target_urls"):
                    validation_results["warnings"].append(
                        f"Manufacturer {manufacturer.get('name', i)} has no target URLs"
                    )
        
        # Check total duration
        if "schedule" in plan:
            total_duration = plan["schedule"].get("total_duration_minutes", 0)
            if total_duration > 480:  # 8 hours
                validation_results["warnings"].append(
                    f"Total duration ({total_duration:.1f} minutes) is very long"
                )
        
        return validation_results
