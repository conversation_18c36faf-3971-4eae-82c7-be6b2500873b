"""Quote Generator Agent for creating professional HVAC equipment quotes."""

import json
import base64
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from loguru import logger
from io import BytesIO

from .base_agent import BaseAgent

try:
    from reportlab.lib.pagesizes import A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import cm
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False
    logger.warning("ReportLab not available - PDF generation will be limited")


class QuoteGeneratorAgent(BaseAgent):
    """Agent responsible for generating professional HVAC equipment quotes."""
    
    def __init__(self, **kwargs):
        super().__init__(
            name="QuoteGenerator",
            role="Professional Quote Generation Specialist",
            goal="Generate compelling and professional HVAC equipment quotes with personalized content",
            backstory=(
                "You are an expert in creating professional business quotes that convert "
                "prospects into customers. You understand how to present technical information "
                "in an accessible way, highlight value propositions, and create compelling "
                "proposals that address customer needs while showcasing equipment benefits."
            ),
            **kwargs
        )
        
        # Company information
        self.company_info = {
            "name": "Fulmark HVAC Solutions",
            "address": "ul. Przykładowa 123, 00-001 Warszawa",
            "phone": "+48 22 123 45 67",
            "email": "<EMAIL>",
            "website": "www.fulmark.pl",
            "nip": "123-456-78-90",
            "regon": "*********",
            "logo_path": None  # Path to company logo
        }
    
    async def execute(self, input_data: Any, **kwargs) -> Dict[str, Any]:
        """Execute quote generation based on customer analysis and equipment recommendations.
        
        Args:
            input_data: Customer analysis and equipment recommendations
            **kwargs: Additional generation parameters
            
        Returns:
            Generated quote with PDF and content
        """
        try:
            logger.info("Starting quote generation")
            
            # Validate input
            required_fields = ["customer_analysis", "equipment_recommendations", "customer_info"]
            if not self.validate_input(input_data, required_fields):
                raise ValueError("Invalid input data for quote generation")
            
            customer_analysis = input_data["customer_analysis"]
            equipment_recommendations = input_data["equipment_recommendations"]
            customer_info = input_data["customer_info"]
            quote_config = input_data.get("config", {})
            
            # Generate quote content
            quote_content = await self._generate_quote_content(
                customer_analysis,
                equipment_recommendations,
                customer_info,
                quote_config
            )
            
            # Generate PDF quote
            pdf_result = await self._generate_pdf_quote(
                quote_content,
                customer_info,
                equipment_recommendations
            )
            
            # Generate follow-up recommendations
            follow_up_plan = await self._generate_follow_up_plan(
                customer_analysis,
                quote_content
            )
            
            # Compile generation results
            generation_results = {
                "generation_summary": {
                    "quote_id": quote_content.get("quote_id"),
                    "customer_id": customer_info.get("customer_id"),
                    "equipment_count": len(equipment_recommendations),
                    "total_value": quote_content.get("pricing", {}).get("total_amount", 0),
                    "generation_date": datetime.now().isoformat(),
                    "generator_version": "1.0.0"
                },
                "quote_content": quote_content,
                "pdf_result": pdf_result,
                "follow_up_plan": follow_up_plan,
                "delivery_recommendations": await self._generate_delivery_recommendations(
                    customer_analysis, customer_info
                )
            }
            
            logger.info(f"Quote generation completed: {quote_content.get('quote_id')}")
            return generation_results
            
        except Exception as e:
            logger.error(f"Quote generation failed: {e}")
            raise
    
    async def _generate_quote_content(
        self,
        customer_analysis: Dict[str, Any],
        equipment_recommendations: List[Dict[str, Any]],
        customer_info: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate comprehensive quote content."""
        
        # Generate quote ID
        quote_id = f"FUL-{datetime.now().strftime('%Y%m%d')}-{customer_info.get('customer_id', '000')[:3].upper()}"
        
        # Extract customer preferences for personalization
        preferences = customer_analysis.get("preferences_analysis", {})
        business_analysis = customer_analysis.get("business_analysis", {})
        
        content_prompt = f"""
        Generate professional HVAC equipment quote content for Polish customer.
        
        Customer Information:
        - Name: {customer_info.get("name", "Szanowny Kliencie")}
        - Company: {customer_info.get("company", "")}
        - Address: {customer_info.get("address", "")}
        - Communication Style: {customer_analysis.get("customer_profile", {}).get("communication_style", "formal")}
        
        Equipment Recommendations ({len(equipment_recommendations)} items):
        {json.dumps(equipment_recommendations, indent=2)}
        
        Customer Preferences:
        - Primary Decision Factor: {preferences.get("decision_factors", {}).get("primary_factor", "value")}
        - Feature Priorities: {preferences.get("feature_priorities", {})}
        - Budget Positioning: {business_analysis.get("recommended_approach", {}).get("quote_positioning", "value")}
        
        Generate quote content in JSON format:
        {{
            "quote_header": {{
                "quote_id": "{quote_id}",
                "date": "{datetime.now().strftime('%d.%m.%Y')}",
                "valid_until": "{(datetime.now() + timedelta(days=30)).strftime('%d.%m.%Y')}",
                "customer_reference": "customer name or project reference"
            }},
            "introduction": {{
                "greeting": "personalized greeting in Polish",
                "project_understanding": "summary of customer needs and project scope",
                "value_proposition": "why Fulmark is the right choice for this project"
            }},
            "equipment_sections": [
                {{
                    "section_title": "Recommended HVAC Solution",
                    "equipment_items": [
                        {{
                            "item_number": 1,
                            "equipment_name": "LG model name and series",
                            "description": "detailed description highlighting key benefits",
                            "technical_specs": {{
                                "cooling_capacity": "value with unit",
                                "heating_capacity": "value with unit",
                                "energy_class": "efficiency rating",
                                "key_features": ["list", "of", "main", "features"]
                            }},
                            "pricing": {{
                                "unit_price": number,
                                "quantity": 1,
                                "subtotal": number,
                                "currency": "PLN"
                            }},
                            "benefits": ["why", "this", "equipment", "is", "perfect"],
                            "warranty": "warranty information"
                        }}
                    ]
                }}
            ],
            "installation_section": {{
                "title": "Professional Installation Service",
                "description": "comprehensive installation service description",
                "included_services": ["list", "of", "included", "services"],
                "timeline": "estimated installation timeline",
                "pricing": {{
                    "installation_cost": number,
                    "additional_services": [
                        {{"service": "service name", "cost": number}}
                    ]
                }}
            }},
            "pricing_summary": {{
                "equipment_total": number,
                "installation_total": number,
                "subtotal": number,
                "vat_rate": 0.23,
                "vat_amount": number,
                "total_amount": number,
                "currency": "PLN"
            }},
            "terms_and_conditions": {{
                "payment_terms": "payment terms and options",
                "delivery_terms": "delivery and installation terms",
                "warranty_terms": "warranty and service terms",
                "validity": "quote validity period"
            }},
            "closing": {{
                "call_to_action": "encouraging next steps",
                "contact_information": "how to proceed with the order",
                "personal_touch": "personalized closing message"
            }}
        }}
        
        Write in professional Polish business language. Focus on value, quality, and customer benefits.
        Highlight energy efficiency, reliability, and long-term savings.
        """
        
        try:
            response = await self.run_task(content_prompt)
            quote_content = json.loads(response)
            
            # Add metadata
            quote_content["metadata"] = {
                "generated_at": datetime.now().isoformat(),
                "generator": "QuoteGeneratorAgent",
                "customer_analysis_confidence": customer_analysis.get("analysis_summary", {}).get("confidence_score", 0.0),
                "personalization_level": self._calculate_personalization_level(customer_analysis)
            }
            
            return quote_content
            
        except (json.JSONDecodeError, Exception) as e:
            logger.error(f"Failed to generate quote content: {e}")
            
            # Fallback to basic quote content
            return await self._generate_fallback_quote_content(
                customer_info, equipment_recommendations, quote_id
            )
    
    async def _generate_pdf_quote(
        self,
        quote_content: Dict[str, Any],
        customer_info: Dict[str, Any],
        equipment_recommendations: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate PDF quote document."""
        
        if not REPORTLAB_AVAILABLE:
            logger.warning("ReportLab not available - generating text-based quote")
            return await self._generate_text_quote(quote_content)
        
        try:
            # Create PDF buffer
            buffer = BytesIO()
            
            # Create PDF document
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # Build PDF content
            story = []
            styles = getSampleStyleSheet()
            
            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                textColor=colors.HexColor('#1f4e79')
            )
            
            # Header
            story.append(Paragraph(f"<b>{self.company_info['name']}</b>", title_style))
            story.append(Paragraph(f"Oferta nr {quote_content.get('quote_header', {}).get('quote_id', 'N/A')}", styles['Heading2']))
            story.append(Spacer(1, 20))
            
            # Customer information
            customer_name = customer_info.get('name', 'Szanowny Kliencie')
            story.append(Paragraph(f"<b>Dla:</b> {customer_name}", styles['Normal']))
            if customer_info.get('company'):
                story.append(Paragraph(f"<b>Firma:</b> {customer_info['company']}", styles['Normal']))
            story.append(Spacer(1, 20))
            
            # Introduction
            intro = quote_content.get('introduction', {})
            if intro.get('greeting'):
                story.append(Paragraph(intro['greeting'], styles['Normal']))
                story.append(Spacer(1, 12))
            
            # Equipment sections
            for section in quote_content.get('equipment_sections', []):
                story.append(Paragraph(f"<b>{section.get('section_title', 'Sprzęt HVAC')}</b>", styles['Heading3']))
                story.append(Spacer(1, 12))
                
                # Equipment table
                equipment_data = [['Poz.', 'Opis', 'Ilość', 'Cena jedn.', 'Wartość']]
                
                for item in section.get('equipment_items', []):
                    equipment_data.append([
                        str(item.get('item_number', 1)),
                        f"{item.get('equipment_name', 'Sprzęt HVAC')}\n{item.get('description', '')[:100]}...",
                        str(item.get('pricing', {}).get('quantity', 1)),
                        f"{item.get('pricing', {}).get('unit_price', 0):,.0f} PLN",
                        f"{item.get('pricing', {}).get('subtotal', 0):,.0f} PLN"
                    ])
                
                equipment_table = Table(equipment_data, colWidths=[1*cm, 8*cm, 2*cm, 3*cm, 3*cm])
                equipment_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                
                story.append(equipment_table)
                story.append(Spacer(1, 20))
            
            # Pricing summary
            pricing = quote_content.get('pricing_summary', {})
            if pricing:
                story.append(Paragraph("<b>Podsumowanie cenowe:</b>", styles['Heading3']))
                
                pricing_data = [
                    ['Sprzęt:', f"{pricing.get('equipment_total', 0):,.0f} PLN"],
                    ['Montaż:', f"{pricing.get('installation_total', 0):,.0f} PLN"],
                    ['Netto:', f"{pricing.get('subtotal', 0):,.0f} PLN"],
                    ['VAT 23%:', f"{pricing.get('vat_amount', 0):,.0f} PLN"],
                    ['<b>RAZEM BRUTTO:</b>', f"<b>{pricing.get('total_amount', 0):,.0f} PLN</b>"]
                ]
                
                pricing_table = Table(pricing_data, colWidths=[10*cm, 4*cm])
                pricing_table.setStyle(TableStyle([
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('ALIGN', (1, 0), (1, -1), 'RIGHT'),
                    ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, -1), (-1, -1), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                    ('TOPPADDING', (0, 0), (-1, -1), 6),
                ]))
                
                story.append(pricing_table)
                story.append(Spacer(1, 20))
            
            # Terms and conditions
            terms = quote_content.get('terms_and_conditions', {})
            if terms:
                story.append(Paragraph("<b>Warunki:</b>", styles['Heading3']))
                if terms.get('payment_terms'):
                    story.append(Paragraph(f"<b>Płatność:</b> {terms['payment_terms']}", styles['Normal']))
                if terms.get('delivery_terms'):
                    story.append(Paragraph(f"<b>Dostawa:</b> {terms['delivery_terms']}", styles['Normal']))
                if terms.get('warranty_terms'):
                    story.append(Paragraph(f"<b>Gwarancja:</b> {terms['warranty_terms']}", styles['Normal']))
                story.append(Spacer(1, 20))
            
            # Footer
            story.append(Paragraph(f"<b>{self.company_info['name']}</b>", styles['Normal']))
            story.append(Paragraph(f"Tel: {self.company_info['phone']} | Email: {self.company_info['email']}", styles['Normal']))
            story.append(Paragraph(f"NIP: {self.company_info['nip']}", styles['Normal']))
            
            # Build PDF
            doc.build(story)
            
            # Get PDF data
            pdf_data = buffer.getvalue()
            buffer.close()
            
            # Encode to base64 for storage/transmission
            pdf_base64 = base64.b64encode(pdf_data).decode('utf-8')
            
            return {
                "pdf_generated": True,
                "pdf_size_bytes": len(pdf_data),
                "pdf_base64": pdf_base64,
                "filename": f"oferta_{quote_content.get('quote_header', {}).get('quote_id', 'unknown')}.pdf",
                "generation_method": "reportlab"
            }
            
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            return await self._generate_text_quote(quote_content)
    
    async def _generate_text_quote(self, quote_content: Dict[str, Any]) -> Dict[str, Any]:
        """Generate text-based quote when PDF generation fails."""
        
        text_content = f"""
=== OFERTA HVAC ===
{self.company_info['name']}

Oferta nr: {quote_content.get('quote_header', {}).get('quote_id', 'N/A')}
Data: {quote_content.get('quote_header', {}).get('date', datetime.now().strftime('%d.%m.%Y'))}

{quote_content.get('introduction', {}).get('greeting', 'Szanowny Kliencie,')}

{quote_content.get('introduction', {}).get('project_understanding', '')}

=== SPRZĘT HVAC ===
"""
        
        # Add equipment details
        for section in quote_content.get('equipment_sections', []):
            text_content += f"\n{section.get('section_title', 'Sprzęt')}:\n"
            for item in section.get('equipment_items', []):
                text_content += f"- {item.get('equipment_name', 'Sprzęt HVAC')}\n"
                text_content += f"  {item.get('description', '')}\n"
                text_content += f"  Cena: {item.get('pricing', {}).get('unit_price', 0):,.0f} PLN\n\n"
        
        # Add pricing
        pricing = quote_content.get('pricing_summary', {})
        if pricing:
            text_content += f"""
=== PODSUMOWANIE ===
Sprzęt: {pricing.get('equipment_total', 0):,.0f} PLN
Montaż: {pricing.get('installation_total', 0):,.0f} PLN
Netto: {pricing.get('subtotal', 0):,.0f} PLN
VAT 23%: {pricing.get('vat_amount', 0):,.0f} PLN
RAZEM BRUTTO: {pricing.get('total_amount', 0):,.0f} PLN
"""
        
        text_content += f"""
=== KONTAKT ===
{self.company_info['name']}
Tel: {self.company_info['phone']}
Email: {self.company_info['email']}
"""
        
        return {
            "pdf_generated": False,
            "text_content": text_content,
            "filename": f"oferta_{quote_content.get('quote_header', {}).get('quote_id', 'unknown')}.txt",
            "generation_method": "text_fallback"
        }
    
    async def _generate_follow_up_plan(
        self,
        customer_analysis: Dict[str, Any],
        quote_content: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate follow-up plan for the quote."""
        
        preferences = customer_analysis.get("preferences_analysis", {})
        business_analysis = customer_analysis.get("business_analysis", {})
        
        return {
            "immediate_actions": [
                "Send quote via preferred communication method",
                "Schedule follow-up call within 2-3 days",
                "Prepare answers to potential technical questions"
            ],
            "follow_up_schedule": {
                "day_2": "Initial follow-up call to confirm receipt",
                "week_1": "Technical clarification meeting if needed",
                "week_2": "Pricing discussion and negotiation",
                "week_3": "Final decision follow-up"
            },
            "communication_strategy": {
                "preferred_method": preferences.get("communication_preferences", {}).get("preferred_contact_method", "email"),
                "information_level": preferences.get("communication_preferences", {}).get("information_detail_level", "medium"),
                "decision_timeline": business_analysis.get("sales_probability", {}).get("timeline_to_decision", "weeks")
            },
            "objection_handling": {
                "price_concerns": "Emphasize long-term energy savings and ROI",
                "technical_doubts": "Offer site visit and detailed technical consultation",
                "competitor_comparison": "Highlight LG quality and comprehensive service"
            }
        }
    
    async def _generate_delivery_recommendations(
        self,
        customer_analysis: Dict[str, Any],
        customer_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate recommendations for quote delivery."""
        
        preferences = customer_analysis.get("preferences_analysis", {})
        
        return {
            "delivery_method": preferences.get("communication_preferences", {}).get("preferred_contact_method", "email"),
            "presentation_format": "PDF with detailed technical appendix",
            "timing": "Send during business hours (9-17)",
            "personalization": {
                "cover_letter": "Include personalized cover letter",
                "technical_focus": preferences.get("communication_preferences", {}).get("technical_explanation_needed", False),
                "value_emphasis": "Highlight energy efficiency and cost savings"
            },
            "additional_materials": [
                "LG product brochures",
                "Energy efficiency certificates",
                "Customer testimonials",
                "Installation timeline"
            ]
        }
    
    def _calculate_personalization_level(self, customer_analysis: Dict[str, Any]) -> str:
        """Calculate the level of personalization achieved."""
        
        confidence = customer_analysis.get("analysis_summary", {}).get("confidence_score", 0.0)
        
        if confidence > 0.8:
            return "high"
        elif confidence > 0.5:
            return "medium"
        else:
            return "low"
    
    async def _generate_fallback_quote_content(
        self,
        customer_info: Dict[str, Any],
        equipment_recommendations: List[Dict[str, Any]],
        quote_id: str
    ) -> Dict[str, Any]:
        """Generate fallback quote content when AI generation fails."""
        
        total_equipment_cost = sum(
            rec.get("equipment", {}).get("estimated_price_pln", 0)
            for rec in equipment_recommendations
        )
        
        installation_cost = 1500  # Base installation cost
        subtotal = total_equipment_cost + installation_cost
        vat_amount = subtotal * 0.23
        total_amount = subtotal + vat_amount
        
        return {
            "quote_header": {
                "quote_id": quote_id,
                "date": datetime.now().strftime('%d.%m.%Y'),
                "valid_until": (datetime.now() + timedelta(days=30)).strftime('%d.%m.%Y')
            },
            "introduction": {
                "greeting": f"Szanowny {customer_info.get('name', 'Kliencie')},",
                "project_understanding": "Dziękujemy za zainteresowanie naszymi rozwiązaniami HVAC.",
                "value_proposition": "Fulmark oferuje najwyższej jakości sprzęt LG z profesjonalnym montażem."
            },
            "equipment_sections": [
                {
                    "section_title": "Rekomendowane rozwiązanie HVAC",
                    "equipment_items": [
                        {
                            "item_number": i + 1,
                            "equipment_name": f"LG {rec.get('equipment', {}).get('model', 'HVAC')}",
                            "description": f"Profesjonalny system klimatyzacji {rec.get('equipment', {}).get('series', '')}",
                            "pricing": {
                                "unit_price": rec.get("equipment", {}).get("estimated_price_pln", 0),
                                "quantity": 1,
                                "subtotal": rec.get("equipment", {}).get("estimated_price_pln", 0)
                            }
                        }
                        for i, rec in enumerate(equipment_recommendations)
                    ]
                }
            ],
            "pricing_summary": {
                "equipment_total": total_equipment_cost,
                "installation_total": installation_cost,
                "subtotal": subtotal,
                "vat_amount": vat_amount,
                "total_amount": total_amount
            },
            "terms_and_conditions": {
                "payment_terms": "30 dni od daty wystawienia faktury",
                "delivery_terms": "3-7 dni roboczych",
                "warranty_terms": "Gwarancja producenta + 2 lata serwisu"
            },
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "generator": "QuoteGeneratorAgent_Fallback",
                "personalization_level": "basic"
            }
        }
