"""Crawl4AI wrapper for HVAC equipment data extraction."""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime
from loguru import logger

try:
    from crawl4ai import AsyncWebCrawler
    CRAWL4AI_AVAILABLE = True
except ImportError:
    logger.warning("Crawl4AI not available, using fallback crawler")
    CRAWL4AI_AVAILABLE = False

from ..config import settings


class Crawl4AIWrapper:
    """Wrapper for Crawl4AI with HVAC-specific optimizations."""
    
    def __init__(self):
        self.crawler = None
        self.session_active = False
        
    async def __aenter__(self):
        """Async context manager entry."""
        await self.initialize()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.cleanup()
    
    async def initialize(self):
        """Initialize the crawler."""
        if not CRAWL4AI_AVAILABLE:
            raise RuntimeError("Crawl4AI is not available")
        
        try:
            self.crawler = AsyncWebCrawler(
                headless=settings.crawling.playwright_headless,
                browser_type="chromium",
                user_agent=settings.crawling.user_agent,
                verbose=True
            )
            await self.crawler.__aenter__()
            self.session_active = True
            logger.info("Crawl4AI wrapper initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Crawl4AI: {e}")
            raise
    
    async def cleanup(self):
        """Cleanup crawler resources."""
        if self.crawler and self.session_active:
            try:
                await self.crawler.__aexit__(None, None, None)
                self.session_active = False
                logger.info("Crawl4AI wrapper cleaned up")
            except Exception as e:
                logger.error(f"Error during crawler cleanup: {e}")
    
    async def crawl_url(
        self,
        url: str,
        max_pages: int = 20,
        delay: float = 1.0,
        timeout: int = 30,
        selectors: Optional[Dict[str, str]] = None,
        deep_crawl: str = "bfs",
        content_filter: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """Crawl a single URL with HVAC-specific optimizations.
        
        Args:
            url: URL to crawl
            max_pages: Maximum pages to crawl
            delay: Delay between requests
            timeout: Request timeout
            selectors: CSS selectors for content extraction
            deep_crawl: Deep crawling strategy (bfs/dfs/false)
            content_filter: Content filtering rules
            **kwargs: Additional crawling parameters
            
        Returns:
            Crawling result with extracted content
        """
        if not self.session_active:
            raise RuntimeError("Crawler not initialized")
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting crawl for URL: {url}")
            
            # Prepare crawling parameters
            crawl_params = self._prepare_crawl_params(
                url=url,
                max_pages=max_pages,
                delay=delay,
                timeout=timeout,
                selectors=selectors,
                deep_crawl=deep_crawl,
                content_filter=content_filter,
                **kwargs
            )
            
            # Execute crawling
            result = await self.crawler.arun(**crawl_params)
            
            # Process result
            processed_result = self._process_crawl_result(result, url, start_time)
            
            logger.info(f"Crawl completed for {url}: {len(processed_result.get('content', ''))} chars")
            return processed_result
            
        except Exception as e:
            logger.error(f"Crawl failed for {url}: {e}")
            return {
                "url": url,
                "success": False,
                "error": str(e),
                "content": "",
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": datetime.now().isoformat(),
                    "duration_seconds": (datetime.now() - start_time).total_seconds()
                }
            }
    
    async def crawl_urls(
        self,
        urls: List[str],
        max_pages: int = 20,
        delay: float = 1.0,
        timeout: int = 30,
        selectors: Optional[Dict[str, str]] = None,
        deep_crawl: str = "bfs",
        content_filter: Optional[List[str]] = None,
        parallel: bool = False,
        max_concurrent: int = 3,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl multiple URLs.
        
        Args:
            urls: List of URLs to crawl
            max_pages: Maximum pages per URL
            delay: Delay between requests
            timeout: Request timeout
            selectors: CSS selectors for content extraction
            deep_crawl: Deep crawling strategy
            content_filter: Content filtering rules
            parallel: Enable parallel crawling
            max_concurrent: Maximum concurrent crawls
            **kwargs: Additional crawling parameters
            
        Returns:
            List of crawling results
        """
        if not urls:
            return []
        
        logger.info(f"Starting crawl for {len(urls)} URLs (parallel: {parallel})")
        
        if parallel and len(urls) > 1:
            return await self._crawl_urls_parallel(
                urls, max_concurrent, max_pages, delay, timeout,
                selectors, deep_crawl, content_filter, **kwargs
            )
        else:
            return await self._crawl_urls_sequential(
                urls, max_pages, delay, timeout,
                selectors, deep_crawl, content_filter, **kwargs
            )
    
    async def _crawl_urls_sequential(
        self,
        urls: List[str],
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl URLs sequentially."""
        results = []
        
        for i, url in enumerate(urls):
            try:
                result = await self.crawl_url(
                    url=url,
                    max_pages=max_pages,
                    delay=delay,
                    timeout=timeout,
                    selectors=selectors,
                    deep_crawl=deep_crawl,
                    content_filter=content_filter,
                    **kwargs
                )
                results.append(result)
                
                # Add delay between URLs
                if i < len(urls) - 1:
                    await asyncio.sleep(delay)
                    
            except Exception as e:
                logger.error(f"Failed to crawl {url}: {e}")
                results.append({
                    "url": url,
                    "success": False,
                    "error": str(e),
                    "content": "",
                    "metadata": {}
                })
        
        return results
    
    async def _crawl_urls_parallel(
        self,
        urls: List[str],
        max_concurrent: int,
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> List[Dict[str, Any]]:
        """Crawl URLs in parallel with concurrency limit."""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def crawl_with_semaphore(url: str) -> Dict[str, Any]:
            async with semaphore:
                return await self.crawl_url(
                    url=url,
                    max_pages=max_pages,
                    delay=delay,
                    timeout=timeout,
                    selectors=selectors,
                    deep_crawl=deep_crawl,
                    content_filter=content_filter,
                    **kwargs
                )
        
        tasks = [crawl_with_semaphore(url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "url": urls[i],
                    "success": False,
                    "error": str(result),
                    "content": "",
                    "metadata": {}
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _prepare_crawl_params(
        self,
        url: str,
        max_pages: int,
        delay: float,
        timeout: int,
        selectors: Optional[Dict[str, str]],
        deep_crawl: str,
        content_filter: Optional[List[str]],
        **kwargs
    ) -> Dict[str, Any]:
        """Prepare crawling parameters for Crawl4AI."""
        
        params = {
            "url": url,
            "word_count_threshold": 50,
            "fit_markdown": True,
            "bypass_cache": False,
            "include_raw_html": False,
            "wait_for": 2000,  # Wait for dynamic content
        }
        
        # Deep crawling configuration
        if deep_crawl and deep_crawl != "false":
            params.update({
                "crawler_strategy": deep_crawl,
                "max_depth": 2,
                "max_pages": min(max_pages, 50)
            })
        
        # Content selectors
        if selectors:
            content_selectors = []
            for selector_type, selector in selectors.items():
                if selector:
                    content_selectors.append(selector)
            
            if content_selectors:
                params["css_selector"] = ", ".join(content_selectors)
        
        # HVAC-specific content filtering
        hvac_keywords = [
            "air conditioner", "heat pump", "hvac", "cooling", "heating",
            "refrigerant", "btu", "kw", "energy efficiency", "specifications",
            "technical data", "product details", "model", "series"
        ]
        
        params["extraction_strategy"] = "LLMExtractionStrategy"
        params["extraction_schema"] = {
            "type": "object",
            "properties": {
                "equipment_data": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "model": {"type": "string"},
                            "type": {"type": "string"},
                            "specifications": {"type": "object"}
                        }
                    }
                }
            }
        }
        
        # Add custom parameters
        params.update(kwargs)
        
        return params
    
    def _process_crawl_result(
        self,
        result: Any,
        url: str,
        start_time: datetime
    ) -> Dict[str, Any]:
        """Process and normalize crawl result."""
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if hasattr(result, 'success') and result.success:
            content = getattr(result, 'markdown', '') or getattr(result, 'cleaned_html', '')
            
            return {
                "url": url,
                "success": True,
                "content": content,
                "raw_html": getattr(result, 'html', ''),
                "links": getattr(result, 'links', []),
                "images": getattr(result, 'images', []),
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "content_length": len(content),
                    "status_code": getattr(result, 'status_code', 200),
                    "response_headers": getattr(result, 'response_headers', {}),
                    "extracted_content": getattr(result, 'extracted_content', None)
                }
            }
        else:
            error_msg = getattr(result, 'error_message', 'Unknown crawling error')
            
            return {
                "url": url,
                "success": False,
                "error": error_msg,
                "content": "",
                "metadata": {
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "status_code": getattr(result, 'status_code', 0)
                }
            }
