version: '3.8'

services:
  # Krabulon Application
  krabulon:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - SECRET_KEY=${SECRET_KEY:-krabulon-secret-key-change-in-production}
      - DATABASE_HOST=postgres
      - MONGODB_HOST=mongodb
      - NEO4J_HOST=neo4j
      - REDIS_HOST=redis
    depends_on:
      - postgres
      - mongodb
      - neo4j
      - redis
    volumes:
      - ./logs:/app/logs
      - ./config.yaml:/app/config.yaml
    networks:
      - krabulon-network
    restart: unless-stopped

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=hvac_crm
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - krabulon-network
    restart: unless-stopped

  # MongoDB Database
  mongodb:
    image: mongo:7-jammy
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password
      - MONGO_INITDB_DATABASE=hvac_equipment
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    networks:
      - krabulon-network
    restart: unless-stopped

  # Neo4j Graph Database
  neo4j:
    image: neo4j:5-community
    environment:
      - NEO4J_AUTH=neo4j/password
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
    ports:
      - "7474:7474"
      - "7687:7687"
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - krabulon-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - krabulon-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - krabulon-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - krabulon-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    networks:
      - krabulon-network
    restart: unless-stopped
    depends_on:
      - krabulon

volumes:
  postgres_data:
  mongodb_data:
  neo4j_data:
  neo4j_logs:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  krabulon-network:
    driver: bridge
