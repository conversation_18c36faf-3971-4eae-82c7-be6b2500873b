"""Main entry point for Krabulon HVAC Data Enrichment System."""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional
from datetime import datetime

import click
from loguru import logger
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn

# Add the parent directory to the path so we can import krabulon
sys.path.insert(0, str(Path(__file__).parent.parent))

from krabulon.agents import AgentOrchestrator
from krabulon.config import settings
from krabulon.api.server import create_app


console = Console()


def setup_logging():
    """Setup logging configuration."""
    logger.remove()  # Remove default handler
    
    # Add console handler
    logger.add(
        sys.stderr,
        level=settings.logging.get("level", "INFO"),
        format=settings.logging.get("format", "{time} | {level} | {message}"),
        colorize=True
    )
    
    # Add file handler if specified
    log_file = settings.logging.get("file")
    if log_file:
        logger.add(
            log_file,
            level=settings.logging.get("level", "INFO"),
            format=settings.logging.get("format", "{time} | {level} | {message}"),
            rotation=settings.logging.get("rotation", "1 day"),
            retention=settings.logging.get("retention", "30 days")
        )


@click.group()
@click.version_option(version="1.0.0")
def cli():
    """Krabulon - AI-powered HVAC Equipment Data Enrichment System"""
    setup_logging()


@cli.command()
@click.option(
    "--manufacturers", "-m",
    multiple=True,
    help="Manufacturer names to process (can be specified multiple times)"
)
@click.option(
    "--config-file", "-c",
    type=click.Path(exists=True),
    help="Custom configuration file path"
)
@click.option(
    "--output", "-o",
    type=click.Path(),
    help="Output file for results (JSON format)"
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Perform a dry run without saving to database"
)
def enrich(manufacturers: tuple, config_file: Optional[str], output: Optional[str], dry_run: bool):
    """Execute HVAC data enrichment workflow."""
    
    async def run_enrichment():
        orchestrator = AgentOrchestrator()
        
        try:
            # Initialize orchestrator
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                init_task = progress.add_task("Initializing system...", total=None)
                await orchestrator.initialize()
                progress.update(init_task, completed=True)
            
            # Use provided manufacturers or default ones
            if manufacturers:
                mfg_list = list(manufacturers)
            else:
                mfg_list = [mfg["name"] for mfg in settings.manufacturers]
            
            if not mfg_list:
                console.print("[red]No manufacturers specified. Use --manufacturers or configure in config.yaml[/red]")
                return
            
            console.print(f"[green]Starting enrichment for manufacturers: {', '.join(mfg_list)}[/green]")
            
            if dry_run:
                console.print("[yellow]DRY RUN MODE - No data will be saved to database[/yellow]")
            
            # Execute workflow
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                workflow_task = progress.add_task("Executing enrichment workflow...", total=None)
                
                results = await orchestrator.execute_enrichment_workflow(
                    manufacturers=mfg_list,
                    config={"dry_run": dry_run}
                )
                
                progress.update(workflow_task, completed=True)
            
            # Display results
            display_workflow_results(results)
            
            # Save results to file if specified
            if output:
                import json
                with open(output, 'w') as f:
                    json.dump(results, f, indent=2, default=str)
                console.print(f"[green]Results saved to {output}[/green]")
            
        except Exception as e:
            console.print(f"[red]Enrichment failed: {e}[/red]")
            logger.error(f"Enrichment workflow failed: {e}")
            raise
        
        finally:
            await orchestrator.cleanup()
    
    asyncio.run(run_enrichment())


@cli.command()
def status():
    """Show system status and health information."""
    
    async def check_status():
        orchestrator = AgentOrchestrator()
        
        try:
            await orchestrator.initialize()
            status_info = await orchestrator.get_system_status()
            
            display_system_status(status_info)
            
        except Exception as e:
            console.print(f"[red]Failed to get system status: {e}[/red]")
        
        finally:
            await orchestrator.cleanup()
    
    asyncio.run(check_status())


@cli.command()
@click.option("--host", default="0.0.0.0", help="Host to bind to")
@click.option("--port", default=8000, help="Port to bind to")
@click.option("--reload", is_flag=True, help="Enable auto-reload for development")
def serve(host: str, port: int, reload: bool):
    """Start the Krabulon API server."""
    
    import uvicorn
    
    console.print(f"[green]Starting Krabulon API server on {host}:{port}[/green]")
    
    if reload:
        console.print("[yellow]Auto-reload enabled (development mode)[/yellow]")
    
    uvicorn.run(
        "krabulon.api.server:app",
        host=host,
        port=port,
        reload=reload,
        log_level=settings.logging.get("level", "info").lower()
    )


@cli.command()
@click.option("--manufacturer", help="Filter by manufacturer")
@click.option("--type", "equipment_type", help="Filter by equipment type")
@click.option("--limit", default=10, help="Number of results to show")
def list_equipment(manufacturer: Optional[str], equipment_type: Optional[str], limit: int):
    """List HVAC equipment from database."""
    
    async def list_data():
        from krabulon.database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        try:
            await db_manager.initialize()
            
            equipment_list = await db_manager.postgresql.get_equipment(
                manufacturer=manufacturer,
                equipment_type=equipment_type,
                limit=limit
            )
            
            display_equipment_list(equipment_list)
            
        except Exception as e:
            console.print(f"[red]Failed to list equipment: {e}[/red]")
        
        finally:
            await db_manager.cleanup()
    
    asyncio.run(list_data())


@cli.command()
@click.argument("search_term")
@click.option("--limit", default=20, help="Number of results to show")
def search(search_term: str, limit: int):
    """Search HVAC equipment in database."""
    
    async def search_data():
        from krabulon.database import DatabaseManager
        
        db_manager = DatabaseManager()
        
        try:
            await db_manager.initialize()
            
            results = await db_manager.postgresql.search_equipment(search_term, limit)
            
            console.print(f"[green]Found {len(results)} results for '{search_term}'[/green]")
            display_equipment_list(results)
            
        except Exception as e:
            console.print(f"[red]Search failed: {e}[/red]")
        
        finally:
            await db_manager.cleanup()
    
    asyncio.run(search_data())


def display_workflow_results(results: dict):
    """Display workflow results in a formatted table."""
    
    summary = results.get("summary", {})
    
    # Main summary table
    table = Table(title="Workflow Summary", show_header=True, header_style="bold magenta")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Workflow ID", summary.get("workflow_id", "Unknown"))
    table.add_row("Status", summary.get("status", "Unknown"))
    table.add_row("Duration", summary.get("duration_formatted", "Unknown"))
    table.add_row("Manufacturers Processed", str(summary.get("manufacturers_planned", 0)))
    table.add_row("Pages Crawled", str(summary.get("pages_crawled", 0)))
    table.add_row("Equipment Extracted", str(summary.get("equipment_extracted", 0)))
    table.add_row("Equipment Validated", str(summary.get("equipment_validated", 0)))
    table.add_row("Equipment Saved", str(summary.get("equipment_saved", 0)))
    table.add_row("Equipment Updated", str(summary.get("equipment_updated", 0)))
    table.add_row("Extraction Confidence", f"{summary.get('extraction_confidence', 0):.3f}")
    table.add_row("Success Rate", f"{summary.get('crawling_success_rate', 0):.1%}")
    
    console.print(table)
    
    # Error summary if any
    if summary.get("total_errors", 0) > 0:
        console.print(f"\n[yellow]Warning: {summary['total_errors']} errors occurred during workflow[/yellow]")


def display_system_status(status_info: dict):
    """Display system status information."""
    
    # System overview
    table = Table(title="System Status", show_header=True, header_style="bold magenta")
    table.add_column("Component", style="cyan")
    table.add_column("Status", style="green")
    table.add_column("Details")
    
    overall_status = status_info.get("status", "unknown")
    status_color = "green" if overall_status == "operational" else "red"
    
    table.add_row("System", f"[{status_color}]{overall_status.upper()}[/{status_color}]", status_info.get("version", "Unknown"))
    
    # Component status
    components = status_info.get("components", {})
    for component, health in components.items():
        is_healthy = health.get("healthy", False)
        status_text = "[green]HEALTHY[/green]" if is_healthy else "[red]UNHEALTHY[/red]"
        details = health.get("error", "") if not is_healthy else "OK"
        table.add_row(component.title(), status_text, details)
    
    console.print(table)
    
    # Database statistics
    db_stats = status_info.get("database_statistics", {})
    if db_stats:
        console.print("\n[bold]Database Statistics:[/bold]")
        console.print(f"Total Equipment: {db_stats.get('total_equipment', 0)}")
        console.print(f"Average Confidence: {db_stats.get('average_confidence', 0):.3f}")
        console.print(f"Recent Additions (7 days): {db_stats.get('recent_additions', 0)}")


def display_equipment_list(equipment_list: List[dict]):
    """Display equipment list in a formatted table."""
    
    if not equipment_list:
        console.print("[yellow]No equipment found[/yellow]")
        return
    
    table = Table(title="HVAC Equipment", show_header=True, header_style="bold magenta")
    table.add_column("Manufacturer", style="cyan")
    table.add_column("Model", style="green")
    table.add_column("Type", style="yellow")
    table.add_column("Cooling Capacity")
    table.add_column("Energy Class")
    table.add_column("Confidence")
    
    for equipment in equipment_list:
        cooling_capacity = "N/A"
        if equipment.get("cooling_capacity_value") and equipment.get("cooling_capacity_unit"):
            cooling_capacity = f"{equipment['cooling_capacity_value']} {equipment['cooling_capacity_unit']}"
        
        energy_class = equipment.get("energy_class", "N/A")
        confidence = f"{equipment.get('confidence_score', 0):.3f}"
        
        table.add_row(
            equipment.get("manufacturer", "Unknown"),
            equipment.get("model", "Unknown"),
            equipment.get("type", "Unknown"),
            cooling_capacity,
            energy_class,
            confidence
        )
    
    console.print(table)


if __name__ == "__main__":
    cli()
