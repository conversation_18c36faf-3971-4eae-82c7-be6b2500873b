"""Setup script for Krabulon HVAC Data Enrichment System."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="krabulon",
    version="1.0.0",
    author="HVAC CRM Team",
    author_email="<EMAIL>",
    description="AI-powered HVAC Equipment Data Enrichment System",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/hvac-crm/krabulon",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Internet :: WWW/HTTP :: Indexing/Search",
        "Topic :: Database",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "isort>=5.12.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
            "pre-commit>=3.6.0",
        ],
        "monitoring": [
            "prometheus-client>=0.19.0",
            "structlog>=23.2.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "krabulon=krabulon.main:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "krabulon": [
            "config.yaml",
            "*.md",
        ],
    },
    zip_safe=False,
)
