"""Setup script for HVAC Multi-Agent Email Analysis System."""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return true
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def setup_virtual_environment():
    """Set up Python virtual environment."""
    venv_path = Path("venv")
    
    if venv_path.exists():
        print("✅ Virtual environment already exists")
        return True
    
    # Create virtual environment
    if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
        return False
    
    return True

def install_dependencies():
    """Install Python dependencies."""
    # Determine pip path based on OS
    if os.name == 'nt':  # Windows
        pip_path = "venv\\Scripts\\pip"
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        pip_path = "venv/bin/pip"
        python_path = "venv/bin/python"
    
    # Upgrade pip
    if not run_command(f"{python_path} -m pip install --upgrade pip", "Upgrading pip"):
        return False
    
    # Install requirements
    if not run_command(f"{pip_path} install -r requirements.txt", "Installing dependencies"):
        return False
    
    return True

def setup_databases():
    """Set up database connections and create tables."""
    print("🔄 Setting up databases...")
    
    # Check if databases are accessible
    try:
        from .database.sql.models import db_manager
        from .database.nosql.mongodb_client import mongo_manager
        from .database.graph.neo4j_client import neo4j_manager
        
        # PostgreSQL
        try:
            db_manager.create_tables()
            print("✅ PostgreSQL tables created")
        except Exception as e:
            print(f"⚠️ PostgreSQL setup failed: {e}")
        
        # MongoDB
        try:
            if mongo_manager.connect():
                mongo_manager.create_indexes()
                print("✅ MongoDB indexes created")
        except Exception as e:
            print(f"⚠️ MongoDB setup failed: {e}")
        
        # Neo4j
        try:
            if neo4j_manager.connect():
                neo4j_manager.create_constraints_and_indexes()
                print("✅ Neo4j constraints created")
        except Exception as e:
            print(f"⚠️ Neo4j setup failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Database setup failed - dependencies not installed: {e}")
        return False

def create_config_file():
    """Create configuration file from template."""
    config_file = Path(".env")
    template_file = Path(".env.example")
    
    if config_file.exists():
        print("✅ Configuration file already exists")
        return True
    
    if template_file.exists():
        # Copy template to .env
        with open(template_file, 'r') as src, open(config_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Configuration file created from template")
        print("⚠️ Please edit .env file with your actual configuration values")
        return True
    else:
        print("❌ Configuration template not found")
        return False

def run_tests():
    """Run basic tests to verify setup."""
    print("🧪 Running basic tests...")
    
    # Determine python path
    if os.name == 'nt':  # Windows
        python_path = "venv\\Scripts\\python"
    else:  # Unix/Linux/macOS
        python_path = "venv/bin/python"
    
    # Test email parsing
    test_command = f"{python_path} -c \"from python_mixer.email_processing.email_parser import EmailParser; parser = EmailParser(); print('Email parser test: OK')\""
    if not run_command(test_command, "Testing email parser"):
        return False
    
    # Test configuration
    test_command = f"{python_path} -c \"from python_mixer.config import config; print('Configuration test: OK')\""
    if not run_command(test_command, "Testing configuration"):
        return False
    
    print("✅ All basic tests passed")
    return True

def main():
    """Main setup function."""
    print("🚀 Setting up HVAC Multi-Agent Email Analysis System")
    print("=" * 60)
    
    # Step 1: Create virtual environment
    if not setup_virtual_environment():
        print("❌ Setup failed at virtual environment creation")
        return False
    
    # Step 2: Install dependencies
    if not install_dependencies():
        print("❌ Setup failed at dependency installation")
        return False
    
    # Step 3: Create configuration file
    if not create_config_file():
        print("❌ Setup failed at configuration file creation")
        return False
    
    # Step 4: Set up databases (optional, may fail if not configured)
    setup_databases()
    
    # Step 5: Run tests
    if not run_tests():
        print("⚠️ Some tests failed, but setup may still be functional")
    
    print("\n" + "=" * 60)
    print("🎉 Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your configuration")
    print("2. Configure your email accounts and API keys")
    print("3. Set up your databases (PostgreSQL, MongoDB, Neo4j)")
    print("4. Run the system:")
    
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\python -m python_mixer.main --mode status")
    else:  # Unix/Linux/macOS
        print("   venv/bin/python -m python_mixer.main --mode status")
    
    print("\nFor help:")
    if os.name == 'nt':  # Windows
        print("   venv\\Scripts\\python -m python_mixer.main --help")
    else:  # Unix/Linux/macOS
        print("   venv/bin/python -m python_mixer.main --help")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
