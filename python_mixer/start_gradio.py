#!/usr/bin/env python3
"""Startup script for HVAC Gradio Interface."""

import sys
import os
from pathlib import Path

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from gradio_interface import launch_interface
    
    if __name__ == "__main__":
        print("🏠 Starting HVAC Multi-Agent System Interface...")
        print("📱 Interface will be available at: http://localhost:7860")
        print("🔧 Features available:")
        print("   - Email Analysis with AI frameworks")
        print("   - Professional Quote Generation")
        print("   - Equipment Database Management")
        print("   - Krabulon Data Enrichment")
        print("   - System Management & Analytics")
        print("\n🚀 Launching interface...")
        
        launch_interface()
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📦 Installing required packages...")
    
    # Install gradio if not available
    os.system("pip install gradio")
    
    try:
        from gradio_interface import launch_interface
        launch_interface()
    except Exception as e:
        print(f"❌ Failed to start interface: {e}")
        print("🔧 Please check your environment and dependencies")
        
except Exception as e:
    print(f"❌ Error starting interface: {e}")
    print("🔧 Please check the logs for more details")
