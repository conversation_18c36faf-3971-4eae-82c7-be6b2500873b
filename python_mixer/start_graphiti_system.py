#!/usr/bin/env python3
"""
HVAC Graphiti System Startup Script
Orchestrates the complete Graphiti-enhanced HVAC CRM system.
"""

import asyncio
import subprocess
import sys
import os
import time
import signal
import logging
from pathlib import Path
from typing import List, Dict, Any
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class HVACGraphitiOrchestrator:
    """Orchestrates the HVAC Graphiti system components."""
    
    def __init__(self):
        self.processes: Dict[str, subprocess.Popen] = {}
        self.running = False
        
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available."""
        logger.info("🔍 Checking system dependencies...")
        
        required_commands = ['docker', 'docker-compose', 'python']
        missing = []
        
        for cmd in required_commands:
            try:
                subprocess.run([cmd, '--version'], 
                             capture_output=True, check=True)
                logger.info(f"✅ {cmd} is available")
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing.append(cmd)
                logger.error(f"❌ {cmd} is not available")
        
        if missing:
            logger.error(f"Missing dependencies: {missing}")
            return False
        
        return True
    
    def check_environment(self) -> bool:
        """Check environment variables and configuration."""
        logger.info("🔍 Checking environment configuration...")
        
        required_env_vars = [
            'OPENAI_API_KEY',
            'NEO4J_PASSWORD',
            'POSTGRES_PASSWORD'
        ]
        
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.warning(f"⚠️ Missing environment variables: {missing_vars}")
            logger.info("Creating .env template...")
            self.create_env_template()
            return False
        
        logger.info("✅ Environment configuration OK")
        return True
    
    def create_env_template(self):
        """Create environment template file."""
        env_template = """# HVAC Graphiti System Environment Configuration

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
MODEL_CHOICE=gpt-4o-mini

# Neo4j Configuration (for Graphiti)
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti_password

# PostgreSQL Configuration (Remote)
POSTGRES_HOST=**************
POSTGRES_PORT=5432
POSTGRES_DB=hvac_crm
POSTGRES_USER=koldbringer
POSTGRES_PASSWORD=your_postgres_password_here

# MongoDB Configuration (Remote)
MONGODB_HOST=**************
MONGODB_PORT=27017

# MinIO Configuration (Remote)
MINIO_ENDPOINT=**************:9000
MINIO_ACCESS_KEY=koldbringer
MINIO_SECRET_KEY=Blaeritipol1

# Redis Configuration (Local)
REDIS_URL=redis://localhost:6379

# System Configuration
DEBUG=true
LOG_LEVEL=INFO
"""
        
        with open('.env', 'w') as f:
            f.write(env_template)
        
        logger.info("📝 Created .env template file. Please fill in your actual values.")
    
    async def start_docker_services(self):
        """Start Docker services for Neo4j and Redis."""
        logger.info("🐳 Starting Docker services...")
        
        try:
            # Start Docker Compose services
            cmd = ['docker-compose', '-f', 'docker-compose.graphiti.yml', 'up', '-d']
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode != 0:
                logger.error(f"❌ Failed to start Docker services: {result.stderr}")
                return False
            
            logger.info("✅ Docker services started")
            
            # Wait for services to be ready
            await self.wait_for_services()
            return True
            
        except Exception as e:
            logger.error(f"❌ Error starting Docker services: {e}")
            return False
    
    async def wait_for_services(self):
        """Wait for Docker services to be ready."""
        logger.info("⏳ Waiting for services to be ready...")
        
        services = {
            'Neo4j': ('localhost', 7474),
            'Redis': ('localhost', 6379)
        }
        
        for service_name, (host, port) in services.items():
            max_attempts = 30
            for attempt in range(max_attempts):
                try:
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(1)
                    result = sock.connect_ex((host, port))
                    sock.close()
                    
                    if result == 0:
                        logger.info(f"✅ {service_name} is ready")
                        break
                    else:
                        await asyncio.sleep(2)
                        
                except Exception:
                    await asyncio.sleep(2)
            else:
                logger.warning(f"⚠️ {service_name} may not be ready")
    
    async def start_api_server(self):
        """Start the FastAPI server."""
        logger.info("🚀 Starting Graphiti API server...")
        
        try:
            cmd = [
                sys.executable, '-m', 'uvicorn',
                'graphiti_integration.api:app',
                '--host', '0.0.0.0',
                '--port', '8001',
                '--reload'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['api'] = process
            logger.info("✅ API server started on port 8001")
            
            # Wait a moment for startup
            await asyncio.sleep(3)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start API server: {e}")
            return False
    
    async def start_gradio_interface(self):
        """Start the Gradio interface."""
        logger.info("🎨 Starting Gradio interface...")
        
        try:
            cmd = [
                sys.executable, '-c',
                'from graphiti_integration.gradio_graphiti_interface import launch_interface; launch_interface()'
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            self.processes['gradio'] = process
            logger.info("✅ Gradio interface started on port 7860")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to start Gradio interface: {e}")
            return False
    
    async def initialize_knowledge_graph(self):
        """Initialize the knowledge graph with HVAC structure."""
        logger.info("🧠 Initializing HVAC knowledge graph...")
        
        try:
            from graphiti_integration.hvac_graphiti_agent import initialize_graphiti
            from graphiti_integration.email_to_graphiti import initialize_hvac_knowledge_graph
            
            # Initialize Graphiti client
            graphiti = await initialize_graphiti()
            
            # Initialize HVAC knowledge structure
            await initialize_hvac_knowledge_graph(graphiti)
            
            logger.info("✅ Knowledge graph initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize knowledge graph: {e}")
            return False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """Shutdown all services gracefully."""
        logger.info("🛑 Shutting down HVAC Graphiti system...")
        
        self.running = False
        
        # Stop Python processes
        for name, process in self.processes.items():
            try:
                logger.info(f"Stopping {name}...")
                process.terminate()
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                logger.warning(f"Force killing {name}...")
                process.kill()
            except Exception as e:
                logger.error(f"Error stopping {name}: {e}")
        
        # Stop Docker services
        try:
            cmd = ['docker-compose', '-f', 'docker-compose.graphiti.yml', 'down']
            subprocess.run(cmd, timeout=30)
            logger.info("✅ Docker services stopped")
        except Exception as e:
            logger.error(f"Error stopping Docker services: {e}")
        
        logger.info("👋 HVAC Graphiti system shutdown complete")
    
    async def start_system(self):
        """Start the complete HVAC Graphiti system."""
        logger.info("🚀 Starting HVAC Graphiti System...")
        logger.info("=" * 60)
        
        # Check dependencies
        if not self.check_dependencies():
            logger.error("❌ Dependency check failed")
            return False
        
        # Check environment
        if not self.check_environment():
            logger.error("❌ Environment check failed")
            return False
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Start Docker services
        if not await self.start_docker_services():
            logger.error("❌ Failed to start Docker services")
            return False
        
        # Initialize knowledge graph
        if not await self.initialize_knowledge_graph():
            logger.error("❌ Failed to initialize knowledge graph")
            return False
        
        # Start API server
        if not await self.start_api_server():
            logger.error("❌ Failed to start API server")
            return False
        
        # Start Gradio interface
        if not await self.start_gradio_interface():
            logger.error("❌ Failed to start Gradio interface")
            return False
        
        self.running = True
        
        logger.info("=" * 60)
        logger.info("🎉 HVAC Graphiti System is running!")
        logger.info("")
        logger.info("📊 Access points:")
        logger.info("  • Gradio Interface: http://localhost:7860")
        logger.info("  • API Server: http://localhost:8001")
        logger.info("  • Neo4j Browser: http://localhost:7474")
        logger.info("  • API Documentation: http://localhost:8001/docs")
        logger.info("")
        logger.info("Press Ctrl+C to stop the system")
        logger.info("=" * 60)
        
        return True
    
    async def monitor_system(self):
        """Monitor system health and restart failed components."""
        while self.running:
            try:
                # Check process health
                for name, process in list(self.processes.items()):
                    if process.poll() is not None:
                        logger.warning(f"⚠️ Process {name} has stopped")
                        # Could implement restart logic here
                
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Error in system monitor: {e}")
                await asyncio.sleep(5)

async def main():
    """Main function."""
    orchestrator = HVACGraphitiOrchestrator()
    
    try:
        # Start the system
        if await orchestrator.start_system():
            # Monitor the system
            await orchestrator.monitor_system()
        else:
            logger.error("❌ Failed to start system")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
    finally:
        orchestrator.shutdown()

if __name__ == "__main__":
    asyncio.run(main())
