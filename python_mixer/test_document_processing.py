#!/usr/bin/env python3
"""
Test script for HVAC Document Processing System
Tests document extraction, Gemma analysis, and transcription capabilities.
"""

import asyncio
import sys
import os
from pathlib import Path
import tempfile
import json

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

from document_processing import (
    DocumentExtractor,
    GemmaDocumentAnalyzer,
    TranscriptionProcessor,
    AttachmentProcessor
)

async def test_document_extraction():
    """Test document extraction capabilities."""
    print("🔍 Testing Document Extraction...")
    
    extractor = DocumentExtractor()
    
    # Create test PDF content
    test_content = """
    OFERTA SERWISOWA HVAC
    
    Klient: <PERSON>: ul. Testowa 123, Warszawa
    
    Sprzęt do serwisu:
    - LG S12ET Split (montaż 2023-01-15)
    - Daikin FTXS25K Multi-split (naprawa filtra)
    
    Koszt serwisu: 450,00 zł
    Termin: pilny - awaria chłodzenia
    
    Wymagane części:
    - Filtr HEPA
    - Czynnik R32
    """
    
    # Create temporary text file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_path = f.name
    
    try:
        # Test extraction
        result = extractor.extract_from_file(temp_path)
        
        print(f"✅ Extraction successful: {result.metadata.filename}")
        print(f"   Text length: {len(result.text)} characters")
        print(f"   HVAC entities: {len(result.hvac_entities) if result.hvac_entities else 0}")
        print(f"   Confidence: {result.metadata.confidence_score:.2%}")
        
        # Print detected HVAC entities
        if result.hvac_entities:
            print("   Detected equipment:")
            for entity in result.hvac_entities[:3]:
                print(f"     - {entity['value']} ({entity['category']})")
        
        return True
        
    except Exception as e:
        print(f"❌ Document extraction failed: {e}")
        return False
        
    finally:
        # Clean up
        os.unlink(temp_path)

async def test_gemma_analysis():
    """Test Gemma3-4b analysis."""
    print("\n🤖 Testing Gemma3-4b Analysis...")
    
    try:
        analyzer = GemmaDocumentAnalyzer("http://*************:1234")
        
        # Test connection first
        is_connected = await analyzer.test_connection()
        if not is_connected:
            print("⚠️ LM Studio not available, skipping Gemma test")
            return True
        
        # Test analysis
        test_text = """
        Serwis klimatyzacji LG S12ET w Warszawie.
        Klient zgłasza brak chłodzenia. Urządzenie zainstalowane w 2023 roku.
        Wymagana naprawa filtra i uzupełnienie czynnika R32.
        Koszt naprawy: 350 zł. Priorytet: wysoki - awaria w sezonie letnim.
        """
        
        result = await analyzer.analyze_text(test_text)
        
        print(f"✅ Gemma analysis successful")
        print(f"   Summary: {result.summary[:100]}...")
        print(f"   HVAC entities: {len(result.hvac_entities)}")
        print(f"   Urgency: {result.urgency_level}")
        print(f"   Confidence: {result.confidence_score:.2%}")
        print(f"   Processing time: {result.processing_time:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Gemma analysis failed: {e}")
        return False

async def test_transcription():
    """Test transcription capabilities."""
    print("\n🎤 Testing Transcription (Mock)...")
    
    try:
        processor = TranscriptionProcessor("http://localhost:8765")
        
        # Test connection
        is_connected = await processor.test_nemo_service()
        if not is_connected:
            print("⚠️ NeMo service not available, testing with mock data")
            
            # Create mock transcription result
            from document_processing.transcription_processor import TranscriptionResult
            
            mock_result = TranscriptionResult(
                text="Dzień dobry, mam problem z klimatyzacją LG. Urządzenie nie chłodzi pomimo że jest włączone. Proszę o pilny serwis.",
                confidence=0.92,
                language="pl",
                duration=15.5,
                segments=[],
                processing_time=2.1,
                model_used="mock-nemo",
                audio_quality="good",
                hvac_keywords=["klimatyzacja", "LG", "serwis"]
            )
            
            print(f"✅ Mock transcription successful")
            print(f"   Text: {mock_result.text[:80]}...")
            print(f"   Confidence: {mock_result.confidence:.2%}")
            print(f"   Duration: {mock_result.duration}s")
            print(f"   HVAC keywords: {len(mock_result.hvac_keywords)}")
            
            return True
        
        print("✅ NeMo service is available")
        return True
        
    except Exception as e:
        print(f"❌ Transcription test failed: {e}")
        return False

async def test_attachment_processor():
    """Test complete attachment processing."""
    print("\n📎 Testing Complete Attachment Processing...")
    
    try:
        processor = AttachmentProcessor(
            lm_studio_url="http://*************:1234",
            nemo_service_url="http://localhost:8765"
        )
        
        # Test service connections
        service_status = await processor.test_services()
        print(f"   Service status: Gemma={service_status.get('gemma', False)}, NeMo={service_status.get('nemo', False)}")
        
        # Create test document
        test_content = """
        RAPORT SERWISOWY HVAC
        Data: 2024-01-15
        Technik: Marek Nowak
        
        Klient: ABC Sp. z o.o.
        Lokalizacja: Biurowiec Centrum, Warszawa
        
        Wykonane prace:
        1. Przegląd klimatyzacji LG S18ET (sala konferencyjna)
        2. Wymiana filtra w jednostce Daikin FTXS35K (recepcja)
        3. Uzupełnienie czynnika R410A w systemie VRF
        
        Wykryte problemy:
        - Zanieczyszczony filtr w LG S18ET
        - Niski poziom czynnika w systemie VRF
        - Konieczna wymiana czujnika temperatury
        
        Koszty:
        - Filtr HEPA: 120,00 zł
        - Czynnik R410A (2kg): 280,00 zł
        - Czujnik temperatury: 150,00 zł
        - Robocizna: 200,00 zł
        RAZEM: 750,00 zł
        
        Następny przegląd: 2024-07-15
        Priorytet: średni
        """
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_path = f.name
        
        try:
            # Read file data
            with open(temp_path, 'rb') as f:
                file_data = f.read()
            
            # Process attachment
            result = await processor.process_attachment(
                file_data, 
                "test_raport_serwisowy.txt",
                enable_ai_analysis=True,
                enable_transcription=False
            )
            
            print(f"✅ Attachment processing successful")
            print(f"   File: {result.filename}")
            print(f"   Success: {result.success}")
            print(f"   Confidence: {result.confidence_score:.2%}")
            print(f"   Processing time: {result.processing_time:.2f}s")
            print(f"   Service priority: {result.service_priority}")
            print(f"   Equipment detected: {len(result.equipment_detected)}")
            
            if result.estimated_value:
                print(f"   Estimated value: {result.estimated_value:.2f} PLN")
            
            if result.hvac_summary:
                print(f"   HVAC summary: {result.hvac_summary[:100]}...")
            
            # Print equipment details
            if result.equipment_detected:
                print("   Detected equipment:")
                for eq in result.equipment_detected[:3]:
                    print(f"     - {eq.get('value', 'N/A')} ({eq.get('category', 'unknown')})")
            
            return True
            
        finally:
            os.unlink(temp_path)
        
    except Exception as e:
        print(f"❌ Attachment processing failed: {e}")
        return False

async def test_batch_processing():
    """Test batch processing capabilities."""
    print("\n📁 Testing Batch Processing...")
    
    try:
        processor = AttachmentProcessor()
        
        # Create multiple test files
        test_files = [
            ("oferta_lg.txt", "Oferta montażu klimatyzacji LG S12ET. Cena: 2500 zł. Termin: 2 tygodnie."),
            ("awaria_daikin.txt", "PILNE: Awaria klimatyzacji Daikin FTXS25K. Brak chłodzenia. Wymagana natychmiastowa naprawa."),
            ("przegląd_mitsubishi.txt", "Planowany przegląd systemu Mitsubishi Electric VRF. Termin: przyszły miesiąc.")
        ]
        
        # Create temporary files
        temp_files = []
        attachments = []
        
        for filename, content in test_files:
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
                f.write(content)
                temp_files.append(f.name)
                
                # Read file data
                with open(f.name, 'rb') as rf:
                    file_data = rf.read()
                attachments.append((file_data, filename))
        
        try:
            # Process batch
            results = await processor.process_multiple_attachments(
                attachments, 
                max_concurrent=2
            )
            
            print(f"✅ Batch processing successful")
            print(f"   Files processed: {len(results)}")
            
            successful = sum(1 for r in results if r.success)
            print(f"   Successful: {successful}/{len(results)}")
            
            # Show results summary
            for i, result in enumerate(results, 1):
                status = "✅" if result.success else "❌"
                print(f"   {i}. {status} {result.filename} - {result.service_priority} priority")
            
            return True
            
        finally:
            # Clean up temp files
            for temp_file in temp_files:
                os.unlink(temp_file)
        
    except Exception as e:
        print(f"❌ Batch processing failed: {e}")
        return False

async def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🚀 HVAC Document Processing System - Comprehensive Test")
    print("=" * 60)
    
    tests = [
        ("Document Extraction", test_document_extraction),
        ("Gemma3-4b Analysis", test_gemma_analysis),
        ("Transcription", test_transcription),
        ("Attachment Processing", test_attachment_processor),
        ("Batch Processing", test_batch_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! System is ready for production.")
    else:
        print("⚠️ Some tests failed. Check configuration and dependencies.")
    
    return passed == len(results)

if __name__ == "__main__":
    # Run tests
    success = asyncio.run(run_comprehensive_test())
    sys.exit(0 if success else 1)
