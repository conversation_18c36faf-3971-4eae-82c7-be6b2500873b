#!/usr/bin/env python3
"""
Test script for HVAC Graphiti Integration
Verifies that all components work correctly.
"""

import asyncio
import os
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

async def test_graphiti_connection():
    """Test basic Graphiti connection."""
    print("🔍 Testing Graphiti connection...")
    
    try:
        from graphiti_integration import initialize_graphiti
        
        graphiti = await initialize_graphiti()
        print("✅ Graphiti connection successful")
        
        # Test basic search
        results = await graphiti.search("test", limit=1)
        print(f"✅ Search functionality working (found {len(results)} results)")
        
        return graphiti
        
    except Exception as e:
        print(f"❌ Graphiti connection failed: {e}")
        return None

async def test_hvac_agent():
    """Test HVAC agent functionality."""
    print("\n🤖 Testing HVAC Agent...")
    
    try:
        from graphiti_integration import hvac_agent, HVACGraphitiDependencies
        from rich.console import Console
        
        graphiti = await initialize_graphiti()
        if not graphiti:
            print("❌ Cannot test agent without Graphiti connection")
            return False
        
        deps = HVACGraphitiDependencies(
            graphiti_client=graphiti,
            console=Console()
        )
        
        # Test agent query
        test_query = "What is the status of the HVAC knowledge graph?"
        result = await hvac_agent.run(test_query, deps=deps)
        
        print(f"✅ Agent response: {result.data[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ HVAC Agent test failed: {e}")
        return False

async def test_email_processing():
    """Test email to knowledge graph processing."""
    print("\n📧 Testing Email Processing...")
    
    try:
        from graphiti_integration import (
            EmailAnalysisResult, 
            HVACKnowledgeExtractor,
            initialize_graphiti
        )
        
        graphiti = await initialize_graphiti()
        if not graphiti:
            print("❌ Cannot test email processing without Graphiti connection")
            return False
        
        # Create test email
        test_email = EmailAnalysisResult(
            email_id="test_001",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="Test LG S12ET Issue",
            timestamp=datetime.now(timezone.utc),
            content="Hello, my LG S12ET air conditioner is making strange noises. Please help.",
            analysis={"confidence": 0.95},
            entities=[
                {"type": "EQUIPMENT", "value": "LG S12ET"},
                {"type": "PERSON", "value": "test customer"}
            ],
            intent="service_request",
            sentiment="concerned",
            priority="medium"
        )
        
        # Process email
        extractor = HVACKnowledgeExtractor(graphiti)
        episodes = await extractor.process_email_analysis(test_email)
        
        print(f"✅ Email processing successful: {len(episodes)} episodes created")
        return True
        
    except Exception as e:
        print(f"❌ Email processing test failed: {e}")
        return False

async def test_knowledge_search():
    """Test knowledge graph search functionality."""
    print("\n🔍 Testing Knowledge Search...")
    
    try:
        graphiti = await initialize_graphiti()
        if not graphiti:
            print("❌ Cannot test search without Graphiti connection")
            return False
        
        # Test various search queries
        test_queries = [
            "HVAC",
            "equipment",
            "customer",
            "service"
        ]
        
        for query in test_queries:
            results = await graphiti.search(query, limit=5)
            print(f"  Query '{query}': {len(results)} results")
        
        print("✅ Knowledge search tests completed")
        return True
        
    except Exception as e:
        print(f"❌ Knowledge search test failed: {e}")
        return False

def test_environment():
    """Test environment configuration."""
    print("🔧 Testing Environment Configuration...")
    
    required_vars = [
        'OPENAI_API_KEY',
        'NEO4J_URI',
        'NEO4J_USER', 
        'NEO4J_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️ Missing environment variables: {missing_vars}")
        print("Please set these in your .env file")
        return False
    
    print("✅ Environment configuration OK")
    return True

def test_dependencies():
    """Test Python dependencies."""
    print("📦 Testing Dependencies...")
    
    required_packages = [
        'graphiti_core',
        'pydantic_ai',
        'neo4j',
        'fastapi',
        'gradio',
        'rich'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {missing_packages}")
        print("Install with: pip install -r graphiti_integration/requirements_graphiti.txt")
        return False
    
    print("✅ All dependencies available")
    return True

async def run_comprehensive_test():
    """Run comprehensive test suite."""
    print("🚀 HVAC Graphiti Integration Test Suite")
    print("=" * 50)
    
    # Test environment
    if not test_environment():
        print("\n❌ Environment test failed - stopping tests")
        return False
    
    # Test dependencies
    if not test_dependencies():
        print("\n❌ Dependency test failed - stopping tests")
        return False
    
    # Test Graphiti connection
    graphiti = await test_graphiti_connection()
    if not graphiti:
        print("\n❌ Graphiti connection failed - stopping tests")
        return False
    
    # Test HVAC agent
    agent_ok = await test_hvac_agent()
    
    # Test email processing
    email_ok = await test_email_processing()
    
    # Test knowledge search
    search_ok = await test_knowledge_search()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    print(f"  Environment: ✅")
    print(f"  Dependencies: ✅")
    print(f"  Graphiti Connection: ✅")
    print(f"  HVAC Agent: {'✅' if agent_ok else '❌'}")
    print(f"  Email Processing: {'✅' if email_ok else '❌'}")
    print(f"  Knowledge Search: {'✅' if search_ok else '❌'}")
    
    all_passed = agent_ok and email_ok and search_ok
    
    if all_passed:
        print("\n🎉 All tests passed! HVAC Graphiti integration is ready.")
        print("\nNext steps:")
        print("  1. Start the system: python start_graphiti_system.py")
        print("  2. Access Gradio UI: http://localhost:7860")
        print("  3. Access API docs: http://localhost:8001/docs")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
    
    return all_passed

async def quick_demo():
    """Run a quick demonstration of the system."""
    print("\n🎬 Quick Demo - HVAC Graphiti in Action")
    print("-" * 40)
    
    try:
        from graphiti_integration import (
            initialize_graphiti,
            hvac_agent,
            HVACGraphitiDependencies,
            add_hvac_episode
        )
        from rich.console import Console
        
        # Initialize
        graphiti = await initialize_graphiti()
        console = Console()
        deps = HVACGraphitiDependencies(graphiti_client=graphiti, console=console)
        
        # Add sample knowledge
        sample_episode = {
            "type": "equipment_installation",
            "customer": "<EMAIL>",
            "equipment": "LG S12ET",
            "installation_date": "2024-01-15",
            "location": "Warsaw Office",
            "technician": "Jan Kowalski"
        }
        
        await add_hvac_episode(
            graphiti,
            sample_episode,
            "demo_system"
        )
        
        print("✅ Added sample equipment installation to knowledge graph")
        
        # Query the agent
        demo_queries = [
            "What equipment was installed in January 2024?",
            "Show me installations by Jan Kowalski",
            "What LG equipment do we have in the system?"
        ]
        
        for query in demo_queries:
            print(f"\n🤖 Query: {query}")
            result = await hvac_agent.run(query, deps=deps)
            print(f"📝 Response: {result.data[:200]}...")
        
        print("\n🎉 Demo completed successfully!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test HVAC Graphiti Integration")
    parser.add_argument("--demo", action="store_true", help="Run quick demo")
    parser.add_argument("--quick", action="store_true", help="Run quick tests only")
    
    args = parser.parse_args()
    
    if args.demo:
        asyncio.run(quick_demo())
    elif args.quick:
        # Quick tests
        test_environment()
        test_dependencies()
    else:
        # Full test suite
        asyncio.run(run_comprehensive_test())
